<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Pripojiť účet';
$lang['connect_whatsapp_business'] = 'Pripojiť WhatsApp Business';
$lang['campaigning'] = 'Kampaně';
$lang['business_account_id_description'] = 'ID vášho účtu WhatsApp Business (WABA)';
$lang['access_token_description'] = 'Váš užívateľský prístupový token po registrácii na Facebook Developers Portal';
$lang['whatsapp_business_account_id'] = 'ID účtu WhatsApp Business';
$lang['whatsapp_access_token'] = 'Prístupový token WhatsApp';
$lang['webhook_callback_url'] = 'URL pre webhook callback';
$lang['verify_token'] = 'Overovací token';
$lang['connect'] = 'Pripojiť';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Pripojenie účtu jedným kliknutím';
$lang['connect_your_whatsapp_account'] = 'Pripojiť svoj účet WhatsApp';
$lang['copy'] = 'Kopírovať';
$lang['copied'] = 'Skopírované!!';
$lang['disconnect'] = 'Odpojiť';
$lang['number'] = 'Číslo';
$lang['number_id'] = 'ID čísla';
$lang['quality'] = 'Kvalita';
$lang['status'] = 'Stav';
$lang['business_account_id'] = 'ID obchodného účtu';
$lang['permissions'] = 'Oprávnenia';
$lang['phone_number_id_description'] = 'ID telefónneho čísla pripojeného k WhatsApp Business API. Ak si nie ste istí, môžete použiť požiadavku GET Phone Number ID na jeho získanie z WhatsApp API (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'ID čísla registrovaného telefónu WhatsApp';
$lang['update_details'] = 'Aktualizovať detaily';

$lang['bots'] = 'Boty';
$lang['bots_management'] = 'Správa botov';
$lang['create_template_base_bot'] = 'Vytvoriť bot na báze šablóny';
$lang['create_message_bot'] = 'Vytvoriť správu bota';
$lang['type'] = 'Typ';
$lang['message_bot'] = 'Správa bota';
$lang['new_template_bot'] = 'Nový bot na báze šablóny';
$lang['new_message_bot'] = 'Nový správa bota';
$lang['bot_name'] = 'Názov bota';
$lang['reply_text'] = 'Text odpovede <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Text, ktorý bude odoslaný potenciálnemu zákazníkovi alebo kontaktu. Môžete tiež použiť {companyname}, {crm_url} alebo iné vlastné polia pre zlúčenie, alebo použiť znak \'@\' na nájdenie dostupných polí pre zlúčenie" data-placement="bottom"></i> <span class="text-muted">(Maximálny povolený počet znakov by mal byť 1024)</span>';
$lang['reply_type'] = 'Typ odpovede';
$lang['trigger'] = 'Spúšťač';
$lang['header'] = 'Hlavička';
$lang['footer_bot'] = 'Päta <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Možnosť 1: Bot s tlačidlami odpovede';
$lang['bot_with_button_link'] = 'Možnosť 2: Bot s odkazom na tlačidlo - CTA URL';
$lang['button1'] = 'Tlačidlo 1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID tlačidla 1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 256" data-placement="bottom"></i>';
$lang['button2'] = 'Tlačidlo 2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID tlačidla 2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 256" data-placement="bottom"></i>';
$lang['button3'] = 'Tlačidlo 3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID tlačidla 3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Názov tlačidla <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximálny povolený počet znakov by mal byť 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Odkaz na tlačidlo';
$lang['enter_name'] = 'Zadajte názov';
$lang['select_reply_type'] = 'Vyberte typ odpovede';
$lang['enter_bot_reply_trigger'] = 'Zadajte spúšťač odpovede bota';
$lang['enter_header'] = 'Zadajte hlavičku';
$lang['enter_footer'] = 'Zadajte pätu';
$lang['enter_button1'] = 'Zadajte tlačidlo 1';
$lang['enter_button1_id'] = 'Zadajte ID tlačidla 1';
$lang['enter_button2'] = 'Zadajte tlačidlo 2';
$lang['enter_button2_id'] = 'Zadajte ID tlačidla 2';
$lang['enter_button3'] = 'Zadajte tlačidlo 3';
$lang['enter_button3_id'] = 'Zadajte ID tlačidla 3';
$lang['enter_button_name'] = 'Zadajte názov tlačidla';
$lang['enter_button_url'] = 'Zadajte URL tlačidla';
$lang['on_exact_match'] = 'Odpoveď bota: Pri presnej zhode';
$lang['when_message_contains'] = 'Odpoveď bota: Keď správa obsahuje';
$lang['when_client_send_the_first_message'] = 'Privítanie odpovede - keď potenciálny zákazník alebo klient pošle prvú správu';
$lang['bot_create_successfully'] = 'Bot bol úspešne vytvorený';
$lang['bot_update_successfully'] = 'Bot bol úspešne aktualizovaný';
$lang['bot_deleted_successfully'] = 'Bot bol úspešne odstránený';
$lang['templates'] = 'Šablóny';
$lang['template_data_loaded'] = 'Šablóny boli úspešne načítané';
$lang['load_templates'] = 'Načítať šablóny';
$lang['template_management'] = 'Správa šablón';

// campaigns
$lang['campaign'] = 'Kampaň';
$lang['campaigns'] = 'Kampane';
$lang['send_new_campaign'] = 'Odoslať novú kampaň';
$lang['campaign_name'] = 'Názov kampane';
$lang['template'] = 'Šablóna';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Na základe kontaktu podľa časového pásma" data-placement="left"></i Naplánovaný čas odoslania';
$lang['scheduled_time_description'] = 'Na základe kontaktu podľa časového pásma';
$lang['ignore_scheduled_time_and_send_now'] = 'Ignorovať naplánovaný čas a poslať teraz';
$lang['template'] = 'Šablóna';
$lang['leads'] = 'Potenciálni zákazníci';
$lang['delivered_to'] = 'Doručené na';
$lang['read_by'] = 'Prečítané od';
$lang['variables'] = 'Premenné';
$lang['body'] = 'Telo';
$lang['variable'] = 'Premenná';
$lang['match_with_selected_field'] = 'Zhoduje sa s vybratým poľom';
$lang['preview'] = 'Náhľad';
$lang['send_campaign'] = 'Odoslať kampaň';
$lang['send_to'] = 'Odoslať na';
$lang['send_campaign'] = 'Odoslať kampaň';
$lang['view_campaign'] = 'Zobraziť kampaň';
$lang['campaign_daily_task'] = 'Denná úloha kampane';
$lang['back'] = 'Späť';
$lang['phone'] = 'Telefón';
$lang['message'] = 'Správa';
$lang['currently_type_not_supported'] = 'Momentálne <strong> %s </strong> typ šablóny nie je podporovaný!';
$lang['of_your'] = 'z vašich ';
$lang['contacts'] = 'Kontakty';
$lang['select_all_leads'] = 'Vybrať všetkých potenciálnych zákazníkov';
$lang['select_all_note_leads'] = 'Ak to vyberiete, všetci budúci potenciálni zákazníci sú zahrnutí do tejto kampane.';
$lang['select_all_note_contacts'] = 'Ak to vyberiete, všetky budúce kontakty sú zahrnuté do tejto kampane.';

$lang['verified_name'] = 'Overené meno';
$lang['mark_as_default'] = 'Označiť ako predvolené';
$lang['default_number_updated'] = 'Predvolené ID telefónneho čísla bolo úspešne aktualizované';
$lang['currently_using_this_number'] = 'Momentálne používate toto číslo';
$lang['leads'] = 'Potenciálni zákazníci';
$lang['pause_campaign'] = 'Pozastaviť kampaň';
$lang['resume_campaign'] = 'Pokračovať v kampani';
$lang['campaign_resumed'] = 'Kampaň bola obnovená';
$lang['campaign_paused'] = 'Kampaň bola pozastavená';

//Template
$lang['body_data'] = 'Údaje tela';
$lang['category'] = 'Kategória';

// Template bot
$lang['create_new_template_bot'] = 'Vytvoriť nového bota so šablónou';
$lang['template_bot'] = 'Bot so šablónou';
$lang['variables'] = 'Premenné';
$lang['preview'] = 'Náhľad';
$lang['template'] = 'Šablóna';
$lang['bot_content_1'] = 'Táto správa bude odoslaná kontaktu, keď bude splnené pravidlo spúšťania v správe odoslanej kontaktom.';
$lang['save_bot'] = 'Uložiť bota';
$lang['please_select_template'] = 'Vyberte prosím šablónu';
$lang['use_manually_define_value'] = 'Použiť ručne definovanú hodnotu';
$lang['merge_fields'] = 'Spojovacie polia';
$lang['template_bot_create_successfully'] = 'Bot so šablónou bol úspešne vytvorený';
$lang['template_bot_update_successfully'] = 'Bot so šablónou bol úspešne aktualizovaný';
$lang['text_bot'] = 'Textový bot';
$lang['option_2_bot_with_link'] = 'Možnosť 2: Bot s tlačidlom - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Možnosť 3: Bot s súborom';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Správa sa odošle, keď sa očakáva oneskorenie odpovede';
$lang['bot_delay_response_placeholder'] = 'Dajte mi chvíľu, čoskoro budem mať odpoveď';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Typ vzťahu';
$lang['select_all'] = 'Vybrať všetko';
$lang['total'] = 'Celkom';
$lang['merge_field_note'] = 'Použite znak \'@\' na pridanie spájacích polí.';
$lang['send_to_all'] = 'Odoslať všetkým ';
$lang['or'] = 'ALEBO';

$lang['convert_whatsapp_message_to_lead'] = 'Automaticky získať nového potenciálneho zákazníka (previesť nové správy WhatsApp na potenciálneho zákazníka)';
$lang['leads_status'] = 'Stav potenciálnych zákazníkov';
$lang['leads_assigned'] = 'Priradený potenciálny zákazník';
$lang['whatsapp_auto_lead'] = 'WhatsApp Auto Lead';
$lang['webhooks_label'] = 'Prijaté údaje WhatsApp sa znovu odošlú na';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Povoliť opätovné odoslanie WebHooks';
$lang['chat'] = 'Chat';
$lang['black_listed_phone_numbers'] = 'Čísla telefónov na čiernej listine';
$lang['sent_status'] = 'Stav odoslania';

$lang['active'] = 'Aktívne';
$lang['approved'] = 'Schválené';
$lang['this_month'] = 'tento mesiac';
$lang['open_chats'] = 'Otvorené chaty';
$lang['resolved_conversations'] = 'Riešené konverzácie';
$lang['messages_sent'] = 'Odoslané správy';
$lang['account_connected'] = 'Účet pripojený';
$lang['account_disconnected'] = 'Účet odpojený';
$lang['webhook_verify_token'] = 'Webhook overovací token';
// Chat integration
$lang['chat_message_note'] = 'Správa bude čoskoro odoslaná. Vezmite prosím na vedomie, že ak je nový kontakt, nebude sa zobraziť v tomto zozname, kým nezačne interagovať s vami!';

$lang['activity_log'] = 'Záznam aktivít';
$lang['whatsapp_logs'] = 'Záznamy WhatsApp';
$lang['response_code'] = 'Kód odpovede';
$lang['recorded_on'] = 'Zaznamenané dňa';

$lang['request_details'] = 'Podrobnosti požiadavky';
$lang['raw_content'] = 'Nekompromisný obsah';
$lang['headers'] = 'Hlavičky';
$lang['format_type'] = 'Typ formátu';

// Permission section
$lang['show_campaign'] = 'Zobraziť kampaň';
$lang['clear_log'] = 'Vymazať záznam';
$lang['log_activity'] = 'Zaznamenať aktivitu';
$lang['load_template'] = 'Načítať šablónu';

$lang['action'] = 'Akcia';
$lang['total_parameters'] = 'Celkový počet parametrov';
$lang['template_name'] = 'Názov šablóny';
$lang['log_cleared_successfully'] = 'Záznam bol úspešne vymazaný';
$lang['whatsbot_stats'] = 'Štatistiky WhatsBot';

$lang['not_found_or_deleted'] = 'Nenašlo sa alebo bolo odstránené';
$lang['response'] = 'Odpoveď';

$lang['select_image'] = 'Vybrať obrázok';
$lang['image'] = 'Obrázok';
$lang['image_deleted_successfully'] = 'Obrázok bol úspešne vymazaný';
$lang['whatsbot_settings'] = 'Nastavenia Whatsbot';
$lang['maximum_file_size_should_be'] = 'Maximálna veľkosť súboru by mala byť ';
$lang['allowed_file_types'] = 'Povolené typy súborov: ';

$lang['send_image'] = 'Odoslať obrázok';
$lang['send_video'] = 'Odoslať video';
$lang['send_document'] = 'Odoslať dokument';
$lang['record_audio'] = 'Nahrať zvuk';
$lang['chat_media_info'] = 'Viac informácií o podporovaných typoch obsahu a spracovaní veľkosti médií';
$lang['help'] = 'Pomoc';

// v1.1.0
$lang['clone'] = 'Klonovať';
$lang['bot_clone_successfully'] = 'Bot bol úspešne sklonovaný';
$lang['all_chat'] = 'Všetky chaty';
$lang['from'] = 'Od:';
$lang['phone_no'] = 'Telefónne číslo:';
$lang['supportagent'] = 'Podporný agent';
$lang['assign_chat_permission_to_support_agent'] = 'Priradiť povolenie na chat iba podpore';
$lang['enable_whatsapp_notification_sound'] = 'Povoliť zvukové upozornenia na chat WhatsApp';
$lang['notification_sound'] = 'Zvuk upozornenia';
$lang['trigger_keyword'] = 'Spúšťacie kľúčové slovo';
$lang['modal_title'] = 'Vyberte podporného agenta';
$lang['close_btn'] = 'Zavrieť';
$lang['save_btn'] = 'Uložiť';
$lang['support_agent'] = 'Podporný agent';
$lang['change_support_agent'] = 'Zmeniť podporného agenta';
$lang['replay_message'] = 'Nemôžete poslať správu, pretože uplynulo 24 hodín.';
$lang['support_agent_note'] = '<strong>Poznámka:</strong> Ak povolíte funkciu podporného agenta, priradený potenciálny zákazník sa automaticky priradí k chatu. Admini môžu tiež priradiť nového agenta z chatovej stránky.';
$lang['permission_bot_clone'] = 'Klonovať bota';
$lang['remove_chat'] = 'Odstrániť chat';
$lang['default_message_on_no_match'] = 'Predvolená odpoveď - ak žiadne kľúčové slovo nezodpovedá';
$lang['default_message_note'] = '<strong>Poznámka:</strong> Povolenej tejto možnosti zvýši zaťaženie vášho webhooku. Pre viac informácií navštívte tento <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">odkaz</a>.';

$lang['whatsbot_connect_account'] = 'Whatsbot Pripojiť účet';
$lang['whatsbot_message_bot'] = 'Whatsbot Správa Bot';
$lang['whatsbot_template_bot'] = 'Whatsbot Šablóna Bot';
$lang['whatsbot_template'] = 'Whatsbot Šablóna';
$lang['whatsbot_campaigns'] = 'Whatsbot Kampane';
$lang['whatsbot_chat'] = 'Whatsbot Chat';
$lang['whatsbot_log_activity'] = 'Whatsbot Log Aktivita';
$lang['message_templates_not_exists_note'] = 'Meta šablónová oprávnenia chýbajú. Prosím, povolte to vo svojom Meta účte';

// v1.2.0
$lang['ai_prompt'] = 'AI Výzvy';
$lang['ai_prompt_note'] = 'Pre AI výzvy prosím zadajte správu, aby ste aktivovali funkciu, alebo použite AI výzvy, ak sú už povolené';
$lang['emojis'] = 'Emodži';
$lang['translate'] = 'Preložiť';
$lang['change_tone'] = 'Zmeniť tón';
$lang['professional'] = 'Profesionálny';
$lang['friendly'] = 'Priateľský';
$lang['empathetic'] = 'Empatický';
$lang['straightforward'] = 'Jednoduchý';
$lang['simplify_language'] = 'Zjednodušiť jazyk';
$lang['fix_spelling_and_grammar'] = 'Opraviť pravopis a gramatiku';

$lang['ai_integration'] = 'AI Integrácia';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'OpenAI Tajný kľúč - <a href="https://platform.openai.com/account/api-keys" target="_blank">Kde nájdete tajný kľúč?</a>';
$lang['chat_text_limit'] = 'Obmedzenie textu chatu';
$lang['chat_text_limit_note'] = 'Na optimalizáciu prevádzkových nákladov zvážte obmedzenie počtu slov v odpovediach chatu OpenAI';
$lang['chat_model'] = 'Model chatu';
$lang['openai_organizations'] = 'OpenAi Organizácie';
$lang['template_type'] = 'Typ šablóny';
$lang['update'] = 'Aktualizovať';
$lang['open_ai_key_verification_fail'] = 'Overenie OpenAi kľúča čaká na nastavenia, prosím pripojte svoj OpenAI účet';
$lang['enable_wb_openai'] = 'Povoliť OpenAI v chate';
$lang['webhook_resend_method'] = 'Metóda opätovného odoslania webhooku';
$lang['search_language'] = 'Hľadať jazyk...';
$lang['document'] = 'Dokument';
$lang['select_document'] = 'Vybrať dokument';
$lang['attchment_deleted_successfully'] = 'Príloha úspešne odstránená';
$lang['attach_image_video_docs'] = 'Pripojiť obrázky, videá, dokumenty';
$lang['choose_file_type'] = 'Vyberte typ súboru';
$lang['max_size'] = 'Max veľkosť: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Hromadné kampane';
$lang['upload_csv'] = 'Nahrať CSV';
$lang['upload'] = 'Nahrať';
$lang['csv_uploaded_successfully'] = 'CSV súbor bol úspešne nahraný';
$lang['please_select_file'] = 'Prosím, vyberte CSV súbor';
$lang['phonenumber_field_is_required'] = 'Pole s telefónnym číslom je povinné';
$lang['out_of_the'] = 'z';
$lang['records_in_your_csv_file'] = 'záznamov vo vašom CSV súbore,';
$lang['valid_the_campaign_can_be_sent'] = 'záznamy sú platné.<br /> Kampaň môže byť úspešne odoslaná týmto';
$lang['users'] = 'užívateľom';
$lang['campaigns_from_csv_file'] = 'Kampane z CSV súboru';
$lang['download_sample'] = 'Stiahnuť vzor';
$lang['csv_rule_1'] = '1. <b>Požiadavka na stĺpec telefónneho čísla:</b> Váš CSV súbor musí obsahovať stĺpec s názvom "Phoneno." Každý záznam v tomto stĺpci by mal obsahovať platné kontaktné číslo, správne naformátované s kódom krajiny, vrátane znaku "+".<br /><br />';
$lang['csv_rule_2'] = '2. <b>CSV formát a kódovanie:</b> Vaše CSV údaje by mali dodržiavať špecifikovaný formát. Prvý riadok vášho CSV súboru musí obsahovať hlavičky stĺpcov, ako je uvedené v príkladovej tabuľke. Uistite sa, že váš súbor je kódovaný v UTF-8, aby ste predišli problémom s kódovaním.';
$lang['please_upload_valid_csv_file'] = 'Prosím, nahrajte platný CSV súbor';
$lang['please_add_valid_number_in_csv_file'] = 'Prosím, pridajte platné <b>Phoneno</b> v CSV súbore';
$lang['total_send_campaign_list'] = 'Celkový odoslaný kampaň : %s';
$lang['sample_data'] = 'Vzorové údaje';
$lang['firstname'] = 'Krstné meno';
$lang['lastname'] = 'Priezvisko';
$lang['phoneno'] = 'Telefónne číslo';
$lang['email'] = 'Email';
$lang['country'] = 'Krajina';
$lang['download_sample_and_read_rules'] = 'Stiahnuť vzorový súbor a prečítať pravidlá';
$lang['please_wait_your_request_in_process'] = 'Prosím, počkajte, vaša žiadosť je momentálne spracovávaná.';
$lang['whatsbot_bulk_campaign'] = 'Whatsbot Hromadné kampane';
$lang['csv_campaign'] = 'CSV Kampaň';

// Canned reply
$lang['canned_reply'] = 'Pripravená odpoveď';
$lang['canned_reply_menu'] = 'Pripravená odpoveď';
$lang['create_canned_reply'] = 'Vytvoriť pripravenú odpoveď';
$lang['title'] = 'Názov';
$lang['desc'] = 'Popis';
$lang['public'] = 'Verejný';
$lang['action'] = 'Akcia';
$lang['delete_successfully'] = 'Odpoveď bola vymazaná.';
$lang['cannot_delete'] = 'Odpoveď sa nedá vymazať.';
$lang['whatsbot_canned_reply'] = 'Whatsbot Pripravená odpoveď';
$lang['reply'] = 'Odpoveď';

//AI Prompts
$lang['ai_prompts'] = 'AI Výzvy';
$lang['create_ai_prompts'] = 'Vytvoriť AI výzvy';
$lang['name'] = 'Názov';
$lang['action'] = 'Akcia';
$lang['prompt_name'] = 'Názov výzvy';
$lang['prompt_action'] = 'Akcia výzvy';
$lang['whatsbot_ai_prompts'] = 'Whatsbot AI Výzvy';

// new chat
$lang['replying_to'] = 'Odpovedá na:';
$lang['download_document'] = 'Stiahnuť dokument';
$lang['custom_prompt'] = 'Vlastná výzva';
$lang['canned_replies'] = 'Pripravené odpovede';
$lang['use_@_to_add_merge_fields'] = 'Použite \'@\' na pridanie zlúčených polí';
$lang['type_your_message'] = 'Napíšte svoju správu';
$lang['you_cannot_send_a_message_using_this_number'] = 'Nemôžete poslať správu pomocou tohto čísla.';

// bot flow
$lang['bot_flow'] = 'Tok bota';
$lang['create_new_flow'] = 'Vytvoriť nový tok';
$lang['flow_name'] = 'Názov toku';
$lang['flow'] = 'Tok';
$lang['bot_flow_builder'] = 'Nástroj na vytváranie toku bota';
$lang['you_can_not_upload_file_type'] = 'Nemôžete nahrať <b> %s </b> typ súboru';
$lang['whatsbot_bot_flow'] = 'Whatsbot Tok bota';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Automaticky vymazať históriu chatu';
$lang['enable_auto_clear_chat_history'] = 'Povoliť automatické vymazanie histórie chatu';
$lang['auto_clear_time'] = 'Čas automatického vymazania histórie';
$lang['clear_chat_history_note'] = '<strong>Poznámka:</strong> Ak povolíte funkciu automatického vymazania histórie chatu, automaticky vymaže históriu chatu na základe počtu dní, ktoré určíte, vždy, keď sa spustí cron úloha.';
$lang['source'] = 'Zdroj';
$lang['groups'] = 'Skupiny';


// v1.3.3
$lang['click_user_to_chat'] = 'Kliknite na používateľa, aby ste mohli chatovať';
$lang['searching'] = 'Vyhľadávanie...';
$lang['filters'] = 'Filtre';
$lang['relation_type'] = 'Typ vzťahu';
$lang['groups'] = 'Skupiny';
$lang['source'] = 'Zdroj';
$lang['status'] = 'Stav';
$lang['select_type'] = 'Vyberte typ';
$lang['select_agents'] = 'Vyberte agentov';
$lang['select_group'] = 'Vyberte skupinu';
$lang['select_source'] = 'Vyberte zdroj';
$lang['select_status'] = 'Vyberte stav';
$lang['agents'] = 'Agenti';

// v1.4.2
$lang['read_only'] = 'Iba na čítanie';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
