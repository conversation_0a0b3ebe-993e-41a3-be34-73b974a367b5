<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Account Verbinden';
$lang['connect_whatsapp_business'] = 'Verbind Whatsapp Business';
$lang['campaigning'] = 'Campagnevoeren';
$lang['business_account_id_description'] = 'Je WhatsApp Business Account (WABA) ID';
$lang['access_token_description'] = 'Je Gebruikerstoegangstoken na aanmelding bij het Facebook Developers Portal';
$lang['whatsapp_business_account_id'] = 'Whatsapp Business Account ID';
$lang['whatsapp_access_token'] = 'Whatsapp Toegangstoken';
$lang['webhook_callback_url'] = 'Webhook Callback URL';
$lang['verify_token'] = 'Verifieer Token';
$lang['connect'] = 'Verbinden';
$lang['whatsapp'] = 'Whatsapp';
$lang['one_click_account_connection'] = 'Een Klik Account Verbinding';
$lang['connect_your_whatsapp_account'] = 'Verbind Je Whatsapp Account';
$lang['copy'] = 'Kopieer';
$lang['copied'] = 'Gekopieerd!!';
$lang['disconnect'] = 'Ontkoppelen';
$lang['number'] = 'Nummer';
$lang['number_id'] = 'Nummer ID';
$lang['quality'] = 'Kwaliteit';
$lang['status'] = 'Status';
$lang['business_account_id'] = 'Business Account ID';
$lang['permissions'] = 'Toestemmingen';
$lang['phone_number_id_description'] = 'ID van het telefoonnummer dat is verbonden met de WhatsApp Business API. Als je hier niet zeker van bent, kun je een GET Telefoonnummer ID-aanroep gebruiken om het op te halen uit de WhatsApp API (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'Nummer ID van het WhatsApp Geregistreerde Telefoon';
$lang['update_details'] = 'Details Bijwerken';

$lang['bots'] = 'Bots';
$lang['bots_management'] = 'Bots Beheer';
$lang['create_template_base_bot'] = 'Maak sjabloon basis bot';
$lang['create_message_bot'] = 'Maak bericht bot';
$lang['type'] = 'Type';
$lang['message_bot'] = 'Bericht Bot';
$lang['new_template_bot'] = 'Nieuwe Sjabloon Bot';
$lang['new_message_bot'] = 'Nieuwe Bericht Bot';
$lang['bot_name'] = 'Bot Naam';
$lang['reply_text'] = 'Antwoordtekst <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Tekst die naar de lead of contact zal worden verzonden. Je kunt ook {companyname}, {crm_url} of andere aangepaste samenvoegvelden van lead of contact gebruiken, of het \'@\' teken gebruiken om beschikbare samenvoegvelden te vinden" data-placement="bottom"></i> <span class="text-muted">(Maximaal toegestane tekens is 1024)</span>';
$lang['reply_type'] = 'Antwoordtype';
$lang['trigger'] = 'Trigger';
$lang['header'] = 'Header';
$lang['footer_bot'] = 'Footer <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Optie 1: Bot met antwoordknoppen';
$lang['bot_with_button_link'] = 'Optie 2: Bot met knoplink - CTA URL';
$lang['button1'] = 'Knop1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'Knop1 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 256" data-placement="bottom"></i>';
$lang['button2'] = 'Knop2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'Knop2 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 256" data-placement="bottom"></i>';
$lang['button3'] = 'Knop3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'Knop3 ID <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Knop Naam <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximaal toegestane tekens is 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Knoplink';
$lang['enter_name'] = 'Voer Naam In';
$lang['select_reply_type'] = 'Selecteer antwoordtype';
$lang['enter_bot_reply_trigger'] = 'Voer bot antwoord trigger in';
$lang['enter_header'] = 'Voer header in';
$lang['enter_footer'] = 'Voer footer in';
$lang['enter_button1'] = 'Voer knop1 in';
$lang['enter_button1_id'] = 'Voer knop1 ID in';
$lang['enter_button2'] = 'Voer knop2 in';
$lang['enter_button2_id'] = 'Voer knop2 ID in';
$lang['enter_button3'] = 'Voer knop3 in';
$lang['enter_button3_id'] = 'Voer knop3 ID in';
$lang['enter_button_name'] = 'Voer knop naam in';
$lang['enter_button_url'] = 'Voer knop URL in';
$lang['on_exact_match'] = 'Antwoord bot: Bij exacte overeenkomst';
$lang['when_message_contains'] = 'Antwoord bot: Wanneer bericht bevat';
$lang['when_client_send_the_first_message'] = 'Welkomstantwoord - wanneer lead of klant het eerste bericht verzendt';
$lang['bot_create_successfully'] = 'Bot succesvol gemaakt';
$lang['bot_update_successfully'] = 'Bot succesvol bijgewerkt';
$lang['bot_deleted_successfully'] = 'Bot succesvol verwijderd';
$lang['templates'] = 'Sjablonen';
$lang['template_data_loaded'] = 'Sjablonen succesvol geladen';
$lang['load_templates'] = 'Laad Sjablonen';
$lang['template_management'] = 'Sjabloonbeheer';


$lang['campaign'] = 'Campagne';
$lang['campaigns'] = 'Campagnes';
$lang['send_new_campaign'] = 'Stuur Nieuwe Campagne';
$lang['campaign_name'] = 'Campagnenaam';
$lang['template'] = 'Sjabloon';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Per klant, op basis van de tijdzone van de contactpersoon" data-placement="left"></i>Geplande verzendtijd';
$lang['scheduled_time_description'] = 'Per klant, op basis van de tijdzone van de contactpersoon';
$lang['ignore_scheduled_time_and_send_now'] = 'Negeer geplande tijd en verzend nu';
$lang['template'] = 'Sjabloon';
$lang['leads'] = 'Leads';
$lang['delivered_to'] = 'Geleverd aan';
$lang['read_by'] = 'Gelezen door';
$lang['variables'] = 'Variabelen';
$lang['body'] = 'Lichaam';
$lang['variable'] = 'Variabele';
$lang['match_with_selected_field'] = 'Overeenkomen met een geselecteerd veld';
$lang['preview'] = 'Voorbeeld';
$lang['send_campaign'] = 'Verzend campagne';
$lang['send_to'] = 'Verzend naar';
$lang['send_campaign'] = 'Verzend Campagne';
$lang['view_campaign'] = 'Bekijk Campagne';
$lang['campaign_daily_task'] = 'Dagelijkse taak van de campagne';
$lang['back'] = 'Terug';
$lang['phone'] = 'Telefoon';
$lang['message'] = 'Bericht';
$lang['currently_type_not_supported'] = 'Momenteel is <strong> %s </strong> sjabloontype niet ondersteund!';
$lang['of_your'] = 'van jouw ';
$lang['contacts'] = 'Contacten';
$lang['select_all_leads'] = 'Selecteer alle Leads';
$lang['select_all_note_leads'] = 'Als je dit selecteert, worden alle toekomstige leads in deze campagne opgenomen.';
$lang['select_all_note_contacts'] = 'Als je dit selecteert, worden alle toekomstige contacten in deze campagne opgenomen.';

$lang['verified_name'] = 'Geverifieerde Naam';
$lang['mark_as_default'] = 'Markeer als standaard';
$lang['default_number_updated'] = 'Standaard telefoonnummer is succesvol bijgewerkt';
$lang['currently_using_this_number'] = 'Momenteel dit nummer gebruiken';
$lang['leads'] = 'Leads';
$lang['pause_campaign'] = 'Pauzeer Campagne';
$lang['resume_campaign'] = 'Hervat Campagne';
$lang['campaign_resumed'] = 'Campagne hervat';
$lang['campaign_paused'] = 'Campagne gepauzeerd';

// Template
$lang['body_data'] = 'Lichaamsgegevens';
$lang['category'] = 'Categorie';

// Template bot
$lang['create_new_template_bot'] = 'Maak nieuwe sjabloonbot';
$lang['template_bot'] = 'Sjabloon Bot';
$lang['variables'] = 'Variabelen';
$lang['preview'] = 'Voorbeeld';
$lang['template'] = 'Sjabloon';
$lang['bot_content_1'] = 'Dit bericht wordt naar de contactpersoon gestuurd zodra de triggerregel is vervuld in het door de contactpersoon verzonden bericht.';
$lang['save_bot'] = 'Sla bot op';
$lang['please_select_template'] = 'Selecteer een sjabloon';
$lang['use_manually_define_value'] = 'Gebruik handmatig gedefinieerde waarde';
$lang['merge_fields'] = 'Samenvoegvelden';
$lang['template_bot_create_successfully'] = 'Sjabloonbot succesvol aangemaakt';
$lang['template_bot_update_successfully'] = 'Sjabloonbot succesvol bijgewerkt';
$lang['text_bot'] = 'Tekstbot';
$lang['option_2_bot_with_link'] = 'Optie 2: Bot met knoplink - Oproep tot Actie (CTA) URL';
$lang['option_3_file'] = 'Optie 3: Bot met bestand';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Bericht verzenden wanneer vertraging in reactie wordt verwacht';
$lang['bot_delay_response_placeholder'] = 'Geef me een moment, ik heb het antwoord snel.';

$lang['whatsbot'] = 'WhatsBot';

// campagnes
$lang['relation_type'] = 'Relatietype';
$lang['select_all'] = 'Selecteer alles';
$lang['total'] = 'Totaal';
$lang['merge_field_note'] = 'Gebruik het \'@\' teken om samenvoegvelden toe te voegen.';
$lang['send_to_all'] = 'Verzend naar allemaal ';
$lang['or'] = 'OF';

$lang['convert_whatsapp_message_to_lead'] = 'Acquireer automatisch nieuwe lead (converteer nieuwe WhatsApp-berichten naar lead)';
$lang['leads_status'] = 'Leadstatus';
$lang['leads_assigned'] = 'Lead toegewezen';
$lang['whatsapp_auto_lead'] = 'WhatsApp Auto Lead';
$lang['webhooks_label'] = 'WhatsApp ontvangen gegevens worden opnieuw verzonden naar';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Schakel WebHooks opnieuw verzenden in';
$lang['chat'] = 'Chat';
$lang['black_listed_phone_numbers'] = 'Zwarte lijst telefoon nummers';
$lang['sent_status'] = 'Verzendstatus';

$lang['active'] = 'Actief';
$lang['approved'] = 'Goedgekeurd';
$lang['this_month'] = 'deze maand';
$lang['open_chats'] = 'Open Chats';
$lang['resolved_conversations'] = 'Opgeloste Gesprekken';
$lang['messages_sent'] = 'Berichten verzonden';
$lang['account_connected'] = 'Account verbonden';
$lang['account_disconnected'] = 'Account losgekoppeld';
$lang['webhook_verify_token'] = 'Webhook verificatietoken';
// Chat integratie
$lang['chat_message_note'] = 'Bericht wordt binnenkort verzonden. Houd er rekening mee dat als het een nieuw contact is, het niet in deze lijst verschijnt totdat het contact begint te communiceren met jou!';

$lang['activity_log'] = 'Activiteitenlog';
$lang['whatsapp_logs'] = 'WhatsApp Logs';
$lang['response_code'] = 'Responscode';
$lang['recorded_on'] = 'Opgenomen op';

$lang['request_details'] = 'Verzoekdetails';
$lang['raw_content'] = 'Ruwe inhoud';
$lang['headers'] = 'Kopteksten';
$lang['format_type'] = 'Formaat type';

// Permissie sectie
$lang['show_campaign'] = 'Toon campagne';
$lang['clear_log'] = 'Wis log';
$lang['log_activity'] = 'Log Activiteit';
$lang['load_template'] = 'Laad sjabloon';

$lang['action'] = 'Actie';
$lang['total_parameters'] = 'Totaal aantal parameters';
$lang['template_name'] = 'Sjabloonnaam';
$lang['log_cleared_successfully'] = 'Log succesvol gewist';
$lang['whatsbot_stats'] = 'WhatsBot Statistieken';

$lang['not_found_or_deleted'] = 'Niet gevonden of verwijderd';
$lang['response'] = 'Respons';

$lang['select_image'] = 'Selecteer afbeelding';
$lang['image'] = 'Afbeelding';
$lang['image_deleted_successfully'] = 'Afbeelding succesvol verwijderd';
$lang['whatsbot_settings'] = 'Whatsbot Instellingen';
$lang['maximum_file_size_should_be'] = 'Maximale bestandsgrootte moet zijn ';
$lang['allowed_file_types'] = 'Toegestane bestandstypen : ';

$lang['send_image'] = 'Verstuur afbeelding';
$lang['send_video'] = 'Verstuur video';
$lang['send_document'] = 'Verstuur document';
$lang['record_audio'] = 'Neem audio op';
$lang['chat_media_info'] = 'Meer informatie over ondersteunde contenttypes en post-processing media grootte';
$lang['help'] = 'Help';

// v1.1.0
$lang['clone'] = 'Kloon';
$lang['bot_clone_successfully'] = 'Bot succesvol gekloond';
$lang['all_chat'] = 'Alle chats';
$lang['from'] = 'Van:';
$lang['phone_no'] = 'Telefoonnummer:';
$lang['supportagent'] = 'Ondersteuningsagent';
$lang['assign_chat_permission_to_support_agent'] = 'Toekennen van chatmachtiging aan ondersteuningsagent alleen';
$lang['enable_whatsapp_notification_sound'] = 'Schakel WhatsApp-chatnotificatiegeluid in';
$lang['notification_sound'] = 'Notificatiegeluid';
$lang['trigger_keyword'] = 'Trigger sleutelwoord';
$lang['modal_title'] = 'Selecteer ondersteuningsagent';
$lang['close_btn'] = 'Sluiten';
$lang['save_btn'] = 'Opslaan';
$lang['support_agent'] = 'Ondersteuningsagent';
$lang['change_support_agent'] = 'Verander ondersteuningsagent';
$lang['replay_message'] = 'Je kunt geen bericht verzenden, 24 uur is verstreken.';
$lang['support_agent_note'] = '<strong>Opmerking: </strong>Wanneer je de ondersteuningsagentfunctie inschakelt, wordt de leadtoewijzing automatisch aan de chat toegewezen. Beheerders kunnen ook een nieuwe agent toewijzen vanaf de chatpagina.';
$lang['permission_bot_clone'] = 'Kloon Bot';
$lang['remove_chat'] = 'Verwijder chat';
$lang['default_message_on_no_match'] = 'Standaard antwoord - als er geen sleutelwoord overeenkomt';
$lang['default_message_note'] = '<strong>Opmerking: </strong>Het inschakelen van deze optie zal je webhook-lading verhogen. Voor meer informatie, bezoek deze <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">link</a>.';

$lang['whatsbot_connect_account'] = 'Whatsbot Koppel Account';
$lang['whatsbot_message_bot'] = 'Whatsbot Bericht Bot';
$lang['whatsbot_template_bot'] = 'Whatsbot Sjabloon Bot';
$lang['whatsbot_template'] = 'Whatsbot Sjabloon';
$lang['whatsbot_campaigns'] = 'Whatsbot Campagnes';
$lang['whatsbot_chat'] = 'Whatsbot Chat';
$lang['whatsbot_log_activity'] = 'Whatsbot Log Activiteit';
$lang['message_templates_not_exists_note'] = 'Meta sjabloonmachtiging ontbreekt. Schakel het in je Meta-account in.';

// v1.2.0
$lang['ai_prompt'] = 'AI Prompt';
$lang['ai_prompt_note'] = 'Voor AI-prompt, voer een bericht in om de functie in te schakelen, of gebruik AI-prompt als deze al is ingeschakeld';
$lang['emojis'] = 'Emoji\'s';
$lang['translate'] = 'Vertalen';
$lang['change_tone'] = 'Verander toon';
$lang['professional'] = 'Professioneel';
$lang['friendly'] = 'Vriendelijk';
$lang['empathetic'] = 'Empathisch';
$lang['straightforward'] = 'Eerlijk';
$lang['simplify_language'] = 'Vereenvoudig taal';
$lang['fix_spelling_and_grammar'] = 'Corrigeer spelling en grammatica';

$lang['ai_integration'] = 'AI-integratie';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'OpenAI Geheime Sleutel - <a href="https://platform.openai.com/account/api-keys" target="_blank">Waar vind je de geheime sleutel?</a>';
$lang['chat_text_limit'] = 'Chat Tekst Limiet';
$lang['chat_text_limit_note'] = 'Om operationele kosten te optimaliseren, overweeg om het aantal woorden van OpenAI\'s chatantwoorden te beperken';
$lang['chat_model'] = 'Chatmodel';
$lang['openai_organizations'] = 'OpenAi Organisaties';
$lang['template_type'] = 'Sjabloon Type';
$lang['update'] = 'Bijwerken';
$lang['open_ai_key_verification_fail'] = 'OpenAi Sleutelverificatie is in afwachting van instellingen, verbind je OpenAI-account';
$lang['enable_wb_openai'] = 'Schakel OpenAI in chat in';
$lang['webhook_resend_method'] = 'Webhook Herzendmethode';
$lang['search_language'] = 'Zoektaal...';
$lang['document'] = 'Document';
$lang['select_document'] = 'Selecteer document';
$lang['attchment_deleted_successfully'] = 'Bijlage succesvol verwijderd';
$lang['attach_image_video_docs'] = 'Voeg afbeelding video documenten toe';
$lang['choose_file_type'] = 'Kies bestandstype';
$lang['max_size'] = 'Maximale grootte: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Bulk Campagnes';
$lang['upload_csv'] = 'Upload CSV';
$lang['upload'] = 'Uploaden';
$lang['csv_uploaded_successfully'] = 'CSV-bestand succesvol geüpload';
$lang['please_select_file'] = 'Selecteer alstublieft CSV-bestand';
$lang['phonenumber_field_is_required'] = 'Telefoonnummerveld is vereist';
$lang['out_of_the'] = 'Van de';
$lang['records_in_your_csv_file'] = 'records in je CSV-bestand,';
$lang['valid_the_campaign_can_be_sent'] = 'records zijn geldig.<br /> De campagne kan succesvol naar deze';
$lang['users'] = 'gebruikers';
$lang['campaigns_from_csv_file'] = 'Campagnes vanuit CSV-bestand';
$lang['download_sample'] = 'Download Voorbeeld';
$lang['csv_rule_1'] = '1. <b>Vereiste kolom voor telefoonnummer:</b> Je CSV-bestand moet een kolom met de naam "Phoneno" bevatten. Elke record in deze kolom moet een geldig contactnummer bevatten, correct geformatteerd met de landcode, inclusief het "+"-teken. <br /><br />';
$lang['csv_rule_2'] = '2. <b>CSV-indeling en codering:</b> Je CSV-gegevens moeten de gespecificeerde indeling volgen. De eerste rij van je CSV-bestand moet de kolomkoppen bevatten, zoals weergegeven in de voorbeeldtabel. Zorg ervoor dat je bestand is gecodeerd in UTF-8 om coderingproblemen te voorkomen.';
$lang['please_upload_valid_csv_file'] = 'Upload alstublieft een geldig CSV-bestand';
$lang['please_add_valid_number_in_csv_file'] = 'Voeg alstublieft een geldig <b>Phoneno</b> toe in CSV-bestand';
$lang['total_send_campaign_list'] = 'Totaal verzonden campagne: %s';
$lang['sample_data'] = 'Voorbeeldgegevens';
$lang['firstname'] = 'Voornaam';
$lang['lastname'] = 'Achternaam';
$lang['phoneno'] = 'Telefoonnummer';
$lang['email'] = 'E-mail';
$lang['country'] = 'Land';
$lang['download_sample_and_read_rules'] = 'Download voorbeeldbestand en lees regels';
$lang['please_wait_your_request_in_process'] = 'Even geduld, uw aanvraag wordt momenteel verwerkt.';
$lang['whatsbot_bulk_campaign'] = 'Whatsbot Bulk Campagnes';
$lang['csv_campaign'] = 'CSV Campagne';

// Canned reply
$lang['canned_reply'] = 'Canned Reply';
$lang['canned_reply_menu'] = 'Canned Reply';
$lang['create_canned_reply'] = 'Maak Canned Reply';
$lang['title'] = 'Titel';
$lang['desc'] = 'Beschrijving';
$lang['public'] = 'Publiek';
$lang['action'] = 'Actie';
$lang['delete_successfully'] = 'Antwoord verwijderd.';
$lang['cannot_delete'] = 'Antwoord kan niet worden verwijderd.';
$lang['whatsbot_canned_reply'] = 'Whatsbot Canned Reply';
$lang['reply'] = 'Antwoord';

//AI Prompts
$lang['ai_prompts'] = 'AI Prompts';
$lang['create_ai_prompts'] = 'Maak AI Prompts';
$lang['name'] = 'Naam';
$lang['action'] = 'Actie';
$lang['prompt_name'] = 'Promptnaam';
$lang['prompt_action'] = 'Promptactie';
$lang['whatsbot_ai_prompts'] = 'Whatsbot AI Prompts';

// nieuwe chat
$lang['replying_to'] = 'Beantwoorden aan:';
$lang['download_document'] = 'Download Document';
$lang['custom_prompt'] = 'Aangepaste Prompt';
$lang['canned_replies'] = 'Canned Antwoorden';
$lang['use_@_to_add_merge_fields'] = 'Gebruik \'@\' om samenvoegvelden toe te voegen';
$lang['type_your_message'] = 'Typ je bericht';
$lang['you_cannot_send_a_message_using_this_number'] = 'Je kunt geen bericht verzenden met dit nummer.';

// bot flow
$lang['bot_flow'] = 'Bot Flow';
$lang['create_new_flow'] = 'Maak Nieuwe Flow';
$lang['flow_name'] = 'Flow Naam';
$lang['flow'] = 'Flow';
$lang['bot_flow_builder'] = 'Bot Flow Builder';
$lang['you_can_not_upload_file_type'] = 'Je kunt geen <b> %s </b> type bestand uploaden';
$lang['whatsbot_bot_flow'] = 'Whatsbot Bot Flow';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Auto Verwijder Chatgeschiedenis';
$lang['enable_auto_clear_chat_history'] = 'Schakel Auto Verwijder Chatgeschiedenis in';
$lang['auto_clear_time'] = 'Auto Verwijder Tijd';
$lang['clear_chat_history_note'] = '<strong>Opmerking: </strong> Als je de auto-verwijder chatgeschiedenisfunctie inschakelt, wordt de chatgeschiedenis automatisch verwijderd op basis van het aantal dagen dat je opgeeft, telkens wanneer de cron-taak draait.';
$lang['source'] = 'Bron';
$lang['groups'] = 'Groepen';

// v1.3.3
$lang['click_user_to_chat'] = 'Klik op gebruiker om te chatten';
$lang['searching'] = 'Zoeken...';
$lang['filters'] = 'Filters';
$lang['relation_type'] = 'Relatietype';
$lang['groups'] = 'Groepen';
$lang['source'] = 'Bron';
$lang['status'] = 'Status';
$lang['select_type'] = 'Selecteer type';
$lang['select_agents'] = 'Selecteer agenten';
$lang['select_group'] = 'Selecteer groep';
$lang['select_source'] = 'Selecteer bron';
$lang['select_status'] = 'Selecteer status';
$lang['agents'] = 'Agenten';

// v1.4.2
$lang['read_only'] = 'Alleen lezen';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
