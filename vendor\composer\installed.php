<?php return array(
    'root' => array(
        'name' => '__root__',
        'pretty_version' => '2.0.2.x-dev',
        'version' => '2.0.2.9999999-dev',
        'reference' => '6b14df7216d2b7559473092914057ba37de41e3f',
        'type' => 'library',
        'install_path' => __DIR__ . '/../../',
        'aliases' => array(),
        'dev' => true,
    ),
    'versions' => array(
        '__root__' => array(
            'pretty_version' => '2.0.2.x-dev',
            'version' => '2.0.2.9999999-dev',
            'reference' => '6b14df7216d2b7559473092914057ba37de41e3f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../../',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'bacon/bacon-qr-code' => array(
            'pretty_version' => '2.0.8',
            'version' => '2.0.8.0',
            'reference' => '8674e51bb65af933a5ffaf1c308a660387c35c22',
            'type' => 'library',
            'install_path' => __DIR__ . '/../bacon/bacon-qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'corbital/rightful' => array(
            'pretty_version' => 'dev-master',
            'version' => 'dev-master',
            'reference' => '7de2a428a799062a19f6eeca7094a0a568e28851',
            'type' => 'library',
            'install_path' => __DIR__ . '/../corbital/rightful',
            'aliases' => array(
                0 => '9999999-dev',
            ),
            'dev_requirement' => false,
        ),
        'dasprid/enum' => array(
            'pretty_version' => '1.0.6',
            'version' => '1.0.6.0',
            'reference' => '8dfd07c6d2cf31c8da90c53b83c026c7696dda90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../dasprid/enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'endroid/qr-code' => array(
            'pretty_version' => '5.0.0',
            'version' => '5.0.0.0',
            'reference' => '1f65da38fc54483690bd20908aadcbdcf66c482e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../endroid/qr-code',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'graham-campbell/result-type' => array(
            'pretty_version' => 'v1.1.3',
            'version' => '1.1.3.0',
            'reference' => '3ba905c11371512af9d9bdd27d99b782216b6945',
            'type' => 'library',
            'install_path' => __DIR__ . '/../graham-campbell/result-type',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/guzzle' => array(
            'pretty_version' => '7.9.2',
            'version' => '7.9.2.0',
            'reference' => 'd281ed313b989f213357e3be1a179f02196ac99b',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/guzzle',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/promises' => array(
            'pretty_version' => '2.0.3',
            'version' => '2.0.3.0',
            'reference' => '6ea8dd08867a2a42619d65c3deb2c0fcbf81c8f8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/promises',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'guzzlehttp/psr7' => array(
            'pretty_version' => '2.7.0',
            'version' => '2.7.0.0',
            'reference' => 'a70f5c95fb43bc83f07c9c948baa0dc1829bf201',
            'type' => 'library',
            'install_path' => __DIR__ . '/../guzzlehttp/psr7',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'league/csv' => array(
            'pretty_version' => '9.16.0',
            'version' => '9.16.0.0',
            'reference' => '998280c6c34bd67d8125fdc8b45bae28d761b440',
            'type' => 'library',
            'install_path' => __DIR__ . '/../league/csv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'myclabs/php-enum' => array(
            'pretty_version' => '1.8.4',
            'version' => '1.8.4.0',
            'reference' => 'a867478eae49c9f59ece437ae7f9506bfaa27483',
            'type' => 'library',
            'install_path' => __DIR__ . '/../myclabs/php-enum',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'netflie/whatsapp-cloud-api' => array(
            'pretty_version' => '2.2.1',
            'version' => '2.2.1.0',
            'reference' => '3c3aea95cac49658eef8c1e361af35fd0567427c',
            'type' => 'library',
            'install_path' => __DIR__ . '/../netflie/whatsapp-cloud-api',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'openai-php/client' => array(
            'pretty_version' => 'v0.10.2',
            'version' => '0.10.2.0',
            'reference' => 'efa92628ba9fb56f7877c0d616f5221c4a447856',
            'type' => 'library',
            'install_path' => __DIR__ . '/../openai-php/client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/async-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '*',
            ),
        ),
        'php-http/discovery' => array(
            'pretty_version' => '1.20.0',
            'version' => '********',
            'reference' => '82fe4c73ef3363caed49ff8dd1539ba06044910d',
            'type' => 'composer-plugin',
            'install_path' => __DIR__ . '/../php-http/discovery',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'php-http/multipart-stream-builder' => array(
            'pretty_version' => '1.4.2',
            'version' => '*******',
            'reference' => '10086e6de6f53489cca5ecc45b6f468604d3460e',
            'type' => 'library',
            'install_path' => __DIR__ . '/../php-http/multipart-stream-builder',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/math' => array(
            'pretty_version' => '0.2.0',
            'version' => '*******',
            'reference' => 'fc2eb6d1a61b058d5dac77197059db30ee3c8329',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/math',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoffice/phpword' => array(
            'pretty_version' => '1.3.0',
            'version' => '*******',
            'reference' => '8392134ce4b5dba65130ba956231a1602b848b7f',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoffice/phpword',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'phpoption/phpoption' => array(
            'pretty_version' => '1.9.3',
            'version' => '*******',
            'reference' => 'e3fac8b24f56113f7cb96af14958c0dd16330f54',
            'type' => 'library',
            'install_path' => __DIR__ . '/../phpoption/phpoption',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client' => array(
            'pretty_version' => '1.0.3',
            'version' => '1.0.3.0',
            'reference' => 'bb5906edc1c324c9a05aa0873d40117941e5fa90',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-client',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-client-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-factory' => array(
            'pretty_version' => '1.1.0',
            'version' => '1.1.0.0',
            'reference' => '2b4765fddfe3b508ac62f829e852b1501d3f6e8a',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-factory',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-factory-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'psr/http-message' => array(
            'pretty_version' => '2.0',
            'version' => '2.0.0.0',
            'reference' => '402d35bcb92c70c026d1a6a9883f06b2ead23d71',
            'type' => 'library',
            'install_path' => __DIR__ . '/../psr/http-message',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'psr/http-message-implementation' => array(
            'dev_requirement' => false,
            'provided' => array(
                0 => '1.0',
                1 => '*',
            ),
        ),
        'ralouphie/getallheaders' => array(
            'pretty_version' => '3.0.3',
            'version' => '3.0.3.0',
            'reference' => '120b605dfeb996808c31b6477290a714d356e822',
            'type' => 'library',
            'install_path' => __DIR__ . '/../ralouphie/getallheaders',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'rmccue/requests' => array(
            'pretty_version' => 'v2.0.12',
            'version' => '2.0.12.0',
            'reference' => 'fb67e3d392ff6b89a90e96f19745662f4ecd62b1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../rmccue/requests',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'smalot/pdfparser' => array(
            'pretty_version' => 'v2.11.0',
            'version' => '2.11.0.0',
            'reference' => 'ac8e6678b0940e4b2ccd5caadd3fb18e68093be6',
            'type' => 'library',
            'install_path' => __DIR__ . '/../smalot/pdfparser',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/deprecation-contracts' => array(
            'pretty_version' => 'v3.5.0',
            'version' => '3.5.0.0',
            'reference' => '0e0d29ce1f20deffb4ab1b016a7257c4f1e789a1',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/deprecation-contracts',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-ctype' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => 'a3cc8b044a6ea513310cbd48ef7333b384945638',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-ctype',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-mbstring' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '85181ba99b2345b0ef10ce42ecac37612d9fd341',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-mbstring',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'symfony/polyfill-php80' => array(
            'pretty_version' => 'v1.31.0',
            'version' => '1.31.0.0',
            'reference' => '60328e362d4c2c802a54fcbf04f9d3fb892b4cf8',
            'type' => 'library',
            'install_path' => __DIR__ . '/../symfony/polyfill-php80',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'theodo-group/llphant' => array(
            'pretty_version' => '0.8.9',
            'version' => '0.8.9.0',
            'reference' => '55f0b09d7e00a85d6aaef11ffc775874bf105deb',
            'type' => 'library',
            'install_path' => __DIR__ . '/../theodo-group/llphant',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
        'vlucas/phpdotenv' => array(
            'pretty_version' => 'v5.6.1',
            'version' => '5.6.1.0',
            'reference' => 'a59a13791077fe3d44f90e7133eb68e7d22eaff2',
            'type' => 'library',
            'install_path' => __DIR__ . '/../vlucas/phpdotenv',
            'aliases' => array(),
            'dev_requirement' => false,
        ),
    ),
);
