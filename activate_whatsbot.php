<?php
/**
 * Complete WhatsBot Activation Script
 * Run this script to fully activate WhatsBot without Envato validation
 */

echo "<h2>WhatsBot Activation Script</h2>";

// Check if we're in the correct directory
if (!file_exists('whatsbot.php')) {
    die('<p style="color: red;">Error: Please run this script from the WhatsBot module directory.</p>');
}

// Try to include CodeIgniter
$ci_paths = [
    '../../index.php',
    '../../../index.php',
    '../../../../index.php'
];

$ci_loaded = false;
foreach ($ci_paths as $path) {
    if (file_exists($path)) {
        // Set up basic environment
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_URI'] = '/admin';
        $_SERVER['HTTP_HOST'] = 'localhost';
        
        try {
            require_once $path;
            $ci_loaded = true;
            break;
        } catch (Exception $e) {
            continue;
        }
    }
}

if ($ci_loaded && function_exists('get_instance')) {
    echo "<p style='color: green;'>✓ CodeIgniter loaded successfully</p>";
    
    $CI = &get_instance();
    $module_name = 'whatsbot';
    
    // Create fake verification data
    $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
    $fake_token = base64_encode(json_encode([
        'item_id' => '53052338', // WhatsBot item ID
        'buyer' => 'admin',
        'purchase_code' => 'fake_purchase_code',
        'check_interval' => 86400 // 24 hours
    ]));
    
    // Set activation options
    $options = [
        $module_name.'_verification_id' => base64_encode($fake_verification_id),
        $module_name.'_last_verification' => time(),
        $module_name.'_product_token' => $fake_token,
        $module_name.'_support_until_date' => date('Y-m-d', strtotime('+10 years'))
    ];
    
    foreach ($options as $option_name => $option_value) {
        update_option($option_name, $option_value);
        echo "<p>✓ Set option: $option_name</p>";
    }
    
    // Remove any heartbeat failure records
    delete_option($module_name.'_heartbeat');
    echo "<p>✓ Removed heartbeat option</p>";
    
    // Try to create license file
    try {
        $CI->load->helper('whatsbot/whatsbot');
        $chatOptions = set_chat_header();
        $content = 'activated_' . time();
        write_file(TEMP_FOLDER . $chatOptions['chat_content'] . '.lic', $content);
        echo "<p>✓ Created license file</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Could not create license file (this is usually okay): " . $e->getMessage() . "</p>";
    }
    
    echo "<h3 style='color: green;'>✓ WhatsBot has been successfully activated!</h3>";
    echo "<p>You can now use all features without Envato validation.</p>";
    echo "<p><a href='../../admin/modules'>Go to Modules</a> | <a href='../../admin/whatsbot'>Open WhatsBot</a></p>";
    
} else {
    echo "<p style='color: orange;'>⚠ Could not load CodeIgniter automatically. Using direct database method...</p>";
    
    // Try direct database activation
    $config_paths = [
        '../../application/config/database.php',
        '../../../application/config/database.php',
        '../../../../application/config/database.php'
    ];
    
    $db_config = null;
    foreach ($config_paths as $path) {
        if (file_exists($path)) {
            include $path;
            if (isset($db['default'])) {
                $db_config = $db['default'];
                break;
            }
        }
    }
    
    if ($db_config) {
        try {
            $pdo = new PDO(
                "mysql:host={$db_config['hostname']};dbname={$db_config['database']}", 
                $db_config['username'], 
                $db_config['password']
            );
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            
            $prefix = $db_config['dbprefix'];
            $module_name = 'whatsbot';
            
            // Create fake verification data
            $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
            $fake_token = base64_encode(json_encode([
                'item_id' => '53052338',
                'buyer' => 'admin',
                'purchase_code' => 'fake_purchase_code',
                'check_interval' => 86400
            ]));
            
            $options = [
                $module_name.'_verification_id' => base64_encode($fake_verification_id),
                $module_name.'_last_verification' => time(),
                $module_name.'_product_token' => $fake_token,
                $module_name.'_support_until_date' => date('Y-m-d', strtotime('+10 years'))
            ];
            
            foreach ($options as $option_name => $option_value) {
                // Check if option exists
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$prefix}options WHERE name = ?");
                $stmt->execute([$option_name]);
                $exists = $stmt->fetchColumn() > 0;
                
                if ($exists) {
                    $stmt = $pdo->prepare("UPDATE {$prefix}options SET value = ? WHERE name = ?");
                    $stmt->execute([$option_value, $option_name]);
                } else {
                    $stmt = $pdo->prepare("INSERT INTO {$prefix}options (name, value, autoload) VALUES (?, ?, 1)");
                    $stmt->execute([$option_name, $option_value]);
                }
                echo "<p>✓ Set option: $option_name</p>";
            }
            
            // Remove heartbeat
            $stmt = $pdo->prepare("DELETE FROM {$prefix}options WHERE name = ?");
            $stmt->execute([$module_name.'_heartbeat']);
            
            echo "<h3 style='color: green;'>✓ WhatsBot has been successfully activated via database!</h3>";
            echo "<p>You can now use all features without Envato validation.</p>";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>Database error: " . $e->getMessage() . "</p>";
        }
    } else {
        echo "<p style='color: red;'>Could not find database configuration.</p>";
    }
}

echo "<hr>";
echo "<p><strong>Next Steps:</strong></p>";
echo "<ol>";
echo "<li>Go to your admin panel → Modules</li>";
echo "<li>If WhatsBot is deactivated, click 'Activate' - it should activate without asking for Envato details</li>";
echo "<li>If it's already activated, you should be able to access all features</li>";
echo "<li>Navigate to WhatsBot from the admin menu to start using it</li>";
echo "</ol>";
?>
