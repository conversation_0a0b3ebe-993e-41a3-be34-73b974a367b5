<?php
/**
 * Complete WhatsBot Fix Script
 * This script fixes all activation and functionality issues
 * Rebranded by Audience Target CRM - https://audiencetargetcrm.online/
 */

echo "<h1>WhatsBot Complete Fix & Activation</h1>";
echo "<p><strong>Rebranded by Audience Target CRM</strong> - <a href='https://audiencetargetcrm.online/' target='_blank'>https://audiencetargetcrm.online/</a></p>";
echo "<hr>";

// Check if we're in the correct directory
if (!file_exists('whatsbot.php')) {
    die('<p style="color: red;">Error: Please run this script from the WhatsBot module directory.</p>');
}

// Try to include CodeIgniter
$ci_paths = [
    '../../index.php',
    '../../../index.php',
    '../../../../index.php'
];

$ci_loaded = false;
foreach ($ci_paths as $path) {
    if (file_exists($path)) {
        // Set up basic environment
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_URI'] = '/admin';
        $_SERVER['HTTP_HOST'] = 'localhost';
        
        try {
            require_once $path;
            $ci_loaded = true;
            break;
        } catch (Exception $e) {
            continue;
        }
    }
}

if ($ci_loaded && function_exists('get_instance')) {
    echo "<p style='color: green;'>✓ CodeIgniter loaded successfully</p>";
    
    $CI = &get_instance();
    $module_name = 'whatsbot';
    
    echo "<h2>Step 1: Module Activation</h2>";
    
    // Force complete activation
    $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
    $fake_token = base64_encode(json_encode([
        'item_id' => '53052338',
        'buyer' => 'admin',
        'purchase_code' => 'fake_purchase_code',
        'check_interval' => 86400
    ]));
    
    $options = [
        $module_name.'_verification_id' => base64_encode($fake_verification_id),
        $module_name.'_last_verification' => time(),
        $module_name.'_product_token' => $fake_token,
        $module_name.'_support_until_date' => date('Y-m-d', strtotime('+10 years'))
    ];
    
    foreach ($options as $option_name => $option_value) {
        update_option($option_name, $option_value);
        echo "<p>✓ Set option: $option_name</p>";
    }
    
    // Remove any heartbeat failures
    delete_option($module_name.'_heartbeat');
    echo "<p>✓ Removed heartbeat failures</p>";
    
    echo "<h2>Step 2: Database Tables Check</h2>";
    
    // Check and create missing tables if needed
    $required_tables = [
        'wtc_templates' => "CREATE TABLE IF NOT EXISTS `" . db_prefix() . "wtc_templates` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `category` varchar(100) DEFAULT NULL,
            `language` varchar(10) DEFAULT 'en',
            `status` enum('APPROVED','PENDING','REJECTED') DEFAULT 'PENDING',
            `header_params_count` int(11) DEFAULT 0,
            `body_params_count` int(11) DEFAULT 0,
            `footer_params_count` int(11) DEFAULT 0,
            `header_data_format` varchar(50) DEFAULT NULL,
            `header_data_text` text,
            `body_data` text,
            `footer_data` text,
            `template_id` varchar(255) DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        )",
        'wtc_campaigns' => "CREATE TABLE IF NOT EXISTS `" . db_prefix() . "wtc_campaigns` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `name` varchar(255) NOT NULL,
            `template_id` int(11) NOT NULL,
            `rel_type` enum('leads','contacts') NOT NULL,
            `select_all` tinyint(1) DEFAULT 0,
            `scheduled_send_time` datetime DEFAULT NULL,
            `pause_campaign` tinyint(1) DEFAULT 0,
            `is_sent` tinyint(1) DEFAULT 0,
            `is_bot` tinyint(1) DEFAULT 0,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        )",
        'wtc_campaign_data' => "CREATE TABLE IF NOT EXISTS `" . db_prefix() . "wtc_campaign_data` (
            `id` int(11) NOT NULL AUTO_INCREMENT,
            `campaign_id` int(11) NOT NULL,
            `rel_id` int(11) NOT NULL,
            `rel_type` enum('leads','contacts') NOT NULL,
            `header_message` text,
            `body_message` text,
            `footer_message` text,
            `status` tinyint(1) DEFAULT 1,
            `sent_at` timestamp NULL DEFAULT NULL,
            `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (`id`)
        )"
    ];
    
    foreach ($required_tables as $table => $sql) {
        $full_table_name = db_prefix() . $table;
        if ($CI->db->table_exists($full_table_name)) {
            echo "<p style='color: green;'>✓ Table $full_table_name exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Table $full_table_name missing - creating...</p>";
            try {
                $CI->db->query($sql);
                echo "<p style='color: green;'>✓ Table $full_table_name created</p>";
            } catch (Exception $e) {
                echo "<p style='color: red;'>✗ Failed to create table $full_table_name: " . $e->getMessage() . "</p>";
            }
        }
    }
    
    echo "<h2>Step 3: Upload Directories</h2>";
    
    $upload_dirs = [
        'uploads/whatsbot',
        'uploads/whatsbot/campaign',
        'uploads/whatsbot/template',
        'uploads/whatsbot/bot_files',
        'uploads/whatsbot/csv',
        'uploads/whatsbot/flow',
        'uploads/whatsbot/personal_assistant'
    ];
    
    foreach ($upload_dirs as $dir) {
        $full_path = FCPATH . $dir;
        if (is_dir($full_path)) {
            echo "<p style='color: green;'>✓ Directory $dir exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Directory $dir missing - creating...</p>";
            if (mkdir($full_path, 0755, true)) {
                // Create index.html for security
                file_put_contents($full_path . '/index.html', '');
                echo "<p style='color: green;'>✓ Directory $dir created</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create directory $dir</p>";
            }
        }
    }
    
    echo "<h2>Step 4: License File</h2>";
    
    try {
        $CI->load->helper('whatsbot/whatsbot');
        $chatOptions = set_chat_header();
        $license_file = TEMP_FOLDER . $chatOptions['chat_content'] . '.lic';
        $content = 'activated_by_audience_target_crm_' . time();
        write_file($license_file, $content);
        echo "<p style='color: green;'>✓ License file created</p>";
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Could not create license file: " . $e->getMessage() . "</p>";
    }
    
    echo "<h2>Step 5: Module Status Check</h2>";
    
    $module = $CI->app_modules->get($module_name);
    if ($module && $CI->app_modules->is_active($module_name)) {
        echo "<p style='color: green;'>✓ Module is activated and ready</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Module needs to be activated in Admin → Modules</p>";
    }
    
    echo "<h2>✅ Complete Fix Applied Successfully!</h2>";
    echo "<div style='background: #d4edda; border: 1px solid #c3e6cb; padding: 15px; border-radius: 5px; margin: 20px 0;'>";
    echo "<h3>Next Steps:</h3>";
    echo "<ol>";
    echo "<li>Go to <strong>Admin → Modules</strong></li>";
    echo "<li>If WhatsBot is not activated, click <strong>'Activate'</strong></li>";
    echo "<li>Navigate to <strong>WhatsBot</strong> from the admin menu</li>";
    echo "<li>All features should now work perfectly!</li>";
    echo "</ol>";
    echo "</div>";
    
} else {
    echo "<p style='color: red;'>Could not load CodeIgniter. Please run this script from a web browser.</p>";
}

echo "<hr>";
echo "<p><strong>WhatsBot Module</strong></p>";
echo "<p>Rebranded and Enhanced by <strong>Audience Target CRM</strong></p>";
echo "<p>Website: <a href='https://audiencetargetcrm.online/' target='_blank'>https://audiencetargetcrm.online/</a></p>";
echo "<p>All original functionality preserved with enhanced activation system.</p>";
?>
