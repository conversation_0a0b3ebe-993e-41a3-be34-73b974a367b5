<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Свържете акаунт';
$lang['connect_whatsapp_business'] = 'Свържете Whatsapp Business';
$lang['campaigning'] = 'Кампани';
$lang['business_account_id_description'] = 'Вашият ID на WhatsApp Business акаунт (WABA)';
$lang['access_token_description'] = 'Вашият потребителски достъп токен след регистрация в Facebook Developers Portal';
$lang['whatsapp_business_account_id'] = 'ID на WhatsApp Business акаунт';
$lang['whatsapp_access_token'] = 'Достъп токен на WhatsApp';
$lang['webhook_callback_url'] = 'URL за обратно повикване на Webhook';
$lang['verify_token'] = 'Потвърдете токен';
$lang['connect'] = 'Свържете';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Свързване на акаунт с едно кликване';
$lang['connect_your_whatsapp_account'] = 'Свържете Вашия WhatsApp акаунт';
$lang['copy'] = 'Копиране';
$lang['copied'] = 'Копирано!!';
$lang['disconnect'] = 'Разключване';
$lang['number'] = 'Номер';
$lang['number_id'] = 'ID на номера';
$lang['quality'] = 'Качество';
$lang['status'] = 'Статус';
$lang['business_account_id'] = 'ID на бизнес акаунт';
$lang['permissions'] = 'Разрешения';
$lang['phone_number_id_description'] = 'ID на телефонния номер, свързан с WhatsApp Business API. Ако не сте сигурни, можете да използвате GET заявка за ID на телефонния номер, за да го получите от WhatsApp API (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'ID на номера на регистрирания телефон в WhatsApp';
$lang['update_details'] = 'Актуализирайте детайлите';

$lang['bots'] = 'Ботове';
$lang['bots_management'] = 'Управление на ботове';
$lang['create_template_base_bot'] = 'Създайте шаблонен бот';
$lang['create_message_bot'] = 'Създайте бот за съобщения';
$lang['type'] = 'Тип';
$lang['message_bot'] = 'Бот за съобщения';
$lang['new_template_bot'] = 'Нов шаблонен бот';
$lang['new_message_bot'] = 'Нов бот за съобщения';
$lang['bot_name'] = 'Име на бота';
$lang['reply_text'] = 'Текст на отговор <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Текстът, който ще бъде изпратен на клиента или контакта. Можете също да използвате {companyname}, {crm_url} или всякакви други персонализирани полета за сливане на клиента или контакта, или да използвате знака \'@\', за да намерите наличните полета за сливане" data-placement="bottom"></i> <span class="text-muted">(Максималният брой разрешени знаци е 1024)</span>';
$lang['reply_type'] = 'Тип на отговора';
$lang['trigger'] = 'Сигнал';
$lang['header'] = 'Заглавие';
$lang['footer_bot'] = 'Долна част <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Опция 1: Бот с бутони за отговор';
$lang['bot_with_button_link'] = 'Опция 2: Бот с бутон линк - CTA URL';
$lang['button1'] = 'Бутон1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID на бутон1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 256" data-placement="bottom"></i>';
$lang['button2'] = 'Бутон2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID на бутон2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 256" data-placement="bottom"></i>';
$lang['button3'] = 'Бутон3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID на бутон3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Име на бутона <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максималният брой разрешени знаци е 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Линк на бутона';
$lang['enter_name'] = 'Въведете име';
$lang['select_reply_type'] = 'Изберете тип на отговора';
$lang['enter_bot_reply_trigger'] = 'Въведете сигнал за отговор на бота';
$lang['enter_header'] = 'Въведете заглавие';
$lang['enter_footer'] = 'Въведете долна част';
$lang['enter_button1'] = 'Въведете бутон1';
$lang['enter_button1_id'] = 'Въведете ID на бутон1';
$lang['enter_button2'] = 'Въведете бутон2';
$lang['enter_button2_id'] = 'Въведете ID на бутон2';
$lang['enter_button3'] = 'Въведете бутон3';
$lang['enter_button3_id'] = 'Въведете ID на бутон3';
$lang['enter_button_name'] = 'Въведете име на бутона';
$lang['enter_button_url'] = 'Въведете URL на бутона';
$lang['on_exact_match'] = 'Бот за отговор: При точно съвпадение';
$lang['when_message_contains'] = 'Бот за отговор: Когато съобщението съдържа';
$lang['when_client_send_the_first_message'] = 'Добре дошли отговор - когато клиентът изпрати първото съобщение';
$lang['bot_create_successfully'] = 'Ботът е създаден успешно';
$lang['bot_update_successfully'] = 'Ботът е актуализиран успешно';
$lang['bot_deleted_successfully'] = 'Ботът е изтрит успешно';
$lang['templates'] = 'Шаблони';
$lang['template_data_loaded'] = 'Шаблоните са заредени успешно';
$lang['load_templates'] = 'Заредете шаблони';
$lang['template_management'] = 'Управление на шаблони';


// кампании
$lang['campaign'] = 'Кампания';
$lang['campaigns'] = 'Кампании';
$lang['send_new_campaign'] = 'Изпратете нова кампания';
$lang['campaign_name'] = 'Име на кампанията';
$lang['template'] = 'Шаблон';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="За всеки клиент, на база часовата зона на контакта" data-placement="left"></i>Насрочено време за изпращане';
$lang['scheduled_time_description'] = 'За всеки клиент, на база часовата зона на контакта';
$lang['ignore_scheduled_time_and_send_now'] = 'Игнорирайте насроченото време и изпратете сега';
$lang['leads'] = 'Лийдове';
$lang['delivered_to'] = 'Доставено на';
$lang['read_by'] = 'Прочетено от';
$lang['variables'] = 'Променливи';
$lang['body'] = 'Тяло';
$lang['variable'] = 'Променлива';
$lang['match_with_selected_field'] = 'Съответствие с избраното поле';
$lang['preview'] = 'Преглед';
$lang['send_campaign'] = 'Изпратете кампания';
$lang['send_to'] = 'Изпратете до';
$lang['view_campaign'] = 'Прегледайте кампанията';
$lang['campaign_daily_task'] = 'Ежедневна задача за кампанията';
$lang['back'] = 'Назад';
$lang['phone'] = 'Телефон';
$lang['message'] = 'Съобщение';
$lang['currently_type_not_supported'] = 'В момента <strong> %s </strong> тип шаблон не се поддържа!';
$lang['of_your'] = 'на вашите';
$lang['contacts'] = 'Контакти';
$lang['select_all_leads'] = 'Изберете всички лийдове';
$lang['select_all_note_leads'] = 'Ако изберете това, всички бъдещи лийдове ще бъдат включени в тази кампания.';
$lang['select_all_note_contacts'] = 'Ако изберете това, всички бъдещи контакти ще бъдат включени в тази кампания.';

$lang['verified_name'] = 'Потвърдено име';
$lang['mark_as_default'] = 'Маркирайте като по подразбиране';
$lang['default_number_updated'] = 'Телефонният номер по подразбиране е успешно актуализиран';
$lang['currently_using_this_number'] = 'Текущо използване на този номер';
$lang['pause_campaign'] = 'Пауза на кампанията';
$lang['resume_campaign'] = 'Продължете кампанията';
$lang['campaign_resumed'] = 'Кампанията е подновена';
$lang['campaign_paused'] = 'Кампанията е поставена на пауза';

// Шаблон
$lang['body_data'] = 'Данни за тялото';
$lang['category'] = 'Категория';

// Шаблон бот
$lang['create_new_template_bot'] = 'Създайте нов шаблон бот';
$lang['template_bot'] = 'Шаблон бот';
$lang['variables'] = 'Променливи';
$lang['preview'] = 'Преглед';
$lang['template'] = 'Шаблон';
$lang['bot_content_1'] = 'Това съобщение ще бъде изпратено на контакта, когато бъде изпълнено правилото за активиране в съобщението, изпратено от контакта.';
$lang['save_bot'] = 'Запазете бота';
$lang['please_select_template'] = 'Моля, изберете шаблон';
$lang['use_manually_define_value'] = 'Използвайте ръчно дефинирана стойност';
$lang['merge_fields'] = 'Обединяване на полета';
$lang['template_bot_create_successfully'] = 'Шаблон ботът е създаден успешно';
$lang['template_bot_update_successfully'] = 'Шаблон ботът е актуализиран успешно';
$lang['text_bot'] = 'Текст бот';
$lang['option_2_bot_with_link'] = 'Опция 2: Бот с бутон за връзка - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Опция 3: Бот с файл';
// Настройки на бот
$lang['bot'] = 'Бот';
$lang['bot_delay_response'] = 'Съобщението се изпраща при очаквано забавяне на отговора';
$lang['bot_delay_response_placeholder'] = 'Дайте ми момент, ще имам отговора скоро';

$lang['whatsbot'] = 'WhatsBot';

// campaigns
$lang['relation_type'] = 'Тип на връзката';
$lang['select_all'] = 'Изберете всички';
$lang['total'] = 'Общо';
$lang['merge_field_note'] = 'Използвайте \'@\' знак за добавяне на полета за обединяване.';
$lang['send_to_all'] = 'Изпрати на всички';
$lang['or'] = 'ИЛИ';

$lang['convert_whatsapp_message_to_lead'] = 'Автоматично придобиване на нов клиент (конвертиране на нови съобщения в WhatsApp в клиент)';
$lang['leads_status'] = 'Статус на клиента';
$lang['leads_assigned'] = 'Клиент назначен';
$lang['whatsapp_auto_lead'] = 'Автоматичен клиент от WhatsApp';
$lang['webhooks_label'] = 'Получените данни от WhatsApp ще бъдат препратени на';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Активирайте препращане чрез WebHooks';
$lang['chat'] = 'Чат';
$lang['black_listed_phone_numbers'] = 'Номера в черния списък';
$lang['sent_status'] = 'Статус на изпращане';

$lang['active'] = 'Активен';
$lang['approved'] = 'Одобрен';
$lang['this_month'] = 'Този месец';
$lang['open_chats'] = 'Отворени чатове';
$lang['resolved_conversations'] = 'Разрешени разговори';
$lang['messages_sent'] = 'Изпратени съобщения';
$lang['account_connected'] = 'Акаунт свързан';
$lang['account_disconnected'] = 'Акаунт прекъснат';
$lang['webhook_verify_token'] = 'Проверка на токена за Webhook';
// Chat integration
$lang['chat_message_note'] = 'Съобщението ще бъде изпратено скоро. Моля, имайте предвид, че ако е нов контакт, той няма да се появи в този списък, докато контактът не започне да комуникира с вас!';

$lang['activity_log'] = 'Дневник на активността';
$lang['whatsapp_logs'] = 'Дневници на WhatsApp';
$lang['response_code'] = 'Код на отговора';
$lang['recorded_on'] = 'Записано на';

$lang['request_details'] = 'Детайли на заявката';
$lang['raw_content'] = 'Сурово съдържание';
$lang['headers'] = 'Заглавки';
$lang['format_type'] = 'Тип формат';

// Permission section
$lang['show_campaign'] = 'Показване на кампанията';
$lang['clear_log'] = 'Изчисти дневника';
$lang['log_activity'] = 'Запиши активността';
$lang['load_template'] = 'Зареди шаблона';

$lang['action'] = 'Действие';
$lang['total_parameters'] = 'Общ брой параметри';
$lang['template_name'] = 'Име на шаблона';
$lang['log_cleared_successfully'] = 'Дневникът успешно изчистен';
$lang['whatsbot_stats'] = 'Статистика на WhatsBot';

$lang['not_found_or_deleted'] = 'Не е намерено или е изтрито';
$lang['response'] = 'Отговор';

$lang['select_image'] = 'Изберете изображение';
$lang['image'] = 'Изображение';
$lang['image_deleted_successfully'] = 'Изображението е успешно изтрито';
$lang['whatsbot_settings'] = 'Настройки на Whatsbot';
$lang['maximum_file_size_should_be'] = 'Максималният размер на файла трябва да бъде ';
$lang['allowed_file_types'] = 'Позволени типове файлове: ';

$lang['send_image'] = 'Изпратете изображение';
$lang['send_video'] = 'Изпратете видео';
$lang['send_document'] = 'Изпратете документ';
$lang['record_audio'] = 'Запишете аудио';
$lang['chat_media_info'] = 'Повече информация за поддържаните типове съдържание и постобработка на медийните файлове';
$lang['help'] = 'Помощ';

// v1.1.0
$lang['clone'] = 'Клонирай';
$lang['bot_clone_successfully'] = 'Ботът беше успешно клониран';
$lang['all_chat'] = 'Всички чатове';
$lang['from'] = 'От:';
$lang['phone_no'] = 'Телефонен номер:';
$lang['supportagent'] = 'Агент за поддръжка';
$lang['assign_chat_permission_to_support_agent'] = 'Присвояване на права за чат само на агент за поддръжка';
$lang['enable_whatsapp_notification_sound'] = 'Активирай звук за уведомления в WhatsApp чат';
$lang['notification_sound'] = 'Звук за уведомление';
$lang['trigger_keyword'] = 'Ключова дума за активиране';
$lang['modal_title'] = 'Изберете агент за поддръжка';
$lang['close_btn'] = 'Затвори';
$lang['save_btn'] = 'Запази';
$lang['support_agent'] = 'Агент за поддръжка';
$lang['change_support_agent'] = 'Смени агента за поддръжка';
$lang['replay_message'] = 'Не можете да изпратите съобщение, изминали са 24 часа.';
$lang['support_agent_note'] = '<strong>Забележка:</strong> Когато активирате функцията за агент за поддръжка, назначените клиенти автоматично ще се назначават към чата. Администраторите също могат да назначат нов агент от страницата за чат.';
$lang['permission_bot_clone'] = 'Клониране на бот';
$lang['remove_chat'] = 'Премахни чат';
$lang['default_message_on_no_match'] = 'По подразбиране отговор - ако няма съвпадение на ключова дума';
$lang['default_message_note'] = '<strong>Забележка:</strong> Активирането на тази опция ще увеличи натоварването на уебхука. За повече информация посетете този <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">линк</a>.';

$lang['whatsbot_connect_account'] = 'Whatsbot свързване на акаунт';
$lang['whatsbot_message_bot'] = 'Whatsbot Бот за съобщения';
$lang['whatsbot_template_bot'] = 'Whatsbot Бот за шаблони';
$lang['whatsbot_template'] = 'Whatsbot Шаблон';
$lang['whatsbot_campaigns'] = 'Whatsbot Кампании';
$lang['whatsbot_chat'] = 'Whatsbot Чат';
$lang['whatsbot_log_activity'] = 'Whatsbot Дневник на активността';
$lang['message_templates_not_exists_note'] = 'Липсват разрешения за шаблони на Meta. Моля, активирайте ги във вашия Meta акаунт';

// v1.2.0
$lang['ai_prompt'] = 'AI Подканвания';
$lang['ai_prompt_note'] = 'За AI подканвания, моля въведете съобщение, за да активирате функцията, или използвайте AI подканвания, ако вече са активирани';
$lang['emojis'] = 'Емоджита';
$lang['translate'] = 'Преведи';
$lang['change_tone'] = 'Смени тона';
$lang['professional'] = 'Професионален';
$lang['friendly'] = 'Приятелски';
$lang['empathetic'] = 'Емпатичен';
$lang['straightforward'] = 'Прям';
$lang['simplify_language'] = 'Опрости езика';
$lang['fix_spelling_and_grammar'] = 'Коригирай правописа и граматиката';

$lang['ai_integration'] = 'AI Интеграция';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'OpenAI Таен ключ - <a href="https://platform.openai.com/account/api-keys" target="_blank">Къде да намерите тайния ключ?</a>';
$lang['chat_text_limit'] = 'Ограничение на текста в чата';
$lang['chat_text_limit_note'] = 'За оптимизация на оперативните разходи, помислете за ограничаване на броя думи в отговорите на OpenAI';
$lang['chat_model'] = 'Модел за чат';
$lang['openai_organizations'] = 'OpenAi Организации';
$lang['template_type'] = 'Тип шаблон';
$lang['update'] = 'Актуализирай';
$lang['open_ai_key_verification_fail'] = 'Проверката на ключа за OpenAi е в очакване от настройките. Моля, свържете вашия OpenAi акаунт';
$lang['enable_wb_openai'] = 'Активирайте OpenAI в чата';
$lang['webhook_resend_method'] = 'Метод за повторно изпращане на Webhook';
$lang['search_language'] = 'Търсене на език...';
$lang['document'] = 'Документ';
$lang['select_document'] = 'Изберете документ';
$lang['attchment_deleted_successfully'] = 'Прикаченият файл е изтрит успешно';
$lang['attach_image_video_docs'] = 'Прикачете изображение, видео или документи';
$lang['choose_file_type'] = 'Изберете тип файл';
$lang['max_size'] = 'Максимален размер: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Масови кампании';
$lang['upload_csv'] = 'Качване на CSV';
$lang['upload'] = 'Качи';
$lang['csv_uploaded_successfully'] = 'CSV файлът беше успешно качен';
$lang['please_select_file'] = 'Моля, изберете CSV файл';
$lang['phonenumber_field_is_required'] = 'Полето за телефонен номер е задължително';
$lang['out_of_the'] = 'От общо';
$lang['records_in_your_csv_file'] = 'записи във вашия CSV файл,';
$lang['valid_the_campaign_can_be_sent'] = 'записи са валидни.<br /> Кампанията може успешно да бъде изпратена на тези';
$lang['users'] = 'потребители';
$lang['campaigns_from_csv_file'] = 'Кампании от CSV файл';
$lang['download_sample'] = 'Изтегли пример';
$lang['csv_rule_1'] = '1. <b>Изисквания към колоната за телефонен номер:</b> Вашият CSV файл трябва да съдържа колона с име "Phoneno." Всеки запис в тази колона трябва да съдържа валиден контакт номер, правилно форматиран с кода на държавата, включително знака "+". <br /><br />';
$lang['csv_rule_2'] = '2. <b>Формат и кодировка на CSV:</b> Вашите CSV данни трябва да следват указания формат. Първият ред на вашия CSV файл трябва да съдържа заглавията на колоните, както е показано в примерната таблица. Уверете се, че вашият файл е кодиран в UTF-8, за да се избегнат проблеми с кодирането.';
$lang['please_upload_valid_csv_file'] = 'Моля, качете валиден CSV файл';
$lang['please_add_valid_number_in_csv_file'] = 'Моля, добавете валиден <b>Телефонен номер</b> в CSV файла';
$lang['total_send_campaign_list'] = 'Общо изпратени кампании: %s';
$lang['sample_data'] = 'Примерни данни';
$lang['firstname'] = 'Първо име';
$lang['lastname'] = 'Фамилно име';
$lang['phoneno'] = 'Телефонен номер';
$lang['email'] = 'Имейл';
$lang['country'] = 'Държава';
$lang['download_sample_and_read_rules'] = 'Изтеглете примерен файл и прочетете правилата';
$lang['please_wait_your_request_in_process'] = 'Моля, изчакайте, заявката ви се обработва.';
$lang['whatsbot_bulk_campaign'] = 'Whatsbot Масови кампании';
$lang['csv_campaign'] = 'CSV Кампания';

// Canned reply
$lang['canned_reply'] = 'Стандартизиран отговор';
$lang['canned_reply_menu'] = 'Стандартизирани отговори';
$lang['create_canned_reply'] = 'Създаване на стандартизиран отговор';
$lang['title'] = 'Заглавие';
$lang['desc'] = 'Описание';
$lang['public'] = 'Публичен';
$lang['action'] = 'Действие';
$lang['delete_successfully'] = 'Отговорът беше изтрит.';
$lang['cannot_delete'] = 'Не може да се изтрие отговорът.';
$lang['whatsbot_canned_reply'] = 'Whatsbot Стандартизиран отговор';
$lang['reply'] = 'Отговор';

// AI Prompts
$lang['ai_prompts'] = 'AI Подканвания';
$lang['create_ai_prompts'] = 'Създаване на AI подканвания';
$lang['name'] = 'Име';
$lang['action'] = 'Действие';
$lang['prompt_name'] = 'Име на подканване';
$lang['prompt_action'] = 'Действие на подканване';
$lang['whatsbot_ai_prompts'] = 'Whatsbot AI Подканвания';

// new chat
$lang['replying_to'] = 'Отговаряте на:';
$lang['download_document'] = 'Изтегли документ';
$lang['custom_prompt'] = 'Персонализирана подканва';
$lang['canned_replies'] = 'Стандартизирани отговори';
$lang['use_@_to_add_merge_fields'] = 'Използвайте \'@\' за добавяне на полета за сливане';
$lang['type_your_message'] = 'Напишете вашето съобщение';
$lang['you_cannot_send_a_message_using_this_number'] = 'Не можете да изпратите съобщение, използвайки този номер.';

// bot flow
$lang['bot_flow'] = 'Поток на бота';
$lang['create_new_flow'] = 'Създаване на нов поток';
$lang['flow_name'] = 'Име на потока';
$lang['flow'] = 'Поток';
$lang['bot_flow_builder'] = 'Конструктор на поток на бота';
$lang['you_can_not_upload_file_type'] = 'Не можете да качите файл от тип <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Whatsbot Поток на бота';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Автоматично изтриване на историята на чата';
$lang['enable_auto_clear_chat_history'] = 'Активирайте автоматично изтриване на историята на чата';
$lang['auto_clear_time'] = 'Време за автоматично изтриване на историята';
$lang['clear_chat_history_note'] = '<strong>Забележка:</strong> Ако активирате функцията за автоматично изтриване на историята на чата, тя автоматично ще изтрива историята на чата въз основа на броя дни, които зададете, всеки път, когато се изпълнява cron задача.';
$lang['source'] = 'Източник';
$lang['groups'] = 'Групи';


// v1.3.3

$lang['click_user_to_chat'] = 'Кликнете върху потребителя, за да чатите';
$lang['searching'] = 'Търсене...';
$lang['filters'] = 'Филтри';
$lang['relation_type'] = 'Тип на връзката';
$lang['groups'] = 'Групи';
$lang['source'] = 'Източник';
$lang['status'] = 'Статус';
$lang['select_type'] = 'Изберете тип';
$lang['select_agents'] = 'Изберете агенти';
$lang['select_group'] = 'Изберете група';
$lang['select_source'] = 'Изберете източник';
$lang['select_status'] = 'Изберете статус';
$lang['agents'] = 'Агенти';

// v1.4.2
$lang['read_only'] = 'Само за четене';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
