<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Collegare Account';
$lang['connect_whatsapp_business'] = 'Collegare Whatsapp Business';
$lang['campaigning'] = 'Campagna';
$lang['business_account_id_description'] = 'Il tuo ID dell\'Account Aziendale WhatsApp (WABA)';
$lang['access_token_description'] = 'Il tuo Token di Accesso Utente dopo esserti registrato sul Portale Sviluppatori di Facebook';
$lang['whatsapp_business_account_id'] = 'ID Account Aziendale Whatsapp';
$lang['whatsapp_access_token'] = 'Token di Accesso Whatsapp';
$lang['webhook_callback_url'] = 'URL di Callback Webhook';
$lang['verify_token'] = 'Verifica Token';
$lang['connect'] = 'Collegare';
$lang['whatsapp'] = 'Whatsapp';
$lang['one_click_account_connection'] = 'Collegamento Account con un Clic';
$lang['connect_your_whatsapp_account'] = 'Collegare il tuo Account Whatsapp';
$lang['copy'] = 'Copia';
$lang['copied'] = 'Copiato!!';
$lang['disconnect'] = 'Disconnettere';
$lang['number'] = 'Numero';
$lang['number_id'] = 'ID Numero';
$lang['quality'] = 'Qualità';
$lang['status'] = 'Stato';
$lang['business_account_id'] = 'ID Account Aziendale';
$lang['permissions'] = 'Permessi';
$lang['phone_number_id_description'] = 'ID del numero di telefono collegato all\'API Business di WhatsApp. Se non sei sicuro, puoi utilizzare una richiesta GET Phone Number ID per recuperarlo dall\'API di WhatsApp (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'ID Numero del Telefono Registrato su Whatsapp';
$lang['update_details'] = 'Aggiorna Dettagli';

$lang['bots'] = 'Bot';
$lang['bots_management'] = 'Gestione Bot';
$lang['create_template_base_bot'] = 'Crea bot basato su modello';
$lang['create_message_bot'] = 'Crea bot di messaggio';
$lang['type'] = 'Tipo';
$lang['message_bot'] = 'Bot di Messaggio';
$lang['new_template_bot'] = 'Nuovo Bot Modello';
$lang['new_message_bot'] = 'Nuovo Bot di Messaggio';
$lang['bot_name'] = 'Nome Bot';
$lang['reply_text'] = 'Testo di risposta <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Testo che sarà inviato al contatto o lead. Puoi anche usare {companyname}, {crm_url} o altri campi di unione personalizzati del lead o contatto, o usare il segno \'@\' per trovare i campi di unione disponibili" data-placement="bottom"></i> <span class="text-muted">(Numero massimo di caratteri consentiti: 1024)</span>';
$lang['reply_type'] = 'Tipo di risposta';
$lang['trigger'] = 'Attivatore';
$lang['header'] = 'Intestazione';
$lang['footer_bot'] = 'Footer <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Opzione 1: Bot con pulsanti di risposta';
$lang['bot_with_button_link'] = 'Opzione 2: Bot con link pulsante - URL CTA';
$lang['button1'] = 'Pulsante1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID Pulsante1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 256" data-placement="bottom"></i>';
$lang['button2'] = 'Pulsante2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID Pulsante2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 256" data-placement="bottom"></i>';
$lang['button3'] = 'Pulsante3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID Pulsante3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Nome Pulsante <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Numero massimo di caratteri consentiti: 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Link Pulsante';
$lang['enter_name'] = 'Inserisci Nome';
$lang['select_reply_type'] = 'Seleziona tipo di risposta';
$lang['enter_bot_reply_trigger'] = 'Inserisci attivatore di risposta bot';
$lang['enter_header'] = 'Inserisci intestazione';
$lang['enter_footer'] = 'Inserisci footer';
$lang['enter_button1'] = 'Inserisci pulsante1';
$lang['enter_button1_id'] = 'Inserisci ID pulsante1';
$lang['enter_button2'] = 'Inserisci pulsante2';
$lang['enter_button2_id'] = 'Inserisci ID pulsante2';
$lang['enter_button3'] = 'Inserisci pulsante3';
$lang['enter_button3_id'] = 'Inserisci ID pulsante3';
$lang['enter_button_name'] = 'Inserisci nome pulsante';
$lang['enter_button_url'] = 'Inserisci URL pulsante';
$lang['on_exact_match'] = 'Bot di risposta: Su corrispondenza esatta';
$lang['when_message_contains'] = 'Bot di risposta: Quando il messaggio contiene';
$lang['when_client_send_the_first_message'] = 'Risposta di benvenuto - quando il lead o il cliente invia il primo messaggio';
$lang['bot_create_successfully'] = 'Bot creato con successo';
$lang['bot_update_successfully'] = 'Bot aggiornato con successo';
$lang['bot_deleted_successfully'] = 'Bot eliminato con successo';
$lang['templates'] = 'Modelli';
$lang['template_data_loaded'] = 'Modelli caricati con successo';
$lang['load_templates'] = 'Carica Modelli';
$lang['template_management'] = 'Gestione Modelli';


// campaigns
$lang['campaign'] = 'Campagna';
$lang['campaigns'] = 'Campagne';
$lang['send_new_campaign'] = 'Invia Nuova Campagna';
$lang['campaign_name'] = 'Nome Campagna';
$lang['template'] = 'Template';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Per cliente, basato sul fuso orario del contatto" data-placement="left"></i>Ora di Invio Programmata';
$lang['scheduled_time_description'] = 'Per cliente, basato sul fuso orario del contatto';
$lang['ignore_scheduled_time_and_send_now'] = 'Ignora l\'ora programmata e invia ora';
$lang['template'] = 'Template';
$lang['leads'] = 'Lead';
$lang['delivered_to'] = 'Consegnato A';
$lang['read_by'] = 'Letto da';
$lang['variables'] = 'Variabili';
$lang['body'] = 'Corpo';
$lang['variable'] = 'Variabile';
$lang['match_with_selected_field'] = 'Corrispondenza con un campo selezionato';
$lang['preview'] = 'Anteprima';
$lang['send_campaign'] = 'Invia campagna';
$lang['send_to'] = 'Invia a';
$lang['send_campaign'] = 'Invia Campagna';
$lang['view_campaign'] = 'Visualizza Campagna';
$lang['campaign_daily_task'] = 'Compito giornaliero della campagna';
$lang['back'] = 'Indietro';
$lang['phone'] = 'Telefono';
$lang['message'] = 'Messaggio';
$lang['currently_type_not_supported'] = 'Attualmente il tipo di template <strong> %s </strong> non è supportato!';
$lang['of_your'] = 'dei tuoi ';
$lang['contacts'] = 'Contatti';
$lang['select_all_leads'] = 'Seleziona tutti i Lead';
$lang['select_all_note_leads'] = 'Se selezioni questo, tutti i futuri lead saranno inclusi in questa campagna.';
$lang['select_all_note_contacts'] = 'Se selezioni questo, tutti i futuri contatti saranno inclusi in questa campagna.';

$lang['verified_name'] = 'Nome Verificato';
$lang['mark_as_default'] = 'Segna come predefinito';
$lang['default_number_updated'] = 'ID numero di telefono predefinito aggiornato con successo';
$lang['currently_using_this_number'] = 'Attualmente in uso questo numero';
$lang['leads'] = 'Lead';
$lang['pause_campaign'] = 'Metti in pausa Campagna';
$lang['resume_campaign'] = 'Riprendi Campagna';
$lang['campaign_resumed'] = 'Campagna ripresa';
$lang['campaign_paused'] = 'Campagna in pausa';

//Template
$lang['body_data'] = 'Dati Corpo';
$lang['category'] = 'Categoria';

// Template bot
$lang['create_new_template_bot'] = 'Crea nuovo template bot';
$lang['template_bot'] = 'Template Bot';
$lang['variables'] = 'Variabili';
$lang['preview'] = 'Anteprima';
$lang['template'] = 'Template';
$lang['bot_content_1'] = 'Questo messaggio sarà inviato al contatto una volta che la regola di attivazione è soddisfatta nel messaggio inviato dal contatto.';
$lang['save_bot'] = 'Salva bot';
$lang['please_select_template'] = 'Si prega di selezionare un template';
$lang['use_manually_define_value'] = 'Usa valore definito manualmente';
$lang['merge_fields'] = 'Campi di Unione';
$lang['template_bot_create_successfully'] = 'Template bot creato con successo';
$lang['template_bot_update_successfully'] = 'Template bot aggiornato con successo';
$lang['text_bot'] = 'Bot di Testo';
$lang['option_2_bot_with_link'] = 'Opzione 2: Bot con link al pulsante - URL Chiamata all\'Azione (CTA)';
$lang['option_3_file'] = 'Opzione 3: Bot con file';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Messaggio inviato quando ci si aspetta un ritardo nella risposta';
$lang['bot_delay_response_placeholder'] = 'Dammi un momento, avrò la risposta a breve';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Tipo di Relazione';
$lang['select_all'] = 'Seleziona tutto';
$lang['total'] = 'Totale';
$lang['merge_field_note'] = 'Usa il segno \'@\' per aggiungere i campi di unione.';
$lang['send_to_all'] = 'Invia a Tutti ';
$lang['or'] = 'O';

$lang['convert_whatsapp_message_to_lead'] = 'Acquisisci Nuovo Lead Automaticamente (converti nuovi messaggi whatsapp in lead)';
$lang['leads_status'] = 'Stato dei Lead';
$lang['leads_assigned'] = 'Lead assegnati';
$lang['whatsapp_auto_lead'] = 'Lead Automatico Whatsapp';
$lang['webhooks_label'] = 'I dati ricevuti da Whatsapp verranno rinviati a';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Abilita Rinvio WebHooks';
$lang['chat'] = 'Chat';
$lang['black_listed_phone_numbers'] = 'Numeri di telefono bloccati';
$lang['sent_status'] = 'Stato Inviato';

$lang['active'] = 'Attivo';
$lang['approved'] = 'Approvato';
$lang['this_month'] = 'questo mese';
$lang['open_chats'] = 'Chat Aperte';
$lang['resolved_conversations'] = 'Conversazioni Risolte';
$lang['messages_sent'] = 'Messaggi inviati';
$lang['account_connected'] = 'Account connesso';
$lang['account_disconnected'] = 'Account disconnesso';
$lang['webhook_verify_token'] = 'Token di verifica Webhook';
// Chat integration
$lang['chat_message_note'] = 'Il messaggio verrà inviato a breve. Si prega di notare che se è un nuovo contatto, non apparirà in questo elenco finché il contatto non inizia a interagire con te!';

$lang['activity_log'] = 'Registro Attività';
$lang['whatsapp_logs'] = 'Log Whatsapp';
$lang['response_code'] = 'Codice di Risposta';
$lang['recorded_on'] = 'Registrato il';

$lang['request_details'] = 'Dettagli Richiesta';
$lang['raw_content'] = 'Contenuto Grezzo';
$lang['headers'] = 'Intestazioni';
$lang['format_type'] = 'Tipo di Formato';

// Permission section
$lang['show_campaign'] = 'Mostra campagna';
$lang['clear_log'] = 'Pulisci Registro';
$lang['log_activity'] = 'Attività di Registro';
$lang['load_template'] = 'Carica Template';

$lang['action'] = 'Azione';
$lang['total_parameters'] = 'Parametri Totali';
$lang['template_name'] = 'Nome Template';
$lang['log_cleared_successfully'] = 'Registro pulito con successo';
$lang['whatsbot_stats'] = 'Statistiche WhatsBot';

$lang['not_found_or_deleted'] = 'Non trovato o eliminato';
$lang['response'] = 'Risposta';

$lang['select_image'] = 'Seleziona immagine';
$lang['image'] = 'Immagine';
$lang['image_deleted_successfully'] = 'Immagine eliminata con successo';
$lang['whatsbot_settings'] = 'Impostazioni Whatsbot';
$lang['maximum_file_size_should_be'] = 'La dimensione massima del file dovrebbe essere ';
$lang['allowed_file_types'] = 'Tipi di file consentiti : ';

$lang['send_image'] = 'Invia Immagine';
$lang['send_video'] = 'Invia Video';
$lang['send_document'] = 'Invia Documento';
$lang['record_audio'] = 'Registra Audio';
$lang['chat_media_info'] = 'Maggiori informazioni per Content-Types Supportati & Post-Processing Media Size';
$lang['help'] = 'Aiuto';

// v1.1.0
$lang['clone'] = 'Clona';
$lang['bot_clone_successfully'] = 'Bot clonato con successo';
$lang['all_chat'] = 'Tutte le chat';
$lang['from'] = 'Da:';
$lang['phone_no'] = 'Numero di telefono:';
$lang['supportagent'] = 'Agente di supporto';
$lang['assign_chat_permission_to_support_agent'] = 'Assegna il permesso di chat all\'agente di supporto solo';
$lang['enable_whatsapp_notification_sound'] = 'Abilita il suono di notifica della chat di WhatsApp';
$lang['notification_sound'] = 'Suono di notifica';
$lang['trigger_keyword'] = 'Parola chiave di attivazione';
$lang['modal_title'] = 'Seleziona Agente di Supporto';
$lang['close_btn'] = 'Chiudi';
$lang['save_btn'] = 'Salva';
$lang['support_agent'] = 'Agente di supporto';
$lang['change_support_agent'] = 'Cambia agente di supporto';
$lang['replay_message'] = 'Non puoi inviare messaggi, sono passate 24 ore.';
$lang['support_agent_note'] = '<strong>Nota: </strong>Quando abiliti la funzione agente di supporto, l\'assegnatario del lead verrà automaticamente assegnato alla chat. Gli amministratori possono anche assegnare un nuovo agente dalla pagina chat.';
$lang['permission_bot_clone'] = 'Clona Bot';
$lang['remove_chat'] = 'Rimuovi chat';
$lang['default_message_on_no_match'] = 'Risposta predefinita - se nessuna parola chiave corrisponde';
$lang['default_message_note'] = '<strong>Nota: </strong>Abilitare questa opzione aumenterà il carico del tuo webhook. Per ulteriori informazioni visita questo <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">link</a>.';

$lang['whatsbot_connect_account'] = 'Collega l\'account Whatsbot';
$lang['whatsbot_message_bot'] = 'Whatsbot Messaggi Bot';
$lang['whatsbot_template_bot'] = 'Whatsbot Template Bot';
$lang['whatsbot_template'] = 'Whatsbot Template';
$lang['whatsbot_campaigns'] = 'Campagne Whatsbot';
$lang['whatsbot_chat'] = 'Chat Whatsbot';
$lang['whatsbot_log_activity'] = 'Attività di Log Whatsbot';
$lang['message_templates_not_exists_note'] = 'Permesso di modello meta mancante. Abilitalo nel tuo account Meta';

// v1.2.0
$lang['ai_prompt'] = 'AI Prompts';
$lang['ai_prompt_note'] = 'Per gli AI prompts, inserisci un messaggio per abilitare la funzione, o utilizza gli AI prompts se già abilitati';
$lang['emojis'] = 'Emoji';
$lang['translate'] = 'Traduci';
$lang['change_tone'] = 'Cambia tono';
$lang['professional'] = 'Professionale';
$lang['friendly'] = 'Amichevole';
$lang['empathetic'] = 'Empatico';
$lang['straightforward'] = 'Diretto';
$lang['simplify_language'] = 'Semplifica il linguaggio';
$lang['fix_spelling_and_grammar'] = 'Correggi ortografia e grammatica';

$lang['ai_integration'] = 'Integrazione AI';
$lang['open_ai_api'] = 'API OpenAI';
$lang['open_ai_secret_key'] = 'Chiave segreta OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Dove puoi trovare la chiave segreta?</a>';
$lang['chat_text_limit'] = 'Limite di testo della chat';
$lang['chat_text_limit_note'] = 'Per ottimizzare i costi operativi, considera di limitare il numero di parole delle risposte chat di OpenAI';
$lang['chat_model'] = 'Modello chat';
$lang['openai_organizations'] = 'Organizzazioni OpenAI';
$lang['template_type'] = 'Tipo di modello';
$lang['update'] = 'Aggiorna';
$lang['open_ai_key_verification_fail'] = 'Verifica della chiave OpenAI in attesa nelle impostazioni, si prega di collegare il proprio account OpenAI';
$lang['enable_wb_openai'] = 'Abilita OpenAI nella chat';
$lang['webhook_resend_method'] = 'Metodo di reinvio del webhook';
$lang['search_language'] = 'Cerca lingua...';
$lang['document'] = 'Documento';
$lang['select_document'] = 'Seleziona documento';
$lang['attchment_deleted_successfully'] = 'Allegato eliminato con successo';
$lang['attach_image_video_docs'] = 'Allega immagini, video e documenti';
$lang['choose_file_type'] = 'Scegli il tipo di file';
$lang['max_size'] = 'Dimensione massima: ';

// v1.3.0

// Importazione CSV
$lang['bulk_campaigns'] = 'Campagne di massa';
$lang['upload_csv'] = 'Carica CSV';
$lang['upload'] = 'Carica';
$lang['csv_uploaded_successfully'] = 'File CSV caricato con successo';
$lang['please_select_file'] = 'Seleziona il file CSV';
$lang['phonenumber_field_is_required'] = 'Il campo numero di telefono è obbligatorio';
$lang['out_of_the'] = 'Fuori da';
$lang['records_in_your_csv_file'] = 'record nel tuo file CSV,';
$lang['valid_the_campaign_can_be_sent'] = 'record sono validi.<br /> La campagna può essere inviata con successo a questi';
$lang['users'] = 'utenti';
$lang['campaigns_from_csv_file'] = 'Campagne da file CSV';
$lang['download_sample'] = 'Scarica esempio';
$lang['csv_rule_1'] = '1. <b>Requisito della colonna numero di telefono:</b> Il tuo file CSV deve includere una colonna chiamata "Phoneno". Ogni record in questa colonna deve contenere un numero di contatto valido, correttamente formattato con il prefisso internazionale, incluso il segno "+" . <br /><br />';
$lang['csv_rule_2'] = '2. <b>Formato e codifica CSV:</b> I dati CSV devono seguire il formato specificato. La prima riga del tuo file CSV deve contenere le intestazioni delle colonne, come mostrato nella tabella di esempio. Assicurati che il tuo file sia codificato in UTF-8 per evitare problemi di codifica.';
$lang['please_upload_valid_csv_file'] = 'Si prega di caricare un file CSV valido';
$lang['please_add_valid_number_in_csv_file'] = 'Si prega di aggiungere un numero <b>Phoneno</b> valido nel file CSV';
$lang['total_send_campaign_list'] = 'Campagna totale inviata: %s';
$lang['sample_data'] = 'Dati di esempio';
$lang['firstname'] = 'Nome';
$lang['lastname'] = 'Cognome';
$lang['phoneno'] = 'Phoneno';
$lang['email'] = 'Email';
$lang['country'] = 'Paese';
$lang['download_sample_and_read_rules'] = 'Scarica il file di esempio e leggi le regole';
$lang['please_wait_your_request_in_process'] = 'Attendi, la tua richiesta è attualmente in elaborazione.';
$lang['whatsbot_bulk_campaign'] = 'Campagne di massa Whatsbot';
$lang['csv_campaign'] = 'Campagna CSV';

// Risposta standard
$lang['canned_reply'] = 'Risposta predefinita';
$lang['canned_reply_menu'] = 'Risposta predefinita';
$lang['create_canned_reply'] = 'Crea risposta predefinita';
$lang['title'] = 'Titolo';
$lang['desc'] = 'Descrizione';
$lang['public'] = 'Pubblico';
$lang['action'] = 'Azione';
$lang['delete_successfully'] = 'Risposta eliminata.';
$lang['cannot_delete'] = 'La risposta non può essere eliminata.';
$lang['whatsbot_canned_reply'] = 'Risposta predefinita Whatsbot';
$lang['reply'] = 'Risposta';

//AI Prompts
$lang['ai_prompts'] = 'AI Prompts';
$lang['create_ai_prompts'] = 'Crea AI Prompts';
$lang['name'] = 'Nome';
$lang['action'] = 'Azione';
$lang['prompt_name'] = 'Nome prompt';
$lang['prompt_action'] = 'Azione prompt';
$lang['whatsbot_ai_prompts'] = 'AI Prompts Whatsbot';

// nuova chat
$lang['replying_to'] = 'Rispondendo a:';
$lang['download_document'] = 'Scarica documento';
$lang['custom_prompt'] = 'Prompt personalizzato';
$lang['canned_replies'] = 'Risposte predefinite';
$lang['use_@_to_add_merge_fields'] = 'Usa \'@\' per aggiungere campi di unione';
$lang['type_your_message'] = 'Scrivi il tuo messaggio';
$lang['you_cannot_send_a_message_using_this_number'] = 'Non puoi inviare un messaggio utilizzando questo numero.';

// flusso bot
$lang['bot_flow'] = 'Flusso Bot';
$lang['create_new_flow'] = 'Crea nuovo flusso';
$lang['flow_name'] = 'Nome del flusso';
$lang['flow'] = 'Flusso';
$lang['bot_flow_builder'] = 'Costruttore di flusso Bot';
$lang['you_can_not_upload_file_type'] = 'Non puoi caricare file di tipo <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Flusso Bot Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Cancellazione automatica della cronologia chat';
$lang['enable_auto_clear_chat_history'] = 'Abilita la cancellazione automatica della cronologia chat';
$lang['auto_clear_time'] = 'Tempo di cancellazione automatica della cronologia';
$lang['clear_chat_history_note'] = '<strong>Nota: </strong> Se abiliti la funzione di cancellazione automatica della cronologia chat, essa cancellerà automaticamente la cronologia della chat in base al numero di giorni specificato, ogni volta che viene eseguito il cron job.';
$lang['source'] = 'Fonte';
$lang['groups'] = 'Gruppi';

// v1.3.3
$lang['click_user_to_chat'] = 'Clicca sull\'utente per chattare';
$lang['searching'] = 'Ricerca in corso...';
$lang['filters'] = 'Filtri';
$lang['relation_type'] = 'Tipo di relazione';
$lang['groups'] = 'Gruppi';
$lang['source'] = 'Fonte';
$lang['status'] = 'Stato';
$lang['select_type'] = 'Seleziona tipo';
$lang['select_agents'] = 'Seleziona agenti';
$lang['select_group'] = 'Seleziona gruppo';
$lang['select_source'] = 'Seleziona sorgente';
$lang['select_status'] = 'Seleziona stato';
$lang['agents'] = 'Agenti';

// v1.4.2
$lang['read_only'] = 'Sola lettura';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
