{"name": "netflie/whatsapp-cloud-api", "description": "The first PHP SDK to send and receive messages using a cloud-hosted version of the WhatsApp Business Platform", "type": "library", "authors": [{"name": "Álex Albarca", "email": "<EMAIL>"}, {"name": "Álex Albarca", "email": "<EMAIL>"}], "license": "MIT", "require": {"php": "^7.4 || ^8.0 || ^8.1", "guzzlehttp/guzzle": "^7.0", "vlucas/phpdotenv": "^5.4", "myclabs/php-enum": "^1.8"}, "require-dev": {"phpunit/phpunit": "^9.0", "symfony/var-dumper": "^5.0", "phpspec/prophecy-phpunit": "^2.0", "fakerphp/faker": "^1.19", "friendsofphp/php-cs-fixer": "^3.13", "phpstan/phpstan": "^1.9 || ^2.0"}, "autoload": {"psr-4": {"Netflie\\WhatsAppCloudApi\\": "src"}}, "autoload-dev": {"psr-4": {"Netflie\\WhatsAppCloudApi\\Tests\\": "tests"}}, "scripts": {"unit-test": "vendor/bin/phpunit --group unit", "integration-test": "vendor/bin/phpunit --group integration"}}