<?php
/**
 * Auto-activation script for WhatsBot module
 * This script bypasses the Envato activation requirement
 */

defined('BASEPATH') || exit('No direct script access allowed');

function auto_activate_whatsbot() {
    $CI = &get_instance();
    $module_name = 'whatsbot';
    
    // Check if already activated
    if (!empty(get_option($module_name.'_verification_id'))) {
        return true; // Already activated
    }
    
    // Get module info
    $module = $CI->app_modules->get($module_name);
    if (!$module) {
        return false;
    }
    
    // Create fake verification data
    $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
    $fake_token = base64_encode(json_encode([
        'item_id' => basename($module['headers']['uri']),
        'buyer' => 'admin',
        'purchase_code' => 'fake_purchase_code',
        'check_interval' => 86400 // 24 hours
    ]));
    
    // Set the required options to simulate successful activation
    update_option($module_name.'_verification_id', base64_encode($fake_verification_id));
    update_option($module_name.'_last_verification', time());
    update_option($module_name.'_product_token', $fake_token);
    update_option($module_name.'_support_until_date', date('Y-m-d', strtotime('+10 years')));
    delete_option($module_name.'_heartbeat'); // Remove any failure heartbeat
    
    // Create license file
    $CI->load->helper('whatsbot/whatsbot');
    $chatOptions = set_chat_header();
    $content = (!empty($chatOptions['chat_header']) && !empty($chatOptions['chat_footer'])) ? hash_hmac('sha512', $chatOptions['chat_header'], $chatOptions['chat_footer']) : 'activated';
    write_file(TEMP_FOLDER . $chatOptions['chat_content'] . '.lic', $content);
    
    return true;
}

// Auto-activate if this file is included
if (function_exists('get_instance')) {
    auto_activate_whatsbot();
}
