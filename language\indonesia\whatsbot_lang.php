<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Sambungkan Akun';
$lang['connect_whatsapp_business'] = 'Sambungkan WhatsApp Business';
$lang['campaigning'] = 'Kampanye';
$lang['business_account_id_description'] = 'ID Akun Bisnis WhatsApp Anda (WABA)';
$lang['access_token_description'] = 'Token Akses Pengguna Anda setelah mendaftar di Portal Pengembang Facebook';
$lang['whatsapp_business_account_id'] = 'ID Akun Bisnis WhatsApp';
$lang['whatsapp_access_token'] = 'Token Akses WhatsApp';
$lang['webhook_callback_url'] = 'URL Callback Webhook';
$lang['verify_token'] = 'Verifikasi Token';
$lang['connect'] = 'Sambungkan';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Sambungan Akun Satu K<PERSON>';
$lang['connect_your_whatsapp_account'] = 'Sambungkan Akun WhatsApp Anda';
$lang['copy'] = 'Salin';
$lang['copied'] = 'Disalin!!';
$lang['disconnect'] = 'Putuskan Sambungan';
$lang['number'] = 'Nomor';
$lang['number_id'] = 'ID Nomor';
$lang['quality'] = 'Kualitas';
$lang['status'] = 'Status';
$lang['business_account_id'] = 'ID Akun Bisnis';
$lang['permissions'] = 'Izin';
$lang['phone_number_id_description'] = 'ID nomor telepon yang terhubung ke WhatsApp Business API. Jika Anda tidak yakin, Anda dapat menggunakan permintaan GET Phone Number ID untuk mengambilnya dari API WhatsApp ( https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers )';
$lang['phone_number_id'] = 'ID Nomor Telepon Terdaftar WhatsApp';
$lang['update_details'] = 'Perbarui Detail';

$lang['bots'] = 'Bot';
$lang['bots_management'] = 'Manajemen Bot';
$lang['create_template_base_bot'] = 'Buat bot berbasis template';
$lang['create_message_bot'] = 'Buat bot pesan';
$lang['type'] = 'Tipe';
$lang['message_bot'] = 'Bot Pesan';
$lang['new_template_bot'] = 'Bot Template Baru';
$lang['new_message_bot'] = 'Bot Pesan Baru';
$lang['bot_name'] = 'Nama Bot';
$lang['reply_text'] = 'Teks Balasan <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Teks yang akan dikirim ke prospek atau kontak. Anda juga dapat menggunakan {companyname}, {crm_url} atau bidang penggabungan kustom lainnya, atau gunakan tanda \'@\' untuk menemukan bidang penggabungan yang tersedia" data-placement="bottom"></i> <span class="text-muted">(Karakter maksimum yang diizinkan adalah 1024)</span>';
$lang['reply_type'] = 'Tipe Balasan';
$lang['trigger'] = 'Pemicu';
$lang['header'] = 'Header';
$lang['footer_bot'] = 'Footer <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Opsi 1: Bot dengan tombol balasan';
$lang['bot_with_button_link'] = 'Opsi 2: Bot dengan tautan tombol - URL CTA';
$lang['button1'] = 'Tombol1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID Tombol1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 256" data-placement="bottom"></i>';
$lang['button2'] = 'Tombol2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID Tombol2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 256" data-placement="bottom"></i>';
$lang['button3'] = 'Tombol3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID Tombol3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Nama Tombol <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Karakter maksimum yang diizinkan adalah 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Tautan Tombol';
$lang['enter_name'] = 'Masukkan Nama';
$lang['select_reply_type'] = 'Pilih tipe balasan';
$lang['enter_bot_reply_trigger'] = 'Masukkan pemicu balasan bot';
$lang['enter_header'] = 'Masukkan header';
$lang['enter_footer'] = 'Masukkan footer';
$lang['enter_button1'] = 'Masukkan tombol1';
$lang['enter_button1_id'] = 'Masukkan ID tombol1';
$lang['enter_button2'] = 'Masukkan tombol2';
$lang['enter_button2_id'] = 'Masukkan ID tombol2';
$lang['enter_button3'] = 'Masukkan tombol3';
$lang['enter_button3_id'] = 'Masukkan ID tombol3';
$lang['enter_button_name'] = 'Masukkan nama tombol';
$lang['enter_button_url'] = 'Masukkan URL tombol';
$lang['on_exact_match'] = 'Balasan bot: Pada kecocokan tepat';
$lang['when_message_contains'] = 'Balasan bot: Ketika pesan mengandung';
$lang['when_client_send_the_first_message'] = 'Balasan sambutan - ketika prospek atau klien mengirim pesan pertama';
$lang['bot_create_successfully'] = 'Bot berhasil dibuat';
$lang['bot_update_successfully'] = 'Bot berhasil diperbarui';
$lang['bot_deleted_successfully'] = 'Bot berhasil dihapus';
$lang['templates'] = 'Template';
$lang['template_data_loaded'] = 'Template berhasil dimuat';
$lang['load_templates'] = 'Muat Template';
$lang['template_management'] = 'Manajemen Template';


/// campaigns
$lang['campaign'] = 'Kampanye';
$lang['campaigns'] = 'Kampanye';
$lang['send_new_campaign'] = 'Kirim Kampanye Baru';
$lang['campaign_name'] = 'Nama Kampanye';
$lang['template'] = 'Template';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Per klien, berdasarkan zona waktu kontak" data-placement="left"></i>Waktu Kirim Terjadwal';
$lang['scheduled_time_description'] = 'Per klien, berdasarkan zona waktu kontak';
$lang['ignore_scheduled_time_and_send_now'] = 'Abaikan waktu terjadwal dan kirim sekarang';
$lang['template'] = 'Template';
$lang['leads'] = 'Prospek';
$lang['delivered_to'] = 'Dikirim ke';
$lang['read_by'] = 'Dibaca oleh';
$lang['variables'] = 'Variabel';
$lang['body'] = 'Isi';
$lang['variable'] = 'Variabel';
$lang['match_with_selected_field'] = 'Cocokkan dengan bidang yang dipilih';
$lang['preview'] = 'Pratampil';
$lang['send_campaign'] = 'Kirim kampanye';
$lang['send_to'] = 'Kirim ke';
$lang['send_campaign'] = 'Kirim Kampanye';
$lang['view_campaign'] = 'Lihat Kampanye';
$lang['campaign_daily_task'] = 'Tugas harian kampanye';
$lang['back'] = 'Kembali';
$lang['phone'] = 'Telepon';
$lang['message'] = 'Pesan';
$lang['currently_type_not_supported'] = 'Saat ini <strong> %s </strong> tipe template tidak didukung!';
$lang['of_your'] = 'dari anda ';
$lang['contacts'] = 'Kontak';
$lang['select_all_leads'] = 'Pilih semua Prospek';
$lang['select_all_note_leads'] = 'Jika Anda memilih ini, semua prospek mendatang akan termasuk dalam kampanye ini.';
$lang['select_all_note_contacts'] = 'Jika Anda memilih ini, semua kontak mendatang akan termasuk dalam kampanye ini.';

$lang['verified_name'] = 'Nama Terverifikasi';
$lang['mark_as_default'] = 'Tandai sebagai default';
$lang['default_number_updated'] = 'ID nomor telepon default diperbarui dengan sukses';
$lang['currently_using_this_number'] = 'Saat ini menggunakan nomor ini';
$lang['leads'] = 'Prospek';
$lang['pause_campaign'] = 'Jeda Kampanye';
$lang['resume_campaign'] = 'Lanjutkan Kampanye';
$lang['campaign_resumed'] = 'Kampanye dilanjutkan';
$lang['campaign_paused'] = 'Kampanye dijeda';

//Template
$lang['body_data'] = 'Data Isi';
$lang['category'] = 'Kategori';

// Template bot
$lang['create_new_template_bot'] = 'Buat bot template baru';
$lang['template_bot'] = 'Bot Template';
$lang['variables'] = 'Variabel';
$lang['preview'] = 'Pratampil';
$lang['template'] = 'Template';
$lang['bot_content_1'] = 'Pesan ini akan dikirim ke kontak setelah aturan pemicu terpenuhi dalam pesan yang dikirim oleh kontak.';
$lang['save_bot'] = 'Simpan bot';
$lang['please_select_template'] = 'Silakan pilih template';
$lang['use_manually_define_value'] = 'Gunakan nilai yang ditentukan secara manual';
$lang['merge_fields'] = 'Bidang Penggabungan';
$lang['template_bot_create_successfully'] = 'Bot template berhasil dibuat';
$lang['template_bot_update_successfully'] = 'Bot template berhasil diperbarui';
$lang['text_bot'] = 'Bot Teks';
$lang['option_2_bot_with_link'] = 'Opsi 2: Bot dengan tautan tombol - Panggilan untuk Bertindak (CTA) URL';
$lang['option_3_file'] = 'Opsi 3: Bot dengan file';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Pesan dikirim saat ada keterlambatan dalam respon yang diharapkan';
$lang['bot_delay_response_placeholder'] = 'Tunggu sebentar, saya akan segera memberikan jawaban';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Tipe Hubungan';
$lang['select_all'] = 'Pilih semua';
$lang['total'] = 'Total';
$lang['merge_field_note'] = 'Gunakan tanda \'@\' untuk menambahkan bidang penggabungan.';
$lang['send_to_all'] = 'Kirim ke Semua ';
$lang['or'] = 'ATAU';

$lang['convert_whatsapp_message_to_lead'] = 'Dapatkan Prospek Baru Secara Otomatis (ubah pesan whatsapp baru menjadi prospek)';
$lang['leads_status'] = 'Status Prospek';
$lang['leads_assigned'] = 'Prospek ditugaskan';
$lang['whatsapp_auto_lead'] = 'WhatsApp Auto Lead';
$lang['webhooks_label'] = 'Data yang diterima WhatsApp akan dikirim ulang ke';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Aktifkan Pengiriman Ulang WebHooks';
$lang['chat'] = 'Obrolan';
$lang['black_listed_phone_numbers'] = 'Nomor telepon yang diblacklist';
$lang['sent_status'] = 'Status Terkirim';

$lang['active'] = 'Aktif';
$lang['approved'] = 'Disetujui';
$lang['this_month'] = 'bulan ini';
$lang['open_chats'] = 'Obrolan Terbuka';
$lang['resolved_conversations'] = 'Percakapan yang Diselesaikan';
$lang['messages_sent'] = 'Pesan terkirim';
$lang['account_connected'] = 'Akun terhubung';
$lang['account_disconnected'] = 'Akun terputus';
$lang['webhook_verify_token'] = 'Token verifikasi webhook';
// Chat integration
$lang['chat_message_note'] = 'Pesan akan segera dikirim. Harap diperhatikan bahwa jika kontak baru, itu tidak akan muncul dalam daftar ini hingga kontak mulai berinteraksi dengan Anda!';

$lang['activity_log'] = 'Log Aktivitas';
$lang['whatsapp_logs'] = 'Log Whatsapp';
$lang['response_code'] = 'Kode Respon';
$lang['recorded_on'] = 'Dicapai Pada';

$lang['request_details'] = 'Detail Permintaan';
$lang['raw_content'] = 'Konten Mentah';
$lang['headers'] = 'Headers';
$lang['format_type'] = 'Tipe Format';

// Permission section
$lang['show_campaign'] = 'Tampilkan kampanye';
$lang['clear_log'] = 'Hapus Log';
$lang['log_activity'] = 'Log Aktivitas';
$lang['load_template'] = 'Muat Template';

$lang['action'] = 'Tindakan';
$lang['total_parameters'] = 'Total Parameter';
$lang['template_name'] = 'Nama Template';
$lang['log_cleared_successfully'] = 'Log berhasil dihapus';
$lang['whatsbot_stats'] = 'Statistik WhatsBot';

$lang['not_found_or_deleted'] = 'Tidak ditemukan atau dihapus';
$lang['response'] = 'Respon';

$lang['select_image'] = 'Pilih gambar';
$lang['image'] = 'Gambar';
$lang['image_deleted_successfully'] = 'Gambar dihapus dengan sukses';
$lang['whatsbot_settings'] = 'Pengaturan Whatsbot';
$lang['maximum_file_size_should_be'] = 'Ukuran file maksimum harus ';
$lang['allowed_file_types'] = 'Tipe file yang diizinkan: ';

$lang['send_image'] = 'Kirim Gambar';
$lang['send_video'] = 'Kirim Video';
$lang['send_document'] = 'Kirim Dokumen';
$lang['record_audio'] = 'Rekam Audio';
$lang['chat_media_info'] = 'Info lebih lanjut tentang Jenis Konten yang Didukung & Ukuran Media Pasca-Proses';
$lang['help'] = 'Bantuan';

// v1.1.0
$lang['clone'] = 'Clone';
$lang['bot_clone_successfully'] = 'Bot berhasil dicloning';
$lang['all_chat'] = 'Semua Obrolan';
$lang['from'] = 'Dari:';
$lang['phone_no'] = 'No Telepon:';
$lang['supportagent'] = 'Agen Dukungan';
$lang['assign_chat_permission_to_support_agent'] = 'Tetapkan izin obrolan hanya untuk agen dukungan';
$lang['enable_whatsapp_notification_sound'] = 'Aktifkan suara notifikasi obrolan WhatsApp';
$lang['notification_sound'] = 'Suara Notifikasi';
$lang['trigger_keyword'] = 'Kata Kunci Pemicu';
$lang['modal_title'] = 'Pilih Agen Dukungan';
$lang['close_btn'] = 'Tutup';
$lang['save_btn'] = 'Simpan';
$lang['support_agent'] = 'Agen Dukungan';
$lang['change_support_agent'] = 'Ubah Agen Dukungan';
$lang['replay_message'] = 'Anda tidak dapat mengirim pesan setelah 24 jam berlalu.';
$lang['support_agent_note'] = '<strong>Catatan: </strong>Ketika Anda mengaktifkan fitur agen dukungan, penugasan prospek akan secara otomatis ditetapkan pada obrolan. Admin juga dapat menetapkan agen baru dari halaman obrolan.';
$lang['permission_bot_clone'] = 'Clone Bot';
$lang['remove_chat'] = 'Hapus Obrolan';
$lang['default_message_on_no_match'] = 'Balasan Default - jika tidak ada kata kunci yang cocok';
$lang['default_message_note'] = '<strong>Catatan: </strong> Mengaktifkan opsi ini akan meningkatkan beban webhook Anda. Untuk info lebih lanjut kunjungi <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">tautan ini</a>.';

$lang['whatsbot_connect_account'] = 'Akun Koneksi Whatsbot';
$lang['whatsbot_message_bot'] = 'Bot Pesan Whatsbot';
$lang['whatsbot_template_bot'] = 'Bot Template Whatsbot';
$lang['whatsbot_template'] = 'Template Whatsbot';
$lang['whatsbot_campaigns'] = 'Kampanye Whatsbot';
$lang['whatsbot_chat'] = 'Obrolan Whatsbot';
$lang['whatsbot_log_activity'] = 'Aktivitas Log Whatsbot';
$lang['message_templates_not_exists_note'] = 'Izin template meta hilang. Harap aktifkan di akun Meta Anda';

// v1.2.0
$lang['ai_prompt'] = 'AI Prompts';
$lang['ai_prompt_note'] = 'Untuk AI prompts, silakan masukkan pesan untuk mengaktifkan fitur, atau gunakan AI prompts jika sudah diaktifkan';
$lang['emojis'] = 'Emojis';
$lang['translate'] = 'Terjemahkan';
$lang['change_tone'] = 'Ubah Nada';
$lang['professional'] = 'Profesional';
$lang['friendly'] = 'Ramah';
$lang['empathetic'] = 'Empatik';
$lang['straightforward'] = 'Langsung';
$lang['simplify_language'] = 'Sederhanakan Bahasa';
$lang['fix_spelling_and_grammar'] = 'Perbaiki Ejaan & Tata Bahasa';

$lang['ai_integration'] = 'Integrasi AI';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'Kunci Rahasia OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Di mana Anda dapat menemukan kunci rahasia?</a>';
$lang['chat_text_limit'] = 'Batas Teks Obrolan';
$lang['chat_text_limit_note'] = 'Untuk mengoptimalkan biaya operasional, pertimbangkan untuk membatasi jumlah kata dari respons obrolan OpenAI';
$lang['chat_model'] = 'Model Obrolan';
$lang['openai_organizations'] = 'Organisasi OpenAi';
$lang['template_type'] = 'Tipe Template';
$lang['update'] = 'Perbarui';
$lang['open_ai_key_verification_fail'] = 'Verifikasi Kunci OpenAi tertunda dari pengaturan, harap sambungkan akun OpenAI Anda';
$lang['enable_wb_openai'] = 'Aktifkan OpenAI dalam obrolan';
$lang['webhook_resend_method'] = 'Metode Pengiriman Ulang Webhook';
$lang['search_language'] = 'Cari bahasa...';
$lang['document'] = 'Dokumen';
$lang['select_document'] = 'Pilih Dokumen';
$lang['attchment_deleted_successfully'] = 'Lampiran Berhasil Dihapus';
$lang['attach_image_video_docs'] = 'Lampirkan Gambar Video Dokumen';
$lang['choose_file_type'] = 'Pilih Tipe File';
$lang['max_size'] = 'Ukuran Maks: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Kampanye Massal';
$lang['upload_csv'] = 'Unggah CSV';
$lang['upload'] = 'Unggah';
$lang['csv_uploaded_successfully'] = 'File CSV Berhasil Diunggah';
$lang['please_select_file'] = 'Harap Pilih File CSV';
$lang['phonenumber_field_is_required'] = 'Kolom nomor telepon diperlukan';
$lang['out_of_the'] = 'Dari';
$lang['records_in_your_csv_file'] = 'catatan dalam file CSV Anda,';
$lang['valid_the_campaign_can_be_sent'] = 'catatan valid.<br /> Kampanye dapat berhasil dikirim ke ini';
$lang['users'] = 'pengguna';
$lang['campaigns_from_csv_file'] = 'Kampanye dari File CSV';
$lang['download_sample'] = 'Unduh Contoh';
$lang['csv_rule_1'] = '1. <b>Kewajiban Kolom Nomor Telepon:</b> File CSV Anda harus mencakup kolom bernama "Phoneno." Setiap catatan dalam kolom ini harus berisi nomor kontak yang valid, diformat dengan benar dengan kode negara, termasuk tanda "+" . <br /><br />';
$lang['csv_rule_2'] = '2. <b>Format dan Encoding CSV:</b> Data CSV Anda harus mengikuti format yang ditentukan. Baris pertama dari file CSV Anda harus berisi header kolom, seperti yang ditunjukkan dalam tabel contoh. Pastikan file Anda dikodekan dalam UTF-8 untuk mencegah masalah pengkodean.';
$lang['please_upload_valid_csv_file'] = 'Harap unggah file CSV yang valid';
$lang['please_add_valid_number_in_csv_file'] = 'Harap tambahkan <b>Phoneno</b> yang valid dalam file CSV';
$lang['total_send_campaign_list'] = 'Total kirim kampanye: %s';
$lang['sample_data'] = 'Data Contoh';
$lang['firstname'] = 'Nama Depan';
$lang['lastname'] = 'Nama Belakang';
$lang['phoneno'] = 'Phoneno';
$lang['email'] = 'Email';
$lang['country'] = 'Negara';
$lang['download_sample_and_read_rules'] = 'Unduh File Contoh & Baca Aturan';
$lang['please_wait_your_request_in_process'] = 'Silakan tunggu, permintaan Anda sedang diproses.';
$lang['whatsbot_bulk_campaign'] = 'Kampanye Massal Whatsbot';
$lang['csv_campaign'] = 'Kampanye CSV';

// Canned reply
$lang['canned_reply'] = 'Balasan Siap Pakai';
$lang['canned_reply_menu'] = 'Balasan Siap Pakai';
$lang['create_canned_reply'] = 'Buat Balasan Siap Pakai';
$lang['title'] = 'Judul';
$lang['desc'] = 'Deskripsi';
$lang['public'] = 'Publik';
$lang['action'] = 'Aksi';
$lang['delete_successfully'] = 'Balasan dihapus.';
$lang['cannot_delete'] = 'Balasan tidak dapat dihapus.';
$lang['whatsbot_canned_reply'] = 'Balasan Siap Pakai Whatsbot';
$lang['reply'] = 'Balasan';

//AI Prompts
$lang['ai_prompts'] = 'AI Prompts';
$lang['create_ai_prompts'] = 'Buat AI Prompts';
$lang['name'] = 'Nama';
$lang['action'] = 'Aksi';
$lang['prompt_name'] = 'Nama Prompt';
$lang['prompt_action'] = 'Aksi Prompt';
$lang['whatsbot_ai_prompts'] = 'AI Prompts Whatsbot';

// new chat
$lang['replying_to'] = 'Membalas :';
$lang['download_document'] = 'Unduh Dokumen';
$lang['custom_prompt'] = 'Prompt Kustom';
$lang['canned_replies'] = 'Balasan Siap Pakai';
$lang['use_@_to_add_merge_fields'] = 'Gunakan \'@\' untuk menambahkan merge fields';
$lang['type_your_message'] = 'Ketik pesan Anda';
$lang['you_cannot_send_a_message_using_this_number'] = 'Anda tidak dapat mengirim pesan menggunakan nomor ini.';

// bot flow
$lang['bot_flow'] = 'Alur Bot';
$lang['create_new_flow'] = 'Buat Alur Baru';
$lang['flow_name'] = 'Nama Alur';
$lang['flow'] = 'Alur';
$lang['bot_flow_builder'] = 'Pembuat Alur Bot';
$lang['you_can_not_upload_file_type'] = 'Anda tidak dapat mengunggah file tipe <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Alur Bot Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Hapus Riwayat Obrolan Otomatis';
$lang['enable_auto_clear_chat_history'] = 'Aktifkan Hapus Riwayat Obrolan Otomatis';
$lang['auto_clear_time'] = 'Waktu Hapus Riwayat';
$lang['clear_chat_history_note'] = '<strong>Catatan: </strong> Jika Anda mengaktifkan fitur hapus riwayat obrolan otomatis, riwayat obrolan akan secara otomatis dihapus berdasarkan jumlah hari yang Anda tentukan, setiap kali pekerjaan cron berjalan.';
$lang['source'] = 'Sumber';
$lang['groups'] = 'Grup';


// v1.3.3
$lang['click_user_to_chat'] = 'Klik Pengguna untuk Mengobrol';
$lang['searching'] = 'Sedang mencari...';
$lang['filters'] = 'Filter';
$lang['relation_type'] = 'Jenis Hubungan';
$lang['groups'] = 'Grup';
$lang['source'] = 'Sumber';
$lang['status'] = 'Status';
$lang['select_type'] = 'Pilih Tipe';
$lang['select_agents'] = 'Pilih Agen';
$lang['select_group'] = 'Pilih Grup';
$lang['select_source'] = 'Pilih Sumber';
$lang['select_status'] = 'Pilih Status';
$lang['agents'] = 'Agen';

// v1.4.2
$lang['read_only'] = 'Hanya Baca';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
