<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Σύνδεση Λογαριασμού';
$lang['connect_whatsapp_business'] = 'Σύνδεση Whatsapp Business';
$lang['campaigning'] = 'Καμπάνια';
$lang['business_account_id_description'] = 'ID του Λογαριασμού WhatsApp Business (WABA)';
$lang['access_token_description'] = 'Το User Access Token σας αφού εγγραφείτε σε λογαριασμό στο Facebook Developers Portal';
$lang['whatsapp_business_account_id'] = 'ID Λογαριασμού Whatsapp Business';
$lang['whatsapp_access_token'] = 'Token πρόσβασης Whatsapp';
$lang['webhook_callback_url'] = 'URL Επιστροφής Webhook';
$lang['verify_token'] = 'Επιβεβαίωση Token';
$lang['connect'] = 'Σύνδεση';
$lang['whatsapp'] = 'Whatsapp';
$lang['one_click_account_connection'] = 'Σύνδεση Λογαριασμού με Ένα Κλικ';
$lang['connect_your_whatsapp_account'] = 'Συνδέστε τον Λογαριασμό σας στο Whatsapp';
$lang['copy'] = 'Αντιγραφή';
$lang['copied'] = 'Αντιγράφηκε!!';
$lang['disconnect'] = 'Αποσύνδεση';
$lang['number'] = 'Αριθμός';
$lang['number_id'] = 'ID Αριθμού';
$lang['quality'] = 'Ποιότητα';
$lang['status'] = 'Κατάσταση';
$lang['business_account_id'] = 'ID Επιχειρηματικού Λογαριασμού';
$lang['permissions'] = 'Άδειες';
$lang['phone_number_id_description'] = 'ID του τηλεφώνου που είναι συνδεδεμένο με το WhatsApp Business API. Εάν δεν είστε σίγουροι, μπορείτε να χρησιμοποιήσετε ένα GET Phone Number ID request για να το ανακτήσετε από το WhatsApp API ( https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers )';
$lang['phone_number_id'] = 'ID Αριθμού του Καταχωρημένου Τηλεφώνου στο WhatsApp';
$lang['update_details'] = 'Ενημέρωση Λεπτομερειών';

$lang['bots'] = 'Bots';
$lang['bots_management'] = 'Διαχείριση Bots';
$lang['create_template_base_bot'] = 'Δημιουργία bot βάσης προτύπου';
$lang['create_message_bot'] = 'Δημιουργία bot μηνύματος';
$lang['type'] = 'Τύπος';
$lang['message_bot'] = 'Bot Μηνύματος';
$lang['new_template_bot'] = 'Νέο Bot Προτύπου';
$lang['new_message_bot'] = 'Νέο Bot Μηνύματος';
$lang['bot_name'] = 'Όνομα Bot';
$lang['reply_text'] = 'Κείμενο απάντησης <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Κείμενο που θα σταλεί στον ενδιαφερόμενο ή επαφή. Μπορείτε επίσης να χρησιμοποιήσετε {companyname}, {crm_url} ή οποιαδήποτε άλλη προσαρμοσμένη πεδίο συγχώνευσης του ενδιαφερόμενου ή επαφής, ή να χρησιμοποιήσετε το σύμβολο \'@\' για να βρείτε διαθέσιμα πεδία συγχώνευσης" data-placement="bottom"></i> <span class="text-muted">(Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 1024)</span>';
$lang['reply_type'] = 'Τύπος απάντησης';
$lang['trigger'] = 'Ενεργοποίηση';
$lang['header'] = 'Κεφαλίδα';
$lang['footer_bot'] = 'Υποσέλιδο <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Επιλογή 1: Bot με κουμπιά απάντησης';
$lang['bot_with_button_link'] = 'Επιλογή 2: Bot με σύνδεσμο κουμπιού - CTA URL';
$lang['button1'] = 'Κουμπί 1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID Κουμπιού 1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 256" data-placement="bottom"></i>';
$lang['button2'] = 'Κουμπί 2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID Κουμπιού 2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 256" data-placement="bottom"></i>';
$lang['button3'] = 'Κουμπί 3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID Κουμπιού 3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Όνομα Κουμπιού <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Το μέγιστο επιτρεπόμενο πλήθος χαρακτήρων είναι 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Σύνδεσμος Κουμπιού';
$lang['enter_name'] = 'Εισάγετε Όνομα';
$lang['select_reply_type'] = 'Επιλέξτε τύπο απάντησης';
$lang['enter_bot_reply_trigger'] = 'Εισάγετε το trigger απάντησης του bot';
$lang['enter_header'] = 'Εισάγετε κεφαλίδα';
$lang['enter_footer'] = 'Εισάγετε υποσέλιδο';
$lang['enter_button1'] = 'Εισάγετε κουμπί 1';
$lang['enter_button1_id'] = 'Εισάγετε ID κουμπιού 1';
$lang['enter_button2'] = 'Εισάγετε κουμπί 2';
$lang['enter_button2_id'] = 'Εισάγετε ID κουμπιού 2';
$lang['enter_button3'] = 'Εισάγετε κουμπί 3';
$lang['enter_button3_id'] = 'Εισάγετε ID κουμπιού 3';
$lang['enter_button_name'] = 'Εισάγετε όνομα κουμπιού';
$lang['enter_button_url'] = 'Εισάγετε URL κουμπιού';
$lang['on_exact_match'] = 'Bot απάντησης: Σε ακριβή ταυτοποίηση';
$lang['when_message_contains'] = 'Bot απάντησης: Όταν το μήνυμα περιέχει';
$lang['when_client_send_the_first_message'] = 'Καλωσόρισμα απάντησης - όταν ο ενδιαφερόμενος ή ο πελάτης στείλει το πρώτο μήνυμα';
$lang['bot_create_successfully'] = 'Το bot δημιουργήθηκε με επιτυχία';
$lang['bot_update_successfully'] = 'Το bot ενημερώθηκε με επιτυχία';
$lang['bot_deleted_successfully'] = 'Το bot διαγράφηκε με επιτυχία';
$lang['templates'] = 'Πρότυπα';
$lang['template_data_loaded'] = 'Τα πρότυπα φορτώθηκαν με επιτυχία';
$lang['load_templates'] = 'Φόρτωση Προτύπων';
$lang['template_management'] = 'Διαχείριση Προτύπων';


// campaigns
$lang['campaign'] = 'Καμπάνια';
$lang['campaigns'] = 'Καμπάνιες';
$lang['send_new_campaign'] = 'Αποστολή νέας καμπάνιας';
$lang['campaign_name'] = 'Όνομα καμπάνιας';
$lang['template'] = 'Πρότυπο';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Ανά πελάτη, με βάση τη ζώνη ώρας της επαφής" data-placement="left"></i>Προγραμματισμένος χρόνος αποστολής';
$lang['scheduled_time_description'] = 'Ανά πελάτη, με βάση τη ζώνη ώρας της επαφής';
$lang['ignore_scheduled_time_and_send_now'] = 'Αγνοήστε τον προγραμματισμένο χρόνο και στείλτε τώρα';
$lang['template'] = 'Πρότυπο';
$lang['leads'] = 'Δυνητικοί πελάτες';
$lang['delivered_to'] = 'Παραδόθηκε σε';
$lang['read_by'] = 'Διαβάστηκε από';
$lang['variables'] = 'Μεταβλητές';
$lang['body'] = 'Σώμα';
$lang['variable'] = 'Μεταβλητή';
$lang['match_with_selected_field'] = 'Ταίριασμα με επιλεγμένο πεδίο';
$lang['preview'] = 'Προεπισκόπηση';
$lang['send_campaign'] = 'Αποστολή καμπάνιας';
$lang['send_to'] = 'Αποστολή σε';
$lang['send_campaign'] = 'Αποστολή καμπάνιας';
$lang['view_campaign'] = 'Προβολή καμπάνιας';
$lang['campaign_daily_task'] = 'Καθημερινή εργασία καμπάνιας';
$lang['back'] = 'Πίσω';
$lang['phone'] = 'Τηλέφωνο';
$lang['message'] = 'Μήνυμα';
$lang['currently_type_not_supported'] = 'Αυτή τη στιγμή ο τύπος προτύπου <strong> %s </strong> δεν υποστηρίζεται!';
$lang['of_your'] = 'των δικών σας ';
$lang['contacts'] = 'Επαφές';
$lang['select_all_leads'] = 'Επιλογή όλων των δυνητικών πελατών';
$lang['select_all_note_leads'] = 'Αν επιλέξετε αυτό, όλοι οι μελλοντικοί δυνητικοί πελάτες περιλαμβάνονται σε αυτή την καμπάνια.';
$lang['select_all_note_contacts'] = 'Αν επιλέξετε αυτό, όλες οι μελλοντικές επαφές περιλαμβάνονται σε αυτή την καμπάνια.';

$lang['verified_name'] = 'Επιβεβαιωμένο όνομα';
$lang['mark_as_default'] = 'Ορισμός ως προεπιλεγμένο';
$lang['default_number_updated'] = 'Η προεπιλεγμένη αναγνωριστική αριθμού ενημερώθηκε με επιτυχία';
$lang['currently_using_this_number'] = 'Χρησιμοποιείτε αυτόν τον αριθμό';
$lang['leads'] = 'Δυνητικοί πελάτες';
$lang['pause_campaign'] = 'Παύση καμπάνιας';
$lang['resume_campaign'] = 'Συνέχιση καμπάνιας';
$lang['campaign_resumed'] = 'Η καμπάνια συνεχίζεται';
$lang['campaign_paused'] = 'Η καμπάνια έχει σταματήσει';

//Template
$lang['body_data'] = 'Δεδομένα σώματος';
$lang['category'] = 'Κατηγορία';

// Template bot
$lang['create_new_template_bot'] = 'Δημιουργία νέου προτύπου bot';
$lang['template_bot'] = 'Πρότυπο Bot';
$lang['variables'] = 'Μεταβλητές';
$lang['preview'] = 'Προεπισκόπηση';
$lang['template'] = 'Πρότυπο';
$lang['bot_content_1'] = 'Αυτό το μήνυμα θα αποσταλεί στην επαφή μόλις πληρωθεί ο κανόνας ενεργοποίησης στο μήνυμα που αποστέλλεται από την επαφή.';
$lang['save_bot'] = 'Αποθήκευση bot';
$lang['please_select_template'] = 'Παρακαλώ επιλέξτε ένα πρότυπο';
$lang['use_manually_define_value'] = 'Χρησιμοποιήστε χειροκίνητα καθορισμένη τιμή';
$lang['merge_fields'] = 'Συγχώνευση πεδίων';
$lang['template_bot_create_successfully'] = 'Το πρότυπο bot δημιουργήθηκε με επιτυχία';
$lang['template_bot_update_successfully'] = 'Το πρότυπο bot ενημερώθηκε με επιτυχία';
$lang['text_bot'] = 'Bot κειμένου';
$lang['option_2_bot_with_link'] = 'Επιλογή 2: Bot με σύνδεσμο κουμπιού - Κλήση σε δράση (CTA) URL';
$lang['option_3_file'] = 'Επιλογή 3: Bot με αρχείο';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Μήνυμα που αποστέλλεται όταν αναμένεται καθυστέρηση στην απάντηση';
$lang['bot_delay_response_placeholder'] = 'Δώστε μου μια στιγμή, θα έχω την απάντηση σύντομα';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Τύπος σχέσης';
$lang['select_all'] = 'Επιλογή όλων';
$lang['total'] = 'Σύνολο';
$lang['merge_field_note'] = 'Χρησιμοποιήστε το σύμβολο \'@\' για να προσθέσετε συγχωνευμένα πεδία.';
$lang['send_to_all'] = 'Αποστολή σε όλους ';
$lang['or'] = 'Ή';

$lang['convert_whatsapp_message_to_lead'] = 'Αυτοματοποιημένη απόκτηση νέου δυνητικού πελάτη (μετατροπή νέων μηνυμάτων WhatsApp σε δυνητικούς πελάτες)';
$lang['leads_status'] = 'Κατάσταση δυνητικού πελάτη';
$lang['leads_assigned'] = 'Δυνητικός πελάτης ανατεθεί';
$lang['whatsapp_auto_lead'] = 'Αυτόματος δυνητικός πελάτης WhatsApp';
$lang['webhooks_label'] = 'Τα δεδομένα που λαμβάνονται από το WhatsApp θα επανασταλούν σε';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Ενεργοποίηση επαναποστολής WebHooks';
$lang['chat'] = 'Συνομιλία';
$lang['black_listed_phone_numbers'] = 'Μαύρη λίστα τηλεφωνικών αριθμών';
$lang['sent_status'] = 'Κατάσταση αποστολής';

$lang['active'] = 'Ενεργό';
$lang['approved'] = 'Εγκεκριμένο';
$lang['this_month'] = 'αυτόν τον μήνα';
$lang['open_chats'] = 'Ανοιχτές συνομιλίες';
$lang['resolved_conversations'] = 'Λυμένες συνομιλίες';
$lang['messages_sent'] = 'Απεσταλμένα μηνύματα';
$lang['account_connected'] = 'Ο λογαριασμός συνδεδεμένος';
$lang['account_disconnected'] = 'Ο λογαριασμός αποσυνδεδεμένος';
$lang['webhook_verify_token'] = 'Token επιβεβαίωσης Webhook';
// Chat integration
$lang['chat_message_note'] = 'Το μήνυμα θα αποσταλεί σύντομα. Σημειώστε ότι αν είναι νέα επαφή, δεν θα εμφανίζεται σε αυτή τη λίστα μέχρι να αρχίσει η επαφή να αλληλεπιδρά μαζί σας!';

$lang['activity_log'] = 'Καταγραφή δραστηριότητας';
$lang['whatsapp_logs'] = 'Καταγραφές WhatsApp';
$lang['response_code'] = 'Κωδικός απάντησης';
$lang['recorded_on'] = 'Καταχωρήθηκε στις';

$lang['request_details'] = 'Λεπτομέρειες αιτήματος';
$lang['raw_content'] = 'Ακατέργαστο περιεχόμενο';
$lang['headers'] = 'Κεφαλίδες';
$lang['format_type'] = 'Τύπος μορφής';

// Permission section
$lang['show_campaign'] = 'Εμφάνιση καμπάνιας';
$lang['clear_log'] = 'Εκκαθάριση καταγραφής';
$lang['log_activity'] = 'Καταγραφή δραστηριότητας';
$lang['load_template'] = 'Φόρτωση προτύπου';

$lang['action'] = 'Δράση';
$lang['total_parameters'] = 'Συνολικές παράμετροι';
$lang['template_name'] = 'Όνομα προτύπου';
$lang['log_cleared_successfully'] = 'Η καταγραφή εκκαθαρίστηκε με επιτυχία';
$lang['whatsbot_stats'] = 'Στατιστικά WhatsBot';

$lang['not_found_or_deleted'] = 'Δεν βρέθηκε ή διαγράφηκε';
$lang['response'] = 'Απάντηση';

$lang['select_image'] = 'Επιλέξτε εικόνα';
$lang['image'] = 'Εικόνα';
$lang['image_deleted_successfully'] = 'Η εικόνα διαγράφηκε με επιτυχία';
$lang['whatsbot_settings'] = 'Ρυθμίσεις Whatsbot';
$lang['maximum_file_size_should_be'] = 'Το μέγιστο μέγεθος αρχείου θα πρέπει να είναι ';
$lang['allowed_file_types'] = 'Επιτρεπόμενοι τύποι αρχείων: ';

$lang['send_image'] = 'Αποστολή εικόνας';
$lang['send_video'] = 'Αποστολή βίντεο';
$lang['send_document'] = 'Αποστολή εγγράφου';
$lang['record_audio'] = 'Ηχογράφηση ήχου';
$lang['chat_media_info'] = 'Περισσότερες πληροφορίες για τις υποστηριζόμενες μορφές περιεχομένου και το μέγεθος επεξεργασίας μέσων';
$lang['help'] = 'Βοήθεια';


// v1.1.0
$lang['clone'] = 'Αντιγραφή';
$lang['bot_clone_successfully'] = 'Ο bot αντιγράφηκε με επιτυχία';
$lang['all_chat'] = 'Όλες οι Συνομιλίες';
$lang['from'] = 'Από:';
$lang['phone_no'] = 'Τηλέφωνο:';
$lang['supportagent'] = 'Πράκτορας Υποστήριξης';
$lang['assign_chat_permission_to_support_agent'] = 'Ανάθεση δικαιώματος συνομιλίας μόνο σε πράκτορα υποστήριξης';
$lang['enable_whatsapp_notification_sound'] = 'Ενεργοποίηση ήχου ειδοποίησης WhatsApp';
$lang['notification_sound'] = 'Ήχος ειδοποίησης';
$lang['trigger_keyword'] = 'Λέξη-κλειδί ενεργοποίησης';
$lang['modal_title'] = 'Επιλογή Πράκτορα Υποστήριξης';
$lang['close_btn'] = 'Κλείσιμο';
$lang['save_btn'] = 'Αποθήκευση';
$lang['support_agent'] = 'Πράκτορας Υποστήριξης';
$lang['change_support_agent'] = 'Αλλαγή Πράκτορα Υποστήριξης';
$lang['replay_message'] = 'Δεν μπορείτε να στείλετε μήνυμα, έχει περάσει 24 ώρες.';
$lang['support_agent_note'] = '<strong>Σημείωση: </strong>Όταν ενεργοποιήσετε τη δυνατότητα πράκτορα υποστήριξης, ο ανατεθείς δυνητικός πελάτης θα ανατεθεί αυτόματα στη συνομιλία. Οι διαχειριστές μπορούν επίσης να αναθέσουν έναν νέο πράκτορα από τη σελίδα συνομιλίας.';
$lang['permission_bot_clone'] = 'Αντιγραφή Bot';
$lang['remove_chat'] = 'Αφαίρεση Συνομιλίας';
$lang['default_message_on_no_match'] = 'Προκαθορισμένη Απάντηση - αν καμία λέξη-κλειδί δεν ταιριάζει';
$lang['default_message_note'] = '<strong>Σημείωση: </strong>Η ενεργοποίηση αυτής της επιλογής θα αυξήσει το φορτίο του webhook σας. Για περισσότερες πληροφορίες επισκεφθείτε αυτό το <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">σύνδεσμο</a>.';

$lang['whatsbot_connect_account'] = 'Σύνδεση Λογαριασμού Whatsbot';
$lang['whatsbot_message_bot'] = 'Bot Μηνυμάτων Whatsbot';
$lang['whatsbot_template_bot'] = 'Bot Προτύπων Whatsbot';
$lang['whatsbot_template'] = 'Πρότυπο Whatsbot';
$lang['whatsbot_campaigns'] = 'Καμπάνιες Whatsbot';
$lang['whatsbot_chat'] = 'Συνομιλία Whatsbot';
$lang['whatsbot_log_activity'] = 'Καταγραφή Δραστηριότητας Whatsbot';
$lang['message_templates_not_exists_note'] = 'Λείπει άδεια για το πρότυπο meta. Παρακαλώ ενεργοποιήστε το στο λογαριασμό σας Meta.';

// v1.2.0
$lang['ai_prompt'] = 'AI Υποδείξεις';
$lang['ai_prompt_note'] = 'Για τις υποδείξεις AI, παρακαλώ εισάγετε ένα μήνυμα για να ενεργοποιήσετε τη δυνατότητα ή χρησιμοποιήστε τις υποδείξεις AI αν είναι ήδη ενεργοποιημένες.';
$lang['emojis'] = 'Εικόνες';
$lang['translate'] = 'Μετάφραση';
$lang['change_tone'] = 'Αλλαγή Τόνου';
$lang['professional'] = 'Επαγγελματικός';
$lang['friendly'] = 'Φιλικός';
$lang['empathetic'] = 'Εμπαθητικός';
$lang['straightforward'] = 'Άμεσος';
$lang['simplify_language'] = 'Απλοποίηση Γλώσσας';
$lang['fix_spelling_and_grammar'] = 'Διόρθωση Ορθογραφίας & Γραμματικής';

$lang['ai_integration'] = 'Ενσωμάτωση AI';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'Κωδικός μυστικού OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Πού μπορείτε να βρείτε τον μυστικό κωδικό;</a>';
$lang['chat_text_limit'] = 'Όριο Κειμένου Συνομιλίας';
$lang['chat_text_limit_note'] = 'Για να βελτιστοποιήσετε τα λειτουργικά κόστη, σκεφτείτε να περιορίσετε τον αριθμό λέξεων των απαντήσεων συνομιλίας του OpenAI.';
$lang['chat_model'] = 'Μοντέλο Συνομιλίας';
$lang['openai_organizations'] = 'Οργανισμοί OpenAI';
$lang['template_type'] = 'Τύπος Προτύπου';
$lang['update'] = 'Ενημέρωση';
$lang['open_ai_key_verification_fail'] = 'Η επαλήθευση του κωδικού OpenAI εκκρεμεί από τις ρυθμίσεις, παρακαλώ συνδέστε τον λογαριασμό σας openai';
$lang['enable_wb_openai'] = 'Ενεργοποίηση OpenAI στη συνομιλία';
$lang['webhook_resend_method'] = 'Μέθοδος Επαναποστολής Webhook';
$lang['search_language'] = 'Γλώσσα αναζήτησης...';
$lang['document'] = 'Έγγραφο';
$lang['select_document'] = 'Επιλέξτε Έγγραφο';
$lang['attchment_deleted_successfully'] = 'Η συνημμένη διαγράφηκε με επιτυχία';
$lang['attach_image_video_docs'] = 'Επισύναψη Εικόνας, Βίντεο, Εγγράφων';
$lang['choose_file_type'] = 'Επιλέξτε Τύπο Αρχείου';
$lang['max_size'] = 'Μέγιστο Μέγεθος: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Μαζικές Καμπάνιες';
$lang['upload_csv'] = 'Μεταφόρτωση CSV';
$lang['upload'] = 'Μεταφόρτωση';
$lang['csv_uploaded_successfully'] = 'Το αρχείο CSV μεταφορτώθηκε με επιτυχία';
$lang['please_select_file'] = 'Παρακαλώ επιλέξτε αρχείο CSV';
$lang['phonenumber_field_is_required'] = 'Το πεδίο τηλεφώνου είναι υποχρεωτικό';
$lang['out_of_the'] = 'Από το';
$lang['records_in_your_csv_file'] = 'αρχεία στο αρχείο CSV σας,';
$lang['valid_the_campaign_can_be_sent'] = 'αρχεία είναι έγκυρα.<br /> Η καμπάνια μπορεί να σταλεί επιτυχώς σε αυτούς τους';
$lang['users'] = 'χρήστες';
$lang['campaigns_from_csv_file'] = 'Καμπάνιες από Αρχείο CSV';
$lang['download_sample'] = 'Λήψη Δείγματος';
$lang['csv_rule_1'] = '1. <b>Απαιτήσεις Στήλης Τηλεφώνου:</b> Το αρχείο CSV σας πρέπει να περιλαμβάνει μια στήλη με το όνομα "Phoneno". Κάθε εγγραφή σε αυτή τη στήλη πρέπει να περιέχει έναν έγκυρο αριθμό επαφής, σωστά μορφοποιημένο με τον κωδικό χώρας, συμπεριλαμβανομένου του "+" σημεία. <br /><br />';
$lang['csv_rule_2'] = '2. <b>Μορφή και Κωδικοποίηση CSV:</b> Τα δεδομένα CSV σας πρέπει να ακολουθούν την καθορισμένη μορφή. Η πρώτη γραμμή του αρχείου CSV σας πρέπει να περιέχει τις κεφαλίδες των στηλών, όπως φαίνεται στον πίνακα παραδείγματος. Βεβαιωθείτε ότι το αρχείο σας είναι κωδικοποιημένο σε UTF-8 για να αποφύγετε τυχόν προβλήματα κωδικοποίησης.';
$lang['please_upload_valid_csv_file'] = 'Παρακαλώ ανεβάστε έγκυρο αρχείο CSV';
$lang['please_add_valid_number_in_csv_file'] = 'Παρακαλώ προσθέστε έγκυρο <b>Phoneno</b> στο αρχείο CSV';
$lang['total_send_campaign_list'] = 'Συνολική αποστολή καμπάνιας: %s';
$lang['sample_data'] = 'Δεδομένα Δείγματος';
$lang['firstname'] = 'Όνομα';
$lang['lastname'] = 'Επώνυμο';
$lang['phoneno'] = 'Τηλέφωνο';
$lang['email'] = 'Email';
$lang['country'] = 'Χώρα';
$lang['download_sample_and_read_rules'] = 'Λήψη Δείγματος & Ανάγνωση Κανόνων';
$lang['please_wait_your_request_in_process'] = 'Παρακαλώ περιμένετε, το αίτημά σας είναι αυτή τη στιγμή σε επεξεργασία.';
$lang['whatsbot_bulk_campaign'] = 'Μαζικές Καμπάνιες Whatsbot';
$lang['csv_campaign'] = 'Καμπάνια CSV';

// Canned reply
$lang['canned_reply'] = 'Έτοιμες Απαντήσεις';
$lang['canned_reply_menu'] = 'Έτοιμες Απαντήσεις';
$lang['create_canned_reply'] = 'Δημιουργία Έτοιμης Απάντησης';
$lang['title'] = 'Τίτλος';
$lang['desc'] = 'Περιγραφή';
$lang['public'] = 'Δημόσιο';
$lang['action'] = 'Δράση';
$lang['delete_successfully'] = 'Η απάντηση διαγράφηκε.';
$lang['cannot_delete'] = 'Δεν μπορεί να διαγραφεί η απάντηση.';
$lang['whatsbot_canned_reply'] = 'Έτοιμες Απαντήσεις Whatsbot';
$lang['reply'] = 'Απάντηση';

//AI Prompts
$lang['ai_prompts'] = 'Υποδείξεις AI';
$lang['create_ai_prompts'] = 'Δημιουργία Υποδείξεων AI';
$lang['name'] = 'Όνομα';
$lang['action'] = 'Δράση';
$lang['prompt_name'] = 'Όνομα υποδείξεων';
$lang['prompt_action'] = 'Δράση υποδείξεων';
$lang['whatsbot_ai_prompts'] = 'Υποδείξεις AI Whatsbot';

// new chat
$lang['replying_to'] = 'Απαντώντας σε:';
$lang['download_document'] = 'Λήψη Εγγράφου';
$lang['custom_prompt'] = 'Προσαρμοσμένη Υπόδειξη';
$lang['canned_replies'] = 'Έτοιμες Απαντήσεις';
$lang['use_@_to_add_merge_fields'] = 'Χρησιμοποιήστε \'@\' για να προσθέσετε πεδία συγχώνευσης';
$lang['type_your_message'] = 'Πληκτρολογήστε το μήνυμά σας';
$lang['you_cannot_send_a_message_using_this_number'] = 'Δεν μπορείτε να στείλετε μήνυμα χρησιμοποιώντας αυτόν τον αριθμό.';

// bot flow
$lang['bot_flow'] = 'Ροή Bot';
$lang['create_new_flow'] = 'Δημιουργία Νέας Ροής';
$lang['flow_name'] = 'Όνομα Ροής';
$lang['flow'] = 'Ροή';
$lang['bot_flow_builder'] = 'Κατασκευαστής Ροής Bot';
$lang['you_can_not_upload_file_type'] = 'Δεν μπορείτε να ανεβάσετε αρχείο τύπου <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Ροή Bot Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Αυτόματη Διαγραφή Ιστορικού Συνομιλίας';
$lang['enable_auto_clear_chat_history'] = 'Ενεργοποίηση Αυτόματης Διαγραφής Ιστορικού Συνομιλίας';
$lang['auto_clear_time'] = 'Χρόνος Αυτόματης Διαγραφής Ιστορικού';
$lang['clear_chat_history_note'] = '<strong>Σημείωση: </strong> Αν ενεργοποιήσετε τη δυνατότητα αυτόματης διαγραφής ιστορικού συνομιλίας, θα διαγράφει αυτόματα το ιστορικό συνομιλίας με βάση τον αριθμό ημερών που θα καθορίσετε, όποτε εκτελείται η εργασία cron.';
$lang['source'] = 'Πηγή';
$lang['groups'] = 'Ομάδες';

// v1.3.3
$lang['click_user_to_chat'] = 'Κάντε κλικ στον χρήστη για συνομιλία';
$lang['searching'] = 'Αναζήτηση...';
$lang['filters'] = 'Φίλτρα';
$lang['relation_type'] = 'Τύπος σχέσης';
$lang['groups'] = 'Ομάδες';
$lang['source'] = 'Πηγή';
$lang['status'] = 'Κατάσταση';
$lang['select_type'] = 'Επιλέξτε τύπο';
$lang['select_agents'] = 'Επιλέξτε πράκτορες';
$lang['select_group'] = 'Επιλέξτε ομάδα';
$lang['select_source'] = 'Επιλέξτε πηγή';
$lang['select_status'] = 'Επιλέξτε κατάσταση';
$lang['agents'] = 'Πράκτορες';

// v1.4.2
$lang['read_only'] = 'Μόνο για ανάγνωση';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
