/*! For license information please see vueflow.bundle.js.LICENSE.txt */
(()=>{"use strict";var e={166:(e,t,n)=>{n.d(t,{A:()=>a});var o=n(601),r=n.n(o),i=n(314),l=n.n(i)()(r());l.push([e.id,".vue-flow__controls {\n  box-shadow: 0 0 2px 1px rgba(0, 0, 0, 0.08);\n}\n\n.vue-flow__controls-button {\n  background: #fefefe;\n  border: none;\n  border-bottom: 1px solid #eee;\n  box-sizing: content-box;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  width: 16px;\n  height: 16px;\n  cursor: pointer;\n  user-select: none;\n  padding: 5px;\n}\n\n.vue-flow__controls-button svg {\n  width: 100%;\n  max-width: 12px;\n  max-height: 12px;\n}\n\n.vue-flow__controls-button:hover {\n  background: #f4f4f4;\n}\n\n\n.vue-flow__controls-button:disabled {\n  pointer-events: none;\n}\n\n.vue-flow__controls-button:disabled svg {\n  fill-opacity: 0.4;\n}\n",""]);const a=l},955:(e,t,n)=>{n.d(t,{A:()=>a});var o=n(601),r=n.n(o),i=n(314),l=n.n(i)()(r());l.push([e.id,".vue-flow {\n  position: relative;\n  width: 100%;\n  height: 100%;\n  overflow: hidden;\n  z-index: 0;\n  direction: ltr;\n}\n\n.vue-flow__container {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  left: 0;\n  top: 0;\n}\n\n.vue-flow__pane {\n  z-index: 1;\n}\n\n.vue-flow__pane.draggable {\n     cursor: grab;\n   }\n\n.vue-flow__pane.dragging {\n     cursor: grabbing;\n   }\n\n.vue-flow__pane.selection {\n     cursor: pointer;\n   }\n\n.vue-flow__transformationpane {\n  transform-origin: 0 0;\n  z-index: 2;\n  pointer-events: none;\n}\n\n.vue-flow__viewport {\n  z-index: 4;\n  overflow: clip;\n}\n\n.vue-flow__selection {\n  z-index: 6;\n}\n\n.vue-flow__edge-labels {\n  position: absolute;\n  width: 100%;\n  height: 100%;\n  pointer-events: none;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n}\n\n.vue-flow__nodesselection-rect:focus,\n.vue-flow__nodesselection-rect:focus-visible {\n  outline: none;\n}\n\n.vue-flow .vue-flow__edges {\n  pointer-events: none;\n  overflow: visible;\n}\n\n.vue-flow__edge-path,\n.vue-flow__connection-path {\n  stroke: #b1b1b7;\n  stroke-width: 1;\n  fill: none;\n}\n\n.vue-flow__edge {\n  pointer-events: visibleStroke;\n  cursor: pointer;\n}\n\n.vue-flow__edge.animated path {\n     stroke-dasharray: 5;\n     animation: dashdraw 0.5s linear infinite;\n   }\n\n.vue-flow__edge.animated path.vue-flow__edge-interaction {\n     stroke-dasharray: none;\n     animation: none;\n   }\n\n.vue-flow__edge.inactive {\n     pointer-events: none;\n   }\n\n.vue-flow__edge.selected,\n  .vue-flow__edge:focus,\n  .vue-flow__edge:focus-visible {\n     outline: none;\n   }\n\n.vue-flow__edge.selected .vue-flow__edge-path,\n  .vue-flow__edge:focus .vue-flow__edge-path,\n  .vue-flow__edge:focus-visible .vue-flow__edge-path {\n     stroke: #555;\n   }\n\n.vue-flow__edge-textwrapper {\n     pointer-events: all;\n   }\n\n.vue-flow__edge-textbg {\n     fill: white;\n   }\n\n.vue-flow__edge-text {\n    pointer-events: none;\n    -webkit-user-select: none;\n       -moz-user-select: none;\n            user-select: none;\n  }\n\n.vue-flow__connection {\n  pointer-events: none;\n}\n\n.vue-flow__connection .animated {\n     stroke-dasharray: 5;\n     animation: dashdraw 0.5s linear infinite;\n   }\n\n.vue-flow__connectionline {\n  z-index: 1001;\n}\n\n.vue-flow__nodes {\n  pointer-events: none;\n  transform-origin: 0 0;\n}\n\n.vue-flow__node-default,\n.vue-flow__node-input,\n.vue-flow__node-output {\n  border-width: 1px;\n  border-style: solid;\n  border-color: #bbb;\n}\n\n.vue-flow__node-default.selected,\n  .vue-flow__node-default:focus,\n  .vue-flow__node-default:focus-visible,\n  .vue-flow__node-input.selected,\n  .vue-flow__node-input:focus,\n  .vue-flow__node-input:focus-visible,\n  .vue-flow__node-output.selected,\n  .vue-flow__node-output:focus,\n  .vue-flow__node-output:focus-visible {\n     outline: none;\n     border: 1px solid #555;\n   }\n\n.vue-flow__node {\n  position: absolute;\n  -webkit-user-select: none;\n     -moz-user-select: none;\n          user-select: none;\n  pointer-events: all;\n  transform-origin: 0 0;\n  box-sizing: border-box;\n  cursor: default;\n}\n\n.vue-flow__node.draggable {\n    cursor: grab;\n    pointer-events: all;\n  }\n\n.vue-flow__node.draggable.dragging {\n      cursor: grabbing;\n    }\n\n.vue-flow__nodesselection {\n  z-index: 3;\n  transform-origin: left top;\n  pointer-events: none;\n}\n\n.vue-flow__nodesselection-rect {\n     position: absolute;\n     pointer-events: all;\n     cursor: grab;\n   }\n\n.vue-flow__nodesselection-rect.dragging {\n          cursor: grabbing;\n        }\n\n.vue-flow__handle {\n  position: absolute;\n  pointer-events: none;\n  min-width: 5px;\n  min-height: 5px;\n}\n\n.vue-flow__handle.connectable {\n     pointer-events: all;\n     cursor: crosshair;\n   }\n\n.vue-flow__handle-bottom {\n     left: 50%;\n     bottom: 0;\n     transform: translate(-50%, 50%);\n   }\n\n.vue-flow__handle-top {\n     left: 50%;\n     top: 0;\n     transform: translate(-50%, -50%);\n   }\n\n.vue-flow__handle-left {\n     top: 50%;\n     left: 0;\n     transform: translate(-50%, -50%);\n   }\n\n.vue-flow__handle-right {\n     top: 50%;\n     right: 0;\n     transform: translate(50%, -50%);\n   }\n\n.vue-flow__edgeupdater {\n  cursor: move;\n  pointer-events: all;\n}\n\n.vue-flow__panel {\n  position: absolute;\n  z-index: 5;\n  margin: 15px;\n}\n\n.vue-flow__panel.top {\n     top: 0;\n   }\n\n.vue-flow__panel.bottom {\n     bottom: 0;\n   }\n\n.vue-flow__panel.left {\n     left: 0;\n   }\n\n.vue-flow__panel.right {\n     right: 0;\n   }\n\n.vue-flow__panel.center {\n     left: 50%;\n     transform: translateX(-50%);\n   }\n\n@keyframes dashdraw {\n  from {\n    stroke-dashoffset: 10;\n  }\n}\n",""]);const a=l},739:(e,t,n)=>{n.d(t,{A:()=>a});var o=n(601),r=n.n(o),i=n(314),l=n.n(i)()(r());l.push([e.id,":root {\n  --vf-node-bg: #fff;\n  --vf-node-text: #222;\n  --vf-connection-path:  #b1b1b7;\n  --vf-handle: #555;\n}\n\n.vue-flow__edge.updating .vue-flow__edge-path {\n      stroke: #777;\n    }\n\n.vue-flow__edge-text {\n  font-size: 10px;\n}\n\n.vue-flow__edge-textbg {\n  fill: #fff;\n}\n\n.vue-flow__connection-path {\n  stroke: var(--vf-connection-path);\n}\n\n.vue-flow__node {\n  cursor: grab;\n}\n\n.vue-flow__node.selectable:focus,\n  .vue-flow__node.selectable:focus-visible {\n     outline: none;\n   }\n\n.vue-flow__node-default,\n.vue-flow__node-input,\n.vue-flow__node-output {\n  padding: 10px;\n  border-radius: 3px;\n  width: 150px;\n  font-size: 12px;\n  text-align: center;\n  border-width: 1px;\n  border-style: solid;\n  color: var(--vf-node-text);\n  background-color: var(--vf-node-bg);\n  border-color: var(--vf-node-color);\n}\n\n.vue-flow__node-default.selected,\n  .vue-flow__node-default.selected:hover,\n  .vue-flow__node-input.selected,\n  .vue-flow__node-input.selected:hover,\n  .vue-flow__node-output.selected,\n  .vue-flow__node-output.selected:hover {\n     box-shadow: 0 0 0 0.5px var(--vf-box-shadow);\n   }\n\n.vue-flow__node-default .vue-flow__handle, .vue-flow__node-input .vue-flow__handle, .vue-flow__node-output .vue-flow__handle {\n    background: var(--vf-handle);\n  }\n\n.vue-flow__node-default.selectable:hover, .vue-flow__node-input.selectable:hover, .vue-flow__node-output.selectable:hover {\n    box-shadow: 0 1px 4px 1px rgba(0, 0, 0, 0.08);\n  }\n\n.vue-flow__node-input {\n  --vf-node-color: var(--vf-node-color, #0041d0);\n  --vf-handle: var(--vf-node-color, #0041d0);\n  --vf-box-shadow: var(--vf-node-color, #0041d0);\n\n  background: var(--vf-node-bg);\n  border-color: var(--vf-node-color, #0041d0);\n}\n\n.vue-flow__node-input.selected,\n  .vue-flow__node-input:focus,\n  .vue-flow__node-input:focus-visible {\n     outline: none;\n     border: 1px solid var(--vf-node-color, #0041d0);\n   }\n\n.vue-flow__node-default {\n  --vf-handle: var(--vf-node-color, #1a192b);\n  --vf-box-shadow: var(--vf-node-color, #1a192b);\n\n  background: var(--vf-node-bg);\n  border-color: var(--vf-node-color, #1a192b);\n}\n\n.vue-flow__node-default.selected,\n  .vue-flow__node-default:focus,\n  .vue-flow__node-default:focus-visible {\n     outline: none;\n     border: 1px solid var(--vf-node-color, #1a192b);\n   }\n\n.vue-flow__node-output {\n  --vf-handle: var(--vf-node-color, #ff0072);\n  --vf-box-shadow: var(--vf-node-color, #ff0072);\n\n  background: var(--vf-node-bg);\n  border-color: var(--vf-node-color, #ff0072);\n}\n\n.vue-flow__node-output.selected,\n  .vue-flow__node-output:focus,\n  .vue-flow__node-output:focus-visible {\n     outline: none;\n     border: 1px solid var(--vf-node-color, #ff0072);\n   }\n\n.vue-flow__nodesselection-rect,\n.vue-flow__selection {\n  background: rgba(0, 89, 220, 0.08);\n  border: 1px dotted rgba(0, 89, 220, 0.8);\n}\n\n.vue-flow__nodesselection-rect:focus,\n  .vue-flow__nodesselection-rect:focus-visible,\n  .vue-flow__selection:focus,\n  .vue-flow__selection:focus-visible {\n     outline: none;\n   }\n\n.vue-flow__handle {\n  width: 6px;\n  height: 6px;\n  background: var(--vf-handle);\n  border: 1px solid #fff;\n  border-radius: 100%;\n}\n",""]);const a=l},490:(e,t,n)=>{n.d(t,{A:()=>d});var o=n(601),r=n.n(o),i=n(314),l=n.n(i),a=n(955),s=n(739),u=n(166),c=l()(r());c.i(a.A),c.i(s.A),c.i(u.A),c.push([e.id,"\n#main-canvas {\n  --vf-handle: hsl(var(--primary));\n.vue-flow__handle {\n    width: 10px;\n    height: 10px;\n}\n.basic-flow .vue-flow__controls .vue-flow__controls-button svg {\n    height: 100%;\n    width: 100%;\n}\n.vue-flow__controls {\n    background-color: white;\n    padding: 0.15rem;\n    border-radius: 1rem;\n}\n.vue-flow__controls-button {\n    margin: 0.5rem;\n    padding: 5px;\n    border-radius: 50%;\n    box-shadow: 0px 0px 5px rgba(128, 128, 128, 0.5);\n}\n}\n::-webkit-scrollbar {\n  width: 6px;\n}\n::-webkit-scrollbar-thumb {\n  background-color: #ccc;\n  border-radius: 10px;\n}\n::-webkit-scrollbar-track {\n  background-color: #f0f0f0;\n}\n",""]);const d=c},170:(e,t,n)=>{n.d(t,{A:()=>a});var o=n(601),r=n.n(o),i=n(314),l=n.n(i)()(r());l.push([e.id,"\n.edge__button_delete {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  border: 1px rgb(183, 183, 183) solid;\n  border-radius: 1rem;\n  padding: 0.1rem;\n  background-color: #f2f5f7;\n}\n.edge__button_delete:hover {\n  transform: scale(1.2);\n  transition: transform 0.5s 0.1s;\n}\n",""]);const a=l},314:e=>{e.exports=function(e){var t=[];return t.toString=function(){return this.map((function(t){var n="",o=void 0!==t[5];return t[4]&&(n+="@supports (".concat(t[4],") {")),t[2]&&(n+="@media ".concat(t[2]," {")),o&&(n+="@layer".concat(t[5].length>0?" ".concat(t[5]):""," {")),n+=e(t),o&&(n+="}"),t[2]&&(n+="}"),t[4]&&(n+="}"),n})).join("")},t.i=function(e,n,o,r,i){"string"==typeof e&&(e=[[null,e,void 0]]);var l={};if(o)for(var a=0;a<this.length;a++){var s=this[a][0];null!=s&&(l[s]=!0)}for(var u=0;u<e.length;u++){var c=[].concat(e[u]);o&&l[c[0]]||(void 0!==i&&(void 0===c[5]||(c[1]="@layer".concat(c[5].length>0?" ".concat(c[5]):""," {").concat(c[1],"}")),c[5]=i),n&&(c[2]?(c[1]="@media ".concat(c[2]," {").concat(c[1],"}"),c[2]=n):c[2]=n),r&&(c[4]?(c[1]="@supports (".concat(c[4],") {").concat(c[1],"}"),c[4]=r):c[4]="".concat(r)),t.push(c))}},t}},601:e=>{e.exports=function(e){return e[1]}},72:e=>{var t=[];function n(e){for(var n=-1,o=0;o<t.length;o++)if(t[o].identifier===e){n=o;break}return n}function o(e,o){for(var i={},l=[],a=0;a<e.length;a++){var s=e[a],u=o.base?s[0]+o.base:s[0],c=i[u]||0,d="".concat(u," ").concat(c);i[u]=c+1;var f=n(d),p={css:s[1],media:s[2],sourceMap:s[3],supports:s[4],layer:s[5]};if(-1!==f)t[f].references++,t[f].updater(p);else{var v=r(p,o);o.byIndex=a,t.splice(a,0,{identifier:d,updater:v,references:1})}l.push(d)}return l}function r(e,t){var n=t.domAPI(t);return n.update(e),function(t){if(t){if(t.css===e.css&&t.media===e.media&&t.sourceMap===e.sourceMap&&t.supports===e.supports&&t.layer===e.layer)return;n.update(e=t)}else n.remove()}}e.exports=function(e,r){var i=o(e=e||[],r=r||{});return function(e){e=e||[];for(var l=0;l<i.length;l++){var a=n(i[l]);t[a].references--}for(var s=o(e,r),u=0;u<i.length;u++){var c=n(i[u]);0===t[c].references&&(t[c].updater(),t.splice(c,1))}i=s}}},659:e=>{var t={};e.exports=function(e,n){var o=function(e){if(void 0===t[e]){var n=document.querySelector(e);if(window.HTMLIFrameElement&&n instanceof window.HTMLIFrameElement)try{n=n.contentDocument.head}catch(e){n=null}t[e]=n}return t[e]}(e);if(!o)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");o.appendChild(n)}},540:e=>{e.exports=function(e){var t=document.createElement("style");return e.setAttributes(t,e.attributes),e.insert(t,e.options),t}},56:(e,t,n)=>{e.exports=function(e){var t=n.nc;t&&e.setAttribute("nonce",t)}},825:e=>{e.exports=function(e){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var t=e.insertStyleElement(e);return{update:function(n){!function(e,t,n){var o="";n.supports&&(o+="@supports (".concat(n.supports,") {")),n.media&&(o+="@media ".concat(n.media," {"));var r=void 0!==n.layer;r&&(o+="@layer".concat(n.layer.length>0?" ".concat(n.layer):""," {")),o+=n.css,r&&(o+="}"),n.media&&(o+="}"),n.supports&&(o+="}");var i=n.sourceMap;i&&"undefined"!=typeof btoa&&(o+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),t.styleTagTransform(o,e,t.options)}(t,e,n)},remove:function(){!function(e){if(null===e.parentNode)return!1;e.parentNode.removeChild(e)}(t)}}}},113:e=>{e.exports=function(e,t){if(t.styleSheet)t.styleSheet.cssText=e;else{for(;t.firstChild;)t.removeChild(t.firstChild);t.appendChild(document.createTextNode(e))}}}},t={};function n(o){var r=t[o];if(void 0!==r)return r.exports;var i=t[o]={id:o,exports:{}};return e[o](i,i.exports,n),i.exports}let o,r,i,l,a,s,u,c,d,f,p;function v(e,t){let n=new Set(e.split(","));return e=>n.has(e)}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.nc=void 0;let h={},g=[],m=()=>{},y=()=>!1,b=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&(e.charCodeAt(2)>122||97>e.charCodeAt(2)),w=e=>e.startsWith("onUpdate:"),x=Object.assign,_=(e,t)=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)},S=Object.prototype.hasOwnProperty,E=(e,t)=>S.call(e,t),k=Array.isArray,C=e=>"[object Map]"===z(e),O=e=>"[object Set]"===z(e),N=e=>"[object Date]"===z(e),M=e=>"function"==typeof e,P=e=>"string"==typeof e,T=e=>"symbol"==typeof e,A=e=>null!==e&&"object"==typeof e,D=e=>(A(e)||M(e))&&M(e.then)&&M(e.catch),I=Object.prototype.toString,z=e=>I.call(e),j=e=>z(e).slice(8,-1),B=e=>"[object Object]"===z(e),L=e=>P(e)&&"NaN"!==e&&"-"!==e[0]&&""+parseInt(e,10)===e,R=v(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),F=e=>{let t=Object.create(null);return n=>t[n]||(t[n]=e(n))},V=/-(\w)/g,H=F((e=>e.replace(V,((e,t)=>t?t.toUpperCase():"")))),U=/\B([A-Z])/g,Y=F((e=>e.replace(U,"-$1").toLowerCase())),X=F((e=>e.charAt(0).toUpperCase()+e.slice(1))),G=F((e=>e?`on${X(e)}`:"")),q=(e,t)=>!Object.is(e,t),W=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Z=(e,t,n,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:n})},K=e=>{let t=parseFloat(e);return isNaN(t)?e:t},J=e=>{let t=P(e)?Number(e):NaN;return isNaN(t)?e:t},Q=()=>o||(o="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof window?window:void 0!==n.g?n.g:{});function ee(e){if(k(e)){let t={};for(let n=0;n<e.length;n++){let o=e[n],r=P(o)?function(e){let t={};return e.replace(oe,"").split(te).forEach((e=>{if(e){let n=e.split(ne);n.length>1&&(t[n[0].trim()]=n[1].trim())}})),t}(o):ee(o);if(r)for(let e in r)t[e]=r[e]}return t}if(P(e)||A(e))return e}let te=/;(?![^(]*\))/g,ne=/:([^]+)/,oe=/\/\*[^]*?\*\//g;function re(e){let t="";if(P(e))t=e;else if(k(e))for(let n=0;n<e.length;n++){let o=re(e[n]);o&&(t+=o+" ")}else if(A(e))for(let n in e)e[n]&&(t+=n+" ");return t.trim()}let ie=v("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function le(e,t){if(e===t)return!0;let n=N(e),o=N(t);if(n||o)return!!n&&!!o&&e.getTime()===t.getTime();if(n=T(e),o=T(t),n||o)return e===t;if(n=k(e),o=k(t),n||o)return!!n&&!!o&&function(e,t){if(e.length!==t.length)return!1;let n=!0;for(let o=0;n&&o<e.length;o++)n=le(e[o],t[o]);return n}(e,t);if(n=A(e),o=A(t),n||o){if(!n||!o||Object.keys(e).length!==Object.keys(t).length)return!1;for(let n in e){let o=e.hasOwnProperty(n),r=t.hasOwnProperty(n);if(o&&!r||!o&&r||!le(e[n],t[n]))return!1}}return String(e)===String(t)}function ae(e,t){return e.findIndex((e=>le(e,t)))}let se=e=>!(!e||!0!==e.__v_isRef),ue=e=>P(e)?e:null==e?"":k(e)||A(e)&&(e.toString===I||!M(e.toString))?se(e)?ue(e.value):JSON.stringify(e,ce,2):String(e),ce=(e,t)=>se(t)?ce(e,t.value):C(t)?{[`Map(${t.size})`]:[...t.entries()].reduce(((e,[t,n],o)=>(e[de(t,o)+" =>"]=n,e)),{})}:O(t)?{[`Set(${t.size})`]:[...t.values()].map((e=>de(e)))}:T(t)?de(t):!A(t)||k(t)||B(t)?t:String(t),de=(e,t="")=>{var n;return T(e)?`Symbol(${null!=(n=e.description)?n:t})`:e};class fe{constructor(e=!1){this.detached=e,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=r,!e&&r&&(this.index=(r.scopes||(r.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){let e,t;if(this._isPaused=!0,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){let e,t;if(this._isPaused=!1,this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){let t=r;try{return r=this,e()}finally{r=t}}}on(){r=this}off(){r=this.parent}stop(e){if(this._active){let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0,this._active=!1}}}function pe(e){return new fe(e)}function ve(){return r}function he(e,t=!1){r&&r.cleanups.push(e)}let ge=new WeakSet;class me{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.nextEffect=void 0,this.cleanup=void 0,this.scheduler=void 0,r&&r.active&&r.effects.push(this)}pause(){this.flags|=64}resume(){64&this.flags&&(this.flags&=-65,ge.has(this)&&(ge.delete(this),this.trigger()))}notify(){(!(2&this.flags)||32&this.flags)&&(8&this.flags||(this.flags|=8,this.nextEffect=l,l=this))}run(){if(!(1&this.flags))return this.fn();this.flags|=2,Me(this),we(this);let e=i,t=ke;i=this,ke=!0;try{return this.fn()}finally{xe(this),i=e,ke=t,this.flags&=-3}}stop(){if(1&this.flags){for(let e=this.deps;e;e=e.nextDep)Ee(e);this.deps=this.depsTail=void 0,Me(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){64&this.flags?ge.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){_e(this)&&this.run()}get dirty(){return _e(this)}}let ye=0;function be(){let e;if(!(--ye>0)){for(;l;){let t=l;for(l=void 0;t;){let n=t.nextEffect;if(t.nextEffect=void 0,t.flags&=-9,1&t.flags)try{t.trigger()}catch(t){e||(e=t)}t=n}}if(e)throw e}}function we(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function xe(e){let t,n=e.depsTail;for(let e=n;e;e=e.prevDep)-1===e.version?(e===n&&(n=e.prevDep),Ee(e),function(e){let{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}(e)):t=e,e.dep.activeLink=e.prevActiveLink,e.prevActiveLink=void 0;e.deps=t,e.depsTail=n}function _e(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&!1===Se(t.dep.computed)||t.dep.version!==t.version)return!0;return!!e._dirty}function Se(e){if(2&e.flags)return!1;if(4&e.flags&&!(16&e.flags)||(e.flags&=-17,e.globalVersion===Pe))return;e.globalVersion=Pe;let t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&!_e(e))return void(e.flags&=-3);let n=i,o=ke;i=e,ke=!0;try{we(e);let n=e.fn();(0===t.version||q(n,e._value))&&(e._value=n,t.version++)}catch(e){throw t.version++,e}finally{i=n,ke=o,xe(e),e.flags&=-3}}function Ee(e){let{dep:t,prevSub:n,nextSub:o}=e;if(n&&(n.nextSub=o,e.prevSub=void 0),o&&(o.prevSub=n,e.nextSub=void 0),t.subs===e&&(t.subs=n),!t.subs&&t.computed){t.computed.flags&=-5;for(let e=t.computed.deps;e;e=e.nextDep)Ee(e)}}let ke=!0,Ce=[];function Oe(){Ce.push(ke),ke=!1}function Ne(){let e=Ce.pop();ke=void 0===e||e}function Me(e){let{cleanup:t}=e;if(e.cleanup=void 0,t){let e=i;i=void 0;try{t()}finally{i=e}}}let Pe=0;class Te{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0}track(e){if(!i||!ke)return;let t=this.activeLink;if(void 0===t||t.sub!==i)t=this.activeLink={dep:this,sub:i,version:this.version,nextDep:void 0,prevDep:void 0,nextSub:void 0,prevSub:void 0,prevActiveLink:void 0},i.deps?(t.prevDep=i.depsTail,i.depsTail.nextDep=t,i.depsTail=t):i.deps=i.depsTail=t,4&i.flags&&function e(t){let n=t.dep.computed;if(n&&!t.dep.subs){n.flags|=20;for(let t=n.deps;t;t=t.nextDep)e(t)}let o=t.dep.subs;o!==t&&(t.prevSub=o,o&&(o.nextSub=t)),t.dep.subs=t}(t);else if(-1===t.version&&(t.version=this.version,t.nextDep)){let e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=i.depsTail,t.nextDep=void 0,i.depsTail.nextDep=t,i.depsTail=t,i.deps===t&&(i.deps=e)}return t}trigger(e){this.version++,Pe++,this.notify(e)}notify(e){ye++;try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()}finally{be()}}}let Ae=new WeakMap,De=Symbol(""),Ie=Symbol(""),$e=Symbol("");function ze(e,t,n){if(ke&&i){let t=Ae.get(e);t||Ae.set(e,t=new Map);let o=t.get(n);o||t.set(n,o=new Te),o.track()}}function je(e,t,n,o,r,i){let l=Ae.get(e);if(!l)return void Pe++;let a=[];if("clear"===t)a=[...l.values()];else{let r=k(e),i=r&&L(n);if(r&&"length"===n){let e=Number(o);l.forEach(((t,n)=>{("length"===n||n===$e||!T(n)&&n>=e)&&a.push(t)}))}else{let o=e=>e&&a.push(e);switch(void 0!==n&&o(l.get(n)),i&&o(l.get($e)),t){case"add":r?i&&o(l.get("length")):(o(l.get(De)),C(e)&&o(l.get(Ie)));break;case"delete":!r&&(o(l.get(De)),C(e)&&o(l.get(Ie)));break;case"set":C(e)&&o(l.get(De))}}}for(let e of(ye++,a))e.trigger();be()}function Be(e){let t=Dt(e);return t===e?t:(ze(t,0,$e),Tt(e)?t:t.map($t))}function Le(e){return ze(e=Dt(e),0,$e),e}let Re={__proto__:null,[Symbol.iterator](){return Fe(this,Symbol.iterator,$t)},concat(...e){return Be(this).concat(...e.map((e=>Be(e))))},entries(){return Fe(this,"entries",(e=>(e[1]=$t(e[1]),e)))},every(e,t){return He(this,"every",e,t,void 0,arguments)},filter(e,t){return He(this,"filter",e,t,(e=>e.map($t)),arguments)},find(e,t){return He(this,"find",e,t,$t,arguments)},findIndex(e,t){return He(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return He(this,"findLast",e,t,$t,arguments)},findLastIndex(e,t){return He(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return He(this,"forEach",e,t,void 0,arguments)},includes(...e){return Ye(this,"includes",e)},indexOf(...e){return Ye(this,"indexOf",e)},join(e){return Be(this).join(e)},lastIndexOf(...e){return Ye(this,"lastIndexOf",e)},map(e,t){return He(this,"map",e,t,void 0,arguments)},pop(){return Xe(this,"pop")},push(...e){return Xe(this,"push",e)},reduce(e,...t){return Ue(this,"reduce",e,t)},reduceRight(e,...t){return Ue(this,"reduceRight",e,t)},shift(){return Xe(this,"shift")},some(e,t){return He(this,"some",e,t,void 0,arguments)},splice(...e){return Xe(this,"splice",e)},toReversed(){return Be(this).toReversed()},toSorted(e){return Be(this).toSorted(e)},toSpliced(...e){return Be(this).toSpliced(...e)},unshift(...e){return Xe(this,"unshift",e)},values(){return Fe(this,"values",$t)}};function Fe(e,t,n){let o=Le(e),r=o[t]();return o===e||Tt(e)||(r._next=r.next,r.next=()=>{let e=r._next();return e.value&&(e.value=n(e.value)),e}),r}let Ve=Array.prototype;function He(e,t,n,o,r,i){let l=Le(e),a=l!==e&&!Tt(e),s=l[t];if(s!==Ve[t]){let t=s.apply(e,i);return a?$t(t):t}let u=n;l!==e&&(a?u=function(t,o){return n.call(this,$t(t),o,e)}:n.length>2&&(u=function(t,o){return n.call(this,t,o,e)}));let c=s.call(l,u,o);return a&&r?r(c):c}function Ue(e,t,n,o){let r=Le(e),i=n;return r!==e&&(Tt(e)?n.length>3&&(i=function(t,o,r){return n.call(this,t,o,r,e)}):i=function(t,o,r){return n.call(this,t,$t(o),r,e)}),r[t](i,...o)}function Ye(e,t,n){let o=Dt(e);ze(o,0,$e);let r=o[t](...n);return-1!==r&&!1!==r||!At(n[0])?r:(n[0]=Dt(n[0]),o[t](...n))}function Xe(e,t,n=[]){Oe(),ye++;let o=Dt(e)[t].apply(e,n);return be(),Ne(),o}let Ge=v("__proto__,__v_isRef,__isVue"),qe=new Set(Object.getOwnPropertyNames(Symbol).filter((e=>"arguments"!==e&&"caller"!==e)).map((e=>Symbol[e])).filter(T));function We(e){T(e)||(e=String(e));let t=Dt(this);return ze(t,0,e),t.hasOwnProperty(e)}class Ze{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){let o=this._isReadonly,r=this._isShallow;if("__v_isReactive"===t)return!o;if("__v_isReadonly"===t)return o;if("__v_isShallow"===t)return r;if("__v_raw"===t)return n===(o?r?kt:Et:r?St:_t).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;let i=k(e);if(!o){let e;if(i&&(e=Re[t]))return e;if("hasOwnProperty"===t)return We}let l=Reflect.get(e,t,jt(e)?e:n);return(T(t)?qe.has(t):Ge(t))?l:(o||ze(e,0,t),r?l:jt(l)?i&&L(t)?l:l.value:A(l)?o?Ot(l):Ct(l):l)}}class Ke extends Ze{constructor(e=!1){super(!1,e)}set(e,t,n,o){let r=e[t];if(!this._isShallow){let t=Pt(r);if(Tt(n)||Pt(n)||(r=Dt(r),n=Dt(n)),!k(e)&&jt(r)&&!jt(n))return!t&&(r.value=n,!0)}let i=k(e)&&L(t)?Number(t)<e.length:E(e,t),l=Reflect.set(e,t,n,jt(e)?e:o);return e===Dt(o)&&(i?q(n,r)&&je(e,"set",t,n):je(e,"add",t,n)),l}deleteProperty(e,t){let n=E(e,t);e[t];let o=Reflect.deleteProperty(e,t);return o&&n&&je(e,"delete",t,void 0),o}has(e,t){let n=Reflect.has(e,t);return T(t)&&qe.has(t)||ze(e,0,t),n}ownKeys(e){return ze(e,0,k(e)?"length":De),Reflect.ownKeys(e)}}class Je extends Ze{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}}let Qe=new Ke,et=new Je,tt=new Ke(!0),nt=e=>e,ot=e=>Reflect.getPrototypeOf(e);function rt(e,t,n=!1,o=!1){let r=Dt(e=e.__v_raw),i=Dt(t);n||(q(t,i)&&ze(r,0,t),ze(r,0,i));let{has:l}=ot(r),a=o?nt:n?zt:$t;return l.call(r,t)?a(e.get(t)):l.call(r,i)?a(e.get(i)):void(e!==r&&e.get(t))}function it(e,t=!1){let n=this.__v_raw,o=Dt(n),r=Dt(e);return t||(q(e,r)&&ze(o,0,e),ze(o,0,r)),e===r?n.has(e):n.has(e)||n.has(r)}function lt(e,t=!1){return e=e.__v_raw,t||ze(Dt(e),0,De),Reflect.get(e,"size",e)}function at(e,t=!1){t||Tt(e)||Pt(e)||(e=Dt(e));let n=Dt(this);return ot(n).has.call(n,e)||(n.add(e),je(n,"add",e,e)),this}function st(e,t,n=!1){n||Tt(t)||Pt(t)||(t=Dt(t));let o=Dt(this),{has:r,get:i}=ot(o),l=r.call(o,e);l||(e=Dt(e),l=r.call(o,e));let a=i.call(o,e);return o.set(e,t),l?q(t,a)&&je(o,"set",e,t):je(o,"add",e,t),this}function ut(e){let t=Dt(this),{has:n,get:o}=ot(t),r=n.call(t,e);r||(e=Dt(e),r=n.call(t,e)),o&&o.call(t,e);let i=t.delete(e);return r&&je(t,"delete",e,void 0),i}function ct(){let e=Dt(this),t=0!==e.size,n=e.clear();return t&&je(e,"clear",void 0,void 0),n}function dt(e,t){return function(n,o){let r=this,i=r.__v_raw,l=Dt(i),a=t?nt:e?zt:$t;return e||ze(l,0,De),i.forEach(((e,t)=>n.call(o,a(e),a(t),r)))}}function ft(e,t,n){return function(...o){let r=this.__v_raw,i=Dt(r),l=C(i),a="entries"===e||e===Symbol.iterator&&l,s=r[e](...o),u=n?nt:t?zt:$t;return t||ze(i,0,"keys"===e&&l?Ie:De),{next(){let{value:e,done:t}=s.next();return t?{value:e,done:t}:{value:a?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function pt(e){return function(...t){return"delete"!==e&&("clear"===e?void 0:this)}}let[vt,ht,gt,mt]=function(){let e={get(e){return rt(this,e)},get size(){return lt(this)},has:it,add:at,set:st,delete:ut,clear:ct,forEach:dt(!1,!1)},t={get(e){return rt(this,e,!1,!0)},get size(){return lt(this)},has:it,add(e){return at.call(this,e,!0)},set(e,t){return st.call(this,e,t,!0)},delete:ut,clear:ct,forEach:dt(!1,!0)},n={get(e){return rt(this,e,!0)},get size(){return lt(this,!0)},has(e){return it.call(this,e,!0)},add:pt("add"),set:pt("set"),delete:pt("delete"),clear:pt("clear"),forEach:dt(!0,!1)},o={get(e){return rt(this,e,!0,!0)},get size(){return lt(this,!0)},has(e){return it.call(this,e,!0)},add:pt("add"),set:pt("set"),delete:pt("delete"),clear:pt("clear"),forEach:dt(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach((r=>{e[r]=ft(r,!1,!1),n[r]=ft(r,!0,!1),t[r]=ft(r,!1,!0),o[r]=ft(r,!0,!0)})),[e,n,t,o]}();function yt(e,t){let n=t?e?mt:gt:e?ht:vt;return(t,o,r)=>"__v_isReactive"===o?!e:"__v_isReadonly"===o?e:"__v_raw"===o?t:Reflect.get(E(n,o)&&o in t?n:t,o,r)}let bt={get:yt(!1,!1)},wt={get:yt(!1,!0)},xt={get:yt(!0,!1)},_t=new WeakMap,St=new WeakMap,Et=new WeakMap,kt=new WeakMap;function Ct(e){return Pt(e)?e:Nt(e,!1,Qe,bt,_t)}function Ot(e){return Nt(e,!0,et,xt,Et)}function Nt(e,t,n,o,r){if(!A(e)||e.__v_raw&&(!t||!e.__v_isReactive))return e;let i=r.get(e);if(i)return i;let l=e.__v_skip||!Object.isExtensible(e)?0:function(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}(j(e));if(0===l)return e;let a=new Proxy(e,2===l?o:n);return r.set(e,a),a}function Mt(e){return Pt(e)?Mt(e.__v_raw):!(!e||!e.__v_isReactive)}function Pt(e){return!(!e||!e.__v_isReadonly)}function Tt(e){return!(!e||!e.__v_isShallow)}function At(e){return!!e&&!!e.__v_raw}function Dt(e){let t=e&&e.__v_raw;return t?Dt(t):e}function It(e){return Object.isExtensible(e)&&Z(e,"__v_skip",!0),e}let $t=e=>A(e)?Ct(e):e,zt=e=>A(e)?Ot(e):e;function jt(e){return!!e&&!0===e.__v_isRef}function Bt(e){return Lt(e,!1)}function Lt(e,t){return jt(e)?e:new Rt(e,t)}class Rt{constructor(e,t){this.dep=new Te,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:Dt(e),this._value=t?e:$t(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,n=this.__v_isShallow||Tt(e)||Pt(e);q(e=n?e:Dt(e),t)&&(this._rawValue=e,this._value=n?e:$t(e),this.dep.trigger())}}function Ft(e){return jt(e)?e.value:e}function Vt(e){return M(e)?e():Ft(e)}let Ht={get:(e,t,n)=>Ft(Reflect.get(e,t,n)),set:(e,t,n,o)=>{let r=e[t];return jt(r)&&!jt(n)?(r.value=n,!0):Reflect.set(e,t,n,o)}};function Ut(e){return Mt(e)?e:new Proxy(e,Ht)}class Yt{constructor(e){this.__v_isRef=!0,this._value=void 0;let t=this.dep=new Te,{get:n,set:o}=e(t.track.bind(t),t.trigger.bind(t));this._get=n,this._set=o}get value(){return this._value=this._get()}set value(e){this._set(e)}}function Xt(e){return new Yt(e)}class Gt{constructor(e,t,n){this._object=e,this._key=t,this._defaultValue=n,this.__v_isRef=!0,this._value=void 0}get value(){let e=this._object[this._key];return this._value=void 0===e?this._defaultValue:e}set value(e){this._object[this._key]=e}get dep(){var e,t,n;return e=Dt(this._object),t=this._key,null==(n=Ae.get(e))?void 0:n.get(t)}}class qt{constructor(e){this._getter=e,this.__v_isRef=!0,this.__v_isReadonly=!0,this._value=void 0}get value(){return this._value=this._getter()}}function Wt(e,t,n){return jt(e)?e:M(e)?new qt(e):A(e)&&arguments.length>1?Zt(e,t,n):Bt(e)}function Zt(e,t,n){let o=e[t];return jt(o)?o:new Gt(e,t,n)}class Kt{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new Te(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Pe-1,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){i!==this&&(this.flags|=16,this.dep.notify())}get value(){let e=this.dep.track();return Se(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}}let Jt={},Qt=new WeakMap;function en(e,t=1/0,n){if(t<=0||!A(e)||e.__v_skip||(n=n||new Set).has(e))return e;if(n.add(e),t--,jt(e))en(e.value,t,n);else if(k(e))for(let o=0;o<e.length;o++)en(e[o],t,n);else if(O(e)||C(e))e.forEach((e=>{en(e,t,n)}));else if(B(e)){for(let o in e)en(e[o],t,n);for(let o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&en(e[o],t,n)}return e}function tn(e,t,n,o){try{return o?e(...o):e()}catch(e){on(e,t,n)}}function nn(e,t,n,o){if(M(e)){let r=tn(e,t,n,o);return r&&D(r)&&r.catch((e=>{on(e,t,n)})),r}if(k(e)){let r=[];for(let i=0;i<e.length;i++)r.push(nn(e[i],t,n,o));return r}}function on(e,t,n,o=!0){t&&t.vnode;let{errorHandler:r,throwUnhandledErrorInProduction:i}=t&&t.appContext.config||h;if(t){let o=t.parent,i=t.proxy,l=`https://vuejs.org/error-reference/#runtime-${n}`;for(;o;){let t=o.ec;if(t)for(let n=0;n<t.length;n++)if(!1===t[n](e,i,l))return;o=o.parent}if(r)return Oe(),tn(r,null,10,[e,i,l]),void Ne()}!function(e,t,n,o=!0,r=!1){if(r)throw e;console.error(e)}(e,0,0,o,i)}let rn=!1,ln=!1,an=[],sn=0,un=[],cn=null,dn=0,fn=Promise.resolve(),pn=null;function vn(e){let t=pn||fn;return e?t.then(this?e.bind(this):e):t}function hn(e){if(!(1&e.flags)){let t=bn(e),n=an[an.length-1];!n||!(2&e.flags)&&t>=bn(n)?an.push(e):an.splice(function(e){let t=rn?sn+1:0,n=an.length;for(;t<n;){let o=t+n>>>1,r=an[o],i=bn(r);i<e||i===e&&2&r.flags?t=o+1:n=o}return t}(t),0,e),4&e.flags||(e.flags|=1),gn()}}function gn(){rn||ln||(ln=!0,pn=fn.then((function e(t){ln=!1,rn=!0;try{for(sn=0;sn<an.length;sn++){let e=an[sn];e&&!(8&e.flags)&&(tn(e,e.i,e.i?15:14),e.flags&=-2)}}finally{sn=0,an.length=0,yn(),rn=!1,pn=null,(an.length||un.length)&&e()}})))}function mn(e,t,n=(rn?sn+1:0)){for(;n<an.length;n++){let t=an[n];if(t&&2&t.flags){if(e&&t.id!==e.uid)continue;an.splice(n,1),n--,t(),t.flags&=-2}}}function yn(e){if(un.length){let e=[...new Set(un)].sort(((e,t)=>bn(e)-bn(t)));if(un.length=0,cn)return void cn.push(...e);for(dn=0,cn=e;dn<cn.length;dn++){let e=cn[dn];8&e.flags||e(),e.flags&=-2}cn=null,dn=0}}let bn=e=>null==e.id?2&e.flags?-1:1/0:e.id,wn=null,xn=null;function _n(e){let t=wn;return wn=e,xn=e&&e.type.__scopeId||null,t}function Sn(e,t=wn,n){if(!t||e._n)return e;let o=(...n)=>{let r;o._d&&Lr(-1);let i=_n(t);try{r=e(...n)}finally{_n(i),o._d&&Lr(1)}return r};return o._n=!0,o._c=!0,o._d=!0,o}function En(e,t){if(null===wn)return e;let n=mi(wn),o=e.dirs||(e.dirs=[]);for(let e=0;e<t.length;e++){let[r,i,l,a=h]=t[e];r&&(M(r)&&(r={mounted:r,updated:r}),r.deep&&en(i),o.push({dir:r,instance:n,value:i,oldValue:void 0,arg:l,modifiers:a}))}return e}function kn(e,t,n,o){let r=e.dirs,i=t&&t.dirs;for(let l=0;l<r.length;l++){let a=r[l];i&&(a.oldValue=i[l].value);let s=a.dir[o];s&&(Oe(),nn(s,n,8,[e.el,a,e,t]),Ne())}}let Cn=Symbol("_vte"),On=e=>e.__isTeleport,Nn=e=>e&&(e.disabled||""===e.disabled),Mn=e=>"undefined"!=typeof SVGElement&&e instanceof SVGElement,Pn=e=>"function"==typeof MathMLElement&&e instanceof MathMLElement,Tn=(e,t)=>{let n=e&&e.to;return P(n)?t?t(n):null:n};function An(e,t,n,{o:{insert:o},m:r},i=2){0===i&&o(e.targetAnchor,t,n);let{el:l,anchor:a,shapeFlag:s,children:u,props:c}=e,d=2===i;if(d&&o(l,t,n),(!d||Nn(c))&&16&s)for(let e=0;e<u.length;e++)r(u[e],t,n,2);d&&o(a,t,n)}let Dn={name:"Teleport",__isTeleport:!0,process(e,t,n,o,r,i,l,a,s,u){let{mc:c,pc:d,pbc:f,o:{insert:p,querySelector:v,createText:h,createComment:g}}=u,m=Nn(t.props),{shapeFlag:y,children:b,dynamicChildren:w}=t;if(null==e){let e=t.el=h(""),u=t.anchor=h("");p(e,n,o),p(u,n,o);let d=(e,t)=>{16&y&&c(b,e,t,r,i,l,a,s)},f=()=>{let e=t.target=Tn(t.props,v),n=$n(e,t,h,p);e&&("svg"!==l&&Mn(e)?l="svg":"mathml"!==l&&Pn(e)&&(l="mathml"),m||(d(e,n),In(t)))};m&&(d(n,u),In(t)),(e=>e&&(e.defer||""===e.defer))(t.props)?cr(f,i):f()}else{t.el=e.el,t.targetStart=e.targetStart;let o=t.anchor=e.anchor,c=t.target=e.target,p=t.targetAnchor=e.targetAnchor,h=Nn(e.props),g=h?n:c;if("svg"===l||Mn(c)?l="svg":("mathml"===l||Pn(c))&&(l="mathml"),w?(f(e.dynamicChildren,w,g,r,i,l,a),vr(e,t,!0)):s||d(e,t,g,h?o:p,r,i,l,a,!1),m)h?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):An(t,n,o,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){let e=t.target=Tn(t.props,v);e&&An(t,e,null,u,0)}else h&&An(t,c,p,u,1);In(t)}},remove(e,t,n,{um:o,o:{remove:r}},i){let{shapeFlag:l,children:a,anchor:s,targetStart:u,targetAnchor:c,target:d,props:f}=e;if(d&&(r(u),r(c)),i&&r(s),16&l){let e=i||!Nn(f);for(let r=0;r<a.length;r++){let i=a[r];o(i,t,n,e,!!i.dynamicChildren)}}},move:An,hydrate:function(e,t,n,o,r,i,{o:{nextSibling:l,parentNode:a,querySelector:s,insert:u,createText:c}},d){let f=t.target=Tn(t.props,s);if(f){let s=f._lpa||f.firstChild;if(16&t.shapeFlag)if(Nn(t.props))t.anchor=d(l(e),t,a(e),n,o,r,i),t.targetStart=s,t.targetAnchor=s&&l(s);else{t.anchor=l(e);let a=s;for(;a;){if(a&&8===a.nodeType)if("teleport start anchor"===a.data)t.targetStart=a;else if("teleport anchor"===a.data){t.targetAnchor=a,f._lpa=t.targetAnchor&&l(t.targetAnchor);break}a=l(a)}t.targetAnchor||$n(f,t,c,u),d(s&&l(s),t,f,n,o,r,i)}In(t)}return t.anchor&&l(t.anchor)}};function In(e){let t=e.ctx;if(t&&t.ut){let n=e.targetStart;for(;n&&n!==e.targetAnchor;)1===n.nodeType&&n.setAttribute("data-v-owner",t.uid),n=n.nextSibling;t.ut()}}function $n(e,t,n,o){let r=t.targetStart=n(""),i=t.targetAnchor=n("");return r[Cn]=i,e&&(o(r,e),o(i,e)),i}let zn=Symbol("_leaveCb"),jn=Symbol("_enterCb");let Bn=[Function,Array],Ln={mode:String,appear:Boolean,persisted:Boolean,onBeforeEnter:Bn,onEnter:Bn,onAfterEnter:Bn,onEnterCancelled:Bn,onBeforeLeave:Bn,onLeave:Bn,onAfterLeave:Bn,onLeaveCancelled:Bn,onBeforeAppear:Bn,onAppear:Bn,onAfterAppear:Bn,onAppearCancelled:Bn},Rn=e=>{let t=e.subTree;return t.component?Rn(t.component):t};function Fn(e){let t=e[0];if(e.length>1)for(let n of e)if(n.type!==Dr){t=n;break}return t}let Vn={name:"BaseTransition",props:Ln,setup(e,{slots:t}){let n=ai(),o=function(){let e={isMounted:!1,isLeaving:!1,isUnmounting:!1,leavingVNodes:new Map};return lo((()=>{e.isMounted=!0})),uo((()=>{e.isUnmounting=!0})),e}();return()=>{let r=t.default&&qn(t.default(),!0);if(!r||!r.length)return;let i=Fn(r),l=Dt(e),{mode:a}=l;if(o.isLeaving)return Yn(i);let s=Xn(i);if(!s)return Yn(i);let u=Un(s,l,o,n,(e=>u=e));Gn(s,u);let c=n.subTree,d=c&&Xn(c);if(d&&d.type!==Dr&&!Ur(s,d)&&Rn(n).type!==Dr){let e=Un(d,l,o,n);if(Gn(d,e),"out-in"===a&&s.type!==Dr)return o.isLeaving=!0,e.afterLeave=()=>{o.isLeaving=!1,8&n.job.flags||n.update()},Yn(i);"in-out"===a&&s.type!==Dr&&(e.delayLeave=(e,t,n)=>{Hn(o,d)[String(d.key)]=d,e[zn]=()=>{t(),e[zn]=void 0,delete u.delayedLeave},u.delayedLeave=n})}return i}}};function Hn(e,t){let{leavingVNodes:n}=e,o=n.get(t.type);return o||(o=Object.create(null),n.set(t.type,o)),o}function Un(e,t,n,o,r){let{appear:i,mode:l,persisted:a=!1,onBeforeEnter:s,onEnter:u,onAfterEnter:c,onEnterCancelled:d,onBeforeLeave:f,onLeave:p,onAfterLeave:v,onLeaveCancelled:h,onBeforeAppear:g,onAppear:m,onAfterAppear:y,onAppearCancelled:b}=t,w=String(e.key),x=Hn(n,e),_=(e,t)=>{e&&nn(e,o,9,t)},S=(e,t)=>{let n=t[1];_(e,t),k(e)?e.every((e=>e.length<=1))&&n():e.length<=1&&n()},E={mode:l,persisted:a,beforeEnter(t){let o=s;if(!n.isMounted){if(!i)return;o=g||s}t[zn]&&t[zn](!0);let r=x[w];r&&Ur(e,r)&&r.el[zn]&&r.el[zn](),_(o,[t])},enter(e){let t=u,o=c,r=d;if(!n.isMounted){if(!i)return;t=m||u,o=y||c,r=b||d}let l=!1,a=e[jn]=t=>{l||(l=!0,_(t?r:o,[e]),E.delayedLeave&&E.delayedLeave(),e[jn]=void 0)};t?S(t,[e,a]):a()},leave(t,o){let r=String(e.key);if(t[jn]&&t[jn](!0),n.isUnmounting)return o();_(f,[t]);let i=!1,l=t[zn]=n=>{i||(i=!0,o(),_(n?h:v,[t]),t[zn]=void 0,x[r]!==e||delete x[r])};x[r]=e,p?S(p,[t,l]):l()},clone(e){let i=Un(e,t,n,o,r);return r&&r(i),i}};return E}function Yn(e){if(Qn(e))return(e=Wr(e)).children=null,e}function Xn(e){if(!Qn(e))return On(e.type)&&e.children?Fn(e.children):e;let{shapeFlag:t,children:n}=e;if(n){if(16&t)return n[0];if(32&t&&M(n.default))return n.default()}}function Gn(e,t){6&e.shapeFlag&&e.component?Gn(e.component.subTree,t):128&e.shapeFlag?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}function qn(e,t=!1,n){let o=[],r=0;for(let i=0;i<e.length;i++){let l=e[i],a=null==n?l.key:String(n)+String(null!=l.key?l.key:i);l.type===Tr?(128&l.patchFlag&&r++,o=o.concat(qn(l.children,t,a))):(t||l.type!==Dr)&&o.push(null!=a?Wr(l,{key:a}):l)}if(r>1)for(let e=0;e<o.length;e++)o[e].patchFlag=-2;return o}function Wn(e,t){return M(e)?x({name:e.name},t,{setup:e}):e}function Zn(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Kn(e,t,n,o,r=!1){if(k(e))return void e.forEach(((e,i)=>Kn(e,t&&(k(t)?t[i]:t),n,o,r)));if(Jn(o)&&!r)return;let i=4&o.shapeFlag?mi(o.component):o.el,l=r?null:i,{i:a,r:s}=e,u=t&&t.r,c=a.refs===h?a.refs={}:a.refs,d=a.setupState;if(null!=u&&u!==s&&(P(u)?(c[u]=null,E(d,u)&&(d[u]=null)):jt(u)&&(u.value=null)),M(s))tn(s,a,12,[l,c]);else{let t=P(s),o=jt(s);if(t||o){let a=()=>{if(e.f){let n=t?E(d,s)?d[s]:c[s]:s.value;r?k(n)&&_(n,i):k(n)?n.includes(i)||n.push(i):t?(c[s]=[i],E(d,s)&&(d[s]=c[s])):(s.value=[i],e.k&&(c[e.k]=s.value))}else t?(c[s]=l,E(d,s)&&(d[s]=l)):o&&(s.value=l,e.k&&(c[e.k]=l))};l?(a.id=-1,cr(a,n)):a()}}}let Jn=e=>!!e.type.__asyncLoader,Qn=e=>e.type.__isKeepAlive;function eo(e,t){no(e,"a",t)}function to(e,t){no(e,"da",t)}function no(e,t,n=li){let o=e.__wdc||(e.__wdc=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()});if(oo(t,o,n),n){let e=n.parent;for(;e&&e.parent;)Qn(e.parent.vnode)&&function(e,t,n,o){let r=oo(t,e,o,!0);co((()=>{_(o[t],r)}),n)}(o,t,n,e),e=e.parent}}function oo(e,t,n=li,o=!1){if(n){let r=n[e]||(n[e]=[]),i=t.__weh||(t.__weh=(...o)=>{Oe();let r=si(n),i=nn(t,n,e,o);return r(),Ne(),i});return o?r.unshift(i):r.push(i),i}}RegExp,RegExp;let ro=e=>(t,n=li)=>{di&&"sp"!==e||oo(e,((...e)=>t(...e)),n)},io=ro("bm"),lo=ro("m"),ao=ro("bu"),so=ro("u"),uo=ro("bum"),co=ro("um"),fo=ro("sp"),po=ro("rtg"),vo=ro("rtc");function ho(e,t=li){oo("ec",e,t)}let go="components";function mo(e,t){return wo(go,e,!0,t)||e}let yo=Symbol.for("v-ndc");function bo(e){return P(e)?wo(go,e,!1)||e:e||yo}function wo(e,t,n=!0,o=!1){let r=wn||li;if(r){let n=r.type;if(e===go){let e=function(e,t=!0){return M(e)?e.displayName||e.name:e.name||t&&e.__name}(n,!1);if(e&&(e===t||e===H(t)||e===X(H(t))))return n}let i=xo(r[e]||n[e],t)||xo(r.appContext[e],t);return!i&&o?n:i}}function xo(e,t){return e&&(e[t]||e[H(t)]||e[X(H(t))])}function _o(e,t,n,o){let r,i=n&&n[o],l=k(e);if(l||P(e)){let n=l&&Mt(e);n&&(e=Le(e)),r=Array(e.length);for(let o=0,l=e.length;o<l;o++)r[o]=t(n?$t(e[o]):e[o],o,void 0,i&&i[o])}else if("number"==typeof e){r=Array(e);for(let n=0;n<e;n++)r[n]=t(n+1,n,void 0,i&&i[n])}else if(A(e))if(e[Symbol.iterator])r=Array.from(e,((e,n)=>t(e,n,void 0,i&&i[n])));else{let n=Object.keys(e);r=Array(n.length);for(let o=0,l=n.length;o<l;o++){let l=n[o];r[o]=t(e[l],l,o,i&&i[o])}}else r=[];return n&&(n[o]=r),r}function So(e,t,n={},o,r){if(wn.ce||wn.parent&&Jn(wn.parent)&&wn.parent.ce)return"default"!==t&&(n.name=t),jr(),Vr(Tr,null,[qr("slot",n,o&&o())],64);let i=e[t];i&&i._c&&(i._d=!1),jr();let l=i&&Eo(i(n)),a=Vr(Tr,{key:(n.key||l&&l.key||`_${t}`)+(!l&&o?"_fb":"")},l||(o?o():[]),l&&1===e._?64:-2);return!r&&a.scopeId&&(a.slotScopeIds=[a.scopeId+"-s"]),i&&i._c&&(i._d=!0),a}function Eo(e){return e.some((e=>!Hr(e)||!(e.type===Dr||e.type===Tr&&!Eo(e.children))))?e:null}let ko=e=>e?ci(e)?mi(e):ko(e.parent):null,Co=x(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>ko(e.parent),$root:e=>ko(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>zo(e),$forceUpdate:e=>e.f||(e.f=()=>{hn(e.update)}),$nextTick:e=>e.n||(e.n=vn.bind(e.proxy)),$watch:e=>xr.bind(e)}),Oo=(e,t)=>e!==h&&!e.__isScriptSetup&&E(e,t),No={get({_:e},t){let n,o,r;if("__v_skip"===t)return!0;let{ctx:i,setupState:l,data:a,props:s,accessCache:u,type:c,appContext:d}=e;if("$"!==t[0]){let o=u[t];if(void 0!==o)switch(o){case 1:return l[t];case 2:return a[t];case 4:return i[t];case 3:return s[t]}else{if(Oo(l,t))return u[t]=1,l[t];if(a!==h&&E(a,t))return u[t]=2,a[t];if((n=e.propsOptions[0])&&E(n,t))return u[t]=3,s[t];if(i!==h&&E(i,t))return u[t]=4,i[t];Io&&(u[t]=0)}}let f=Co[t];return f?("$attrs"===t&&ze(e.attrs,0,""),f(e)):(o=c.__cssModules)&&(o=o[t])?o:i!==h&&E(i,t)?(u[t]=4,i[t]):E(r=d.config.globalProperties,t)?r[t]:void 0},set({_:e},t,n){let{data:o,setupState:r,ctx:i}=e;return Oo(r,t)?(r[t]=n,!0):o!==h&&E(o,t)?(o[t]=n,!0):!(E(e.props,t)||"$"===t[0]&&t.slice(1)in e||(i[t]=n,0))},has({_:{data:e,setupState:t,accessCache:n,ctx:o,appContext:r,propsOptions:i}},l){let a;return!!n[l]||e!==h&&E(e,l)||Oo(t,l)||(a=i[0])&&E(a,l)||E(o,l)||E(Co,l)||E(r.config.globalProperties,l)},defineProperty(e,t,n){return null!=n.get?e._.accessCache[t]=0:E(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function Mo(){return To().slots}function Po(){return To().attrs}function To(){let e=ai();return e.setupContext||(e.setupContext=gi(e))}function Ao(e){return k(e)?e.reduce(((e,t)=>(e[t]=null,e)),{}):e}function Do(e,t){let n={};for(let o in e)t.includes(o)||Object.defineProperty(n,o,{enumerable:!0,get:()=>e[o]});return n}let Io=!0;function $o(e,t,n){nn(k(e)?e.map((e=>e.bind(t.proxy))):e.bind(t.proxy),t,n)}function zo(e){let t,n=e.type,{mixins:o,extends:r}=n,{mixins:i,optionsCache:l,config:{optionMergeStrategies:a}}=e.appContext,s=l.get(n);return s?t=s:i.length||o||r?(t={},i.length&&i.forEach((e=>jo(t,e,a,!0))),jo(t,n,a)):t=n,A(n)&&l.set(n,t),t}function jo(e,t,n,o=!1){let{mixins:r,extends:i}=t;for(let l in i&&jo(e,i,n,!0),r&&r.forEach((t=>jo(e,t,n,!0))),t)if(o&&"expose"===l);else{let o=Bo[l]||n&&n[l];e[l]=o?o(e[l],t[l]):t[l]}return e}let Bo={data:Lo,props:Ho,emits:Ho,methods:Vo,computed:Vo,beforeCreate:Fo,created:Fo,beforeMount:Fo,mounted:Fo,beforeUpdate:Fo,updated:Fo,beforeDestroy:Fo,beforeUnmount:Fo,destroyed:Fo,unmounted:Fo,activated:Fo,deactivated:Fo,errorCaptured:Fo,serverPrefetch:Fo,components:Vo,directives:Vo,watch:function(e,t){if(!e)return t;if(!t)return e;let n=x(Object.create(null),e);for(let o in t)n[o]=Fo(e[o],t[o]);return n},provide:Lo,inject:function(e,t){return Vo(Ro(e),Ro(t))}};function Lo(e,t){return t?e?function(){return x(M(e)?e.call(this,this):e,M(t)?t.call(this,this):t)}:t:e}function Ro(e){if(k(e)){let t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Fo(e,t){return e?[...new Set([].concat(e,t))]:t}function Vo(e,t){return e?x(Object.create(null),e,t):t}function Ho(e,t){return e?k(e)&&k(t)?[...new Set([...e,...t])]:x(Object.create(null),Ao(e),Ao(null!=t?t:{})):t}function Uo(){return{app:null,config:{isNativeTag:y,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Yo=0,Xo=null;function Go(e,t){if(li){let n=li.provides,o=li.parent&&li.parent.provides;o===n&&(n=li.provides=Object.create(o)),n[e]=t}}function qo(e,t,n=!1){let o=li||wn;if(o||Xo){let r=Xo?Xo._context.provides:o?null==o.parent?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return n&&M(t)?t.call(o&&o.proxy):t}}let Wo={},Zo=()=>Object.create(Wo),Ko=e=>Object.getPrototypeOf(e)===Wo;function Jo(e,t,n,o){let r,[i,l]=e.propsOptions,a=!1;if(t)for(let s in t){let u;if(R(s))continue;let c=t[s];i&&E(i,u=H(s))?l&&l.includes(u)?(r||(r={}))[u]=c:n[u]=c:kr(e.emitsOptions,s)||s in o&&c===o[s]||(o[s]=c,a=!0)}if(l){let t=Dt(n),o=r||h;for(let r=0;r<l.length;r++){let a=l[r];n[a]=Qo(i,t,a,o[a],e,!E(o,a))}}return a}function Qo(e,t,n,o,r,i){let l=e[n];if(null!=l){let e=E(l,"default");if(e&&void 0===o){let e=l.default;if(l.type!==Function&&!l.skipFactory&&M(e)){let{propsDefaults:i}=r;if(n in i)o=i[n];else{let l=si(r);o=i[n]=e.call(null,t),l()}}else o=e;r.ce&&r.ce._setProp(n,o)}l[0]&&(i&&!e?o=!1:l[1]&&(""===o||o===Y(n))&&(o=!0))}return o}let er=new WeakMap;function tr(e){return!("$"===e[0]||R(e))}let nr=e=>"_"===e[0]||"$stable"===e,or=e=>k(e)?e.map(Jr):[Jr(e)],rr=(e,t,n)=>{if(t._n)return t;let o=Sn(((...e)=>or(t(...e))),n);return o._c=!1,o},ir=(e,t,n)=>{let o=e._ctx;for(let n in e){if(nr(n))continue;let r=e[n];if(M(r))t[n]=rr(0,r,o);else if(null!=r){let e=or(r);t[n]=()=>e}}},lr=(e,t)=>{let n=or(t);e.slots.default=()=>n},ar=(e,t,n)=>{for(let o in t)(n||"_"!==o)&&(e[o]=t[o])},sr=(e,t,n)=>{let o=e.slots=Zo();if(32&e.vnode.shapeFlag){let e=t._;e?(ar(o,t,n),n&&Z(o,"_",e,!0)):ir(t,o)}else t&&lr(e,t)},ur=(e,t,n)=>{let{vnode:o,slots:r}=e,i=!0,l=h;if(32&o.shapeFlag){let e=t._;e?n&&1===e?i=!1:ar(r,t,n):(i=!t.$stable,ir(t,r)),l=t}else t&&(lr(e,t),l={default:1});if(i)for(let e in r)nr(e)||null!=l[e]||delete r[e]},cr=function(e,t){t&&t.pendingBranch?k(e)?t.effects.push(...e):t.effects.push(e):function(e){k(e)?un.push(...e):cn&&-1===e.id?cn.splice(dn+1,0,e):1&e.flags||(un.push(e),4&e.flags||(e.flags|=1)),gn()}(e)};function dr(e){return function(e,t){var n;let o,r;Q().__VUE__=!0;let{insert:i,remove:l,patchProp:a,createElement:s,createText:u,createComment:c,setText:d,setElementText:f,parentNode:p,nextSibling:v,setScopeId:y=m,insertStaticContent:b}=e,w=(e,t,n,o=null,r=null,i=null,l,a=null,s=!!t.dynamicChildren)=>{if(e===t)return;e&&!Ur(e,t)&&(o=te(e),q(e,r,i,!0),e=null),-2===t.patchFlag&&(s=!1,t.dynamicChildren=null);let{type:u,ref:c,shapeFlag:d}=t;switch(u){case Ar:_(e,t,n,o);break;case Dr:S(e,t,n,o);break;case Ir:null==e&&k(t,n,o,l);break;case Tr:$(e,t,n,o,r,i,l,a,s);break;default:1&d?C(e,t,n,o,r,i,l,a,s):6&d?z(e,t,n,o,r,i,l,a,s):(64&d||128&d)&&u.process(e,t,n,o,r,i,l,a,s,re)}null!=c&&r&&Kn(c,e&&e.ref,i,t||e,!t)},_=(e,t,n,o)=>{if(null==e)i(t.el=u(t.children),n,o);else{let n=t.el=e.el;t.children!==e.children&&d(n,t.children)}},S=(e,t,n,o)=>{null==e?i(t.el=c(t.children||""),n,o):t.el=e.el},k=(e,t,n,o)=>{[e.el,e.anchor]=b(e.children,t,n,o,e.el,e.anchor)},C=(e,t,n,o,r,i,l,a,s)=>{"svg"===t.type?l="svg":"math"===t.type&&(l="mathml"),null==e?O(t,n,o,r,i,l,a,s):T(e,t,r,i,l,a,s)},O=(e,t,n,o,r,l,u,c)=>{let d,p,{props:v,shapeFlag:h,transition:g,dirs:m}=e;if(d=e.el=s(e.type,l,v&&v.is,v),8&h?f(d,e.children):16&h&&P(e.children,d,null,o,r,fr(e,l),u,c),m&&kn(e,null,o,"created"),N(d,e,e.scopeId,u,o),v){for(let e in v)"value"===e||R(e)||a(d,e,null,v[e],l,o);"value"in v&&a(d,"value",null,v.value,l),(p=v.onVnodeBeforeMount)&&ni(p,o,e)}m&&kn(e,null,o,"beforeMount");let y=function(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}(r,g);y&&g.beforeEnter(d),i(d,t,n),((p=v&&v.onVnodeMounted)||y||m)&&cr((()=>{p&&ni(p,o,e),y&&g.enter(d),m&&kn(e,null,o,"mounted")}),r)},N=(e,t,n,o,r)=>{if(n&&y(e,n),o)for(let t=0;t<o.length;t++)y(e,o[t]);if(r){let n=r.subTree;if(t===n||Pr(n.type)&&(n.ssContent===t||n.ssFallback===t)){let t=r.vnode;N(e,t,t.scopeId,t.slotScopeIds,r.parent)}}},P=(e,t,n,o,r,i,l,a,s=0)=>{for(let u=s;u<e.length;u++)w(null,e[u]=a?Qr(e[u]):Jr(e[u]),t,n,o,r,i,l,a)},T=(e,t,n,o,r,i,l)=>{let s,u=t.el=e.el,{patchFlag:c,dynamicChildren:d,dirs:p}=t;c|=16&e.patchFlag;let v=e.props||h,g=t.props||h;if(n&&pr(n,!1),(s=g.onVnodeBeforeUpdate)&&ni(s,n,t,e),p&&kn(t,e,n,"beforeUpdate"),n&&pr(n,!0),(v.innerHTML&&null==g.innerHTML||v.textContent&&null==g.textContent)&&f(u,""),d?D(e.dynamicChildren,d,u,n,o,fr(t,r),i):l||V(e,t,u,null,n,o,fr(t,r),i,!1),c>0){if(16&c)I(u,v,g,n,r);else if(2&c&&v.class!==g.class&&a(u,"class",null,g.class,r),4&c&&a(u,"style",v.style,g.style,r),8&c){let e=t.dynamicProps;for(let t=0;t<e.length;t++){let o=e[t],i=v[o],l=g[o];(l!==i||"value"===o)&&a(u,o,i,l,r,n)}}1&c&&e.children!==t.children&&f(u,t.children)}else l||null!=d||I(u,v,g,n,r);((s=g.onVnodeUpdated)||p)&&cr((()=>{s&&ni(s,n,t,e),p&&kn(t,e,n,"updated")}),o)},D=(e,t,n,o,r,i,l)=>{for(let a=0;a<t.length;a++){let s=e[a],u=t[a],c=s.el&&(s.type===Tr||!Ur(s,u)||70&s.shapeFlag)?p(s.el):n;w(s,u,c,null,o,r,i,l,!0)}},I=(e,t,n,o,r)=>{if(t!==n){if(t!==h)for(let i in t)R(i)||i in n||a(e,i,t[i],null,r,o);for(let i in n){if(R(i))continue;let l=n[i],s=t[i];l!==s&&"value"!==i&&a(e,i,s,l,r,o)}"value"in n&&a(e,"value",t.value,n.value,r)}},$=(e,t,n,o,r,l,a,s,c)=>{let d=t.el=e?e.el:u(""),f=t.anchor=e?e.anchor:u(""),{patchFlag:p,dynamicChildren:v,slotScopeIds:h}=t;h&&(s=s?s.concat(h):h),null==e?(i(d,n,o),i(f,n,o),P(t.children||[],n,f,r,l,a,s,c)):p>0&&64&p&&v&&e.dynamicChildren?(D(e.dynamicChildren,v,n,r,l,a,s),(null!=t.key||r&&t===r.subTree)&&vr(e,t,!0)):V(e,t,n,f,r,l,a,s,c)},z=(e,t,n,o,r,i,l,a,s)=>{t.slotScopeIds=a,null==e?512&t.shapeFlag?r.ctx.activate(t,n,o,l,s):j(t,n,o,r,i,l,s):B(e,t,s)},j=(e,t,n,o,r,i,l)=>{let a=e.component=ii(e,o,r);Qn(e)&&(a.ctx.renderer=re),fi(a,!1,l),a.asyncDep?(r&&r.registerDep(a,L,l),e.el||S(null,a.subTree=qr(Dr),t,n)):L(a,e,t,n,r,i,l)},B=(e,t,n)=>{let o=t.component=e.component;if(function(e,t,n){let{props:o,children:r,component:i}=e,{props:l,children:a,patchFlag:s}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(!(n&&s>=0))return(!!r||!!a)&&(!a||!a.$stable)||o!==l&&(o?!l||Mr(o,l,u):!!l);if(1024&s)return!0;if(16&s)return o?Mr(o,l,u):!!l;if(8&s){let e=t.dynamicProps;for(let t=0;t<e.length;t++){let n=e[t];if(l[n]!==o[n]&&!kr(u,n))return!0}}return!1}(e,t,n)){if(o.asyncDep&&!o.asyncResolved)return void F(o,t,n);o.next=t,o.update()}else t.el=e.el,o.vnode=t},L=(e,t,n,o,i,l,a)=>{let s=()=>{if(e.isMounted){let t,{next:n,bu:o,u:r,parent:u,vnode:c}=e;{let t=function e(t){let n=t.subTree.component;if(n)return n.asyncDep&&!n.asyncResolved?n:e(n)}(e);if(t)return n&&(n.el=c.el,F(e,n,a)),void t.asyncDep.then((()=>{e.isUnmounted||s()}))}let d=n;pr(e,!1),n?(n.el=c.el,F(e,n,a)):n=c,o&&W(o),(t=n.props&&n.props.onVnodeBeforeUpdate)&&ni(t,u,n,c),pr(e,!0);let f=Cr(e),v=e.subTree;e.subTree=f,w(v,f,p(v.el),te(v),e,i,l),n.el=f.el,null===d&&function({vnode:e,parent:t},n){for(;t;){let o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o!==e)break;(e=t.vnode).el=n,t=t.parent}}(e,f.el),r&&cr(r,i),(t=n.props&&n.props.onVnodeUpdated)&&cr((()=>ni(t,u,n,c)),i)}else{let a,{el:s,props:u}=t,{bm:c,m:d,parent:f,root:p,type:v}=e,h=Jn(t);if(pr(e,!1),c&&W(c),!h&&(a=u&&u.onVnodeBeforeMount)&&ni(a,f,t),pr(e,!0),s&&r){let t=()=>{e.subTree=Cr(e),r(s,e.subTree,e,i,null)};h?v.__asyncHydrate(s,e,t):t()}else{p.ce&&p.ce._injectChildStyle(v);let r=e.subTree=Cr(e);w(null,r,n,o,e,i,l),t.el=r.el}if(d&&cr(d,i),!h&&(a=u&&u.onVnodeMounted)){let e=t;cr((()=>ni(a,f,e)),i)}(256&t.shapeFlag||f&&Jn(f.vnode)&&256&f.vnode.shapeFlag)&&e.a&&cr(e.a,i),e.isMounted=!0,t=n=o=null}};e.scope.on();let u=e.effect=new me(s);e.scope.off();let c=e.update=u.run.bind(u),d=e.job=u.runIfDirty.bind(u);d.i=e,d.id=e.uid,u.scheduler=()=>hn(d),pr(e,!0),c()},F=(e,t,n)=>{t.component=e;let o=e.vnode.props;e.vnode=t,e.next=null,function(e,t,n,o){let{props:r,attrs:i,vnode:{patchFlag:l}}=e,a=Dt(r),[s]=e.propsOptions,u=!1;if(!(o||l>0)||16&l){let o;for(let l in Jo(e,t,r,i)&&(u=!0),a)t&&(E(t,l)||(o=Y(l))!==l&&E(t,o))||(s?n&&(void 0!==n[l]||void 0!==n[o])&&(r[l]=Qo(s,a,l,void 0,e,!0)):delete r[l]);if(i!==a)for(let e in i)t&&E(t,e)||(delete i[e],u=!0)}else if(8&l){let n=e.vnode.dynamicProps;for(let o=0;o<n.length;o++){let l=n[o];if(kr(e.emitsOptions,l))continue;let c=t[l];if(s)if(E(i,l))c!==i[l]&&(i[l]=c,u=!0);else{let t=H(l);r[t]=Qo(s,a,t,c,e,!1)}else c!==i[l]&&(i[l]=c,u=!0)}}u&&je(e.attrs,"set","")}(e,t.props,o,n),ur(e,t.children,n),Oe(),mn(e),Ne()},V=(e,t,n,o,r,i,l,a,s=!1)=>{let u=e&&e.children,c=e?e.shapeFlag:0,d=t.children,{patchFlag:p,shapeFlag:v}=t;if(p>0){if(128&p)return void X(u,d,n,o,r,i,l,a,s);if(256&p)return void U(u,d,n,o,r,i,l,a,s)}8&v?(16&c&&ee(u,r,i),d!==u&&f(n,d)):16&c?16&v?X(u,d,n,o,r,i,l,a,s):ee(u,r,i,!0):(8&c&&f(n,""),16&v&&P(d,n,o,r,i,l,a,s))},U=(e,t,n,o,r,i,l,a,s)=>{let u;t=t||g;let c=(e=e||g).length,d=t.length,f=Math.min(c,d);for(u=0;u<f;u++){let o=t[u]=s?Qr(t[u]):Jr(t[u]);w(e[u],o,n,null,r,i,l,a,s)}c>d?ee(e,r,i,!0,!1,f):P(t,n,o,r,i,l,a,s,f)},X=(e,t,n,o,r,i,l,a,s)=>{let u=0,c=t.length,d=e.length-1,f=c-1;for(;u<=d&&u<=f;){let o=e[u],c=t[u]=s?Qr(t[u]):Jr(t[u]);if(!Ur(o,c))break;w(o,c,n,null,r,i,l,a,s),u++}for(;u<=d&&u<=f;){let o=e[d],u=t[f]=s?Qr(t[f]):Jr(t[f]);if(!Ur(o,u))break;w(o,u,n,null,r,i,l,a,s),d--,f--}if(u>d){if(u<=f){let e=f+1,d=e<c?t[e].el:o;for(;u<=f;)w(null,t[u]=s?Qr(t[u]):Jr(t[u]),n,d,r,i,l,a,s),u++}}else if(u>f)for(;u<=d;)q(e[u],r,i,!0),u++;else{let p,v=u,h=u,m=new Map;for(u=h;u<=f;u++){let e=t[u]=s?Qr(t[u]):Jr(t[u]);null!=e.key&&m.set(e.key,u)}let y=0,b=f-h+1,x=!1,_=0,S=Array(b);for(u=0;u<b;u++)S[u]=0;for(u=v;u<=d;u++){let o,c=e[u];if(y>=b)q(c,r,i,!0);else{if(null!=c.key)o=m.get(c.key);else for(p=h;p<=f;p++)if(0===S[p-h]&&Ur(c,t[p])){o=p;break}void 0===o?q(c,r,i,!0):(S[o-h]=u+1,o>=_?_=o:x=!0,w(c,t[o],n,null,r,i,l,a,s),y++)}}let E=x?function(e){let t,n,o,r,i,l=e.slice(),a=[0],s=e.length;for(t=0;t<s;t++){let s=e[t];if(0!==s){if(e[n=a[a.length-1]]<s){l[t]=n,a.push(t);continue}for(o=0,r=a.length-1;o<r;)e[a[i=o+r>>1]]<s?o=i+1:r=i;s<e[a[o]]&&(o>0&&(l[t]=a[o-1]),a[o]=t)}}for(o=a.length,r=a[o-1];o-- >0;)a[o]=r,r=l[r];return a}(S):g;for(p=E.length-1,u=b-1;u>=0;u--){let e=h+u,d=t[e],f=e+1<c?t[e+1].el:o;0===S[u]?w(null,d,n,f,r,i,l,a,s):x&&(p<0||u!==E[p]?G(d,n,f,2):p--)}}},G=(e,t,n,o,r=null)=>{let{el:l,type:a,transition:s,children:u,shapeFlag:c}=e;if(6&c)G(e.component.subTree,t,n,o);else if(128&c)e.suspense.move(t,n,o);else if(64&c)a.move(e,t,n,re);else if(a!==Tr)if(a!==Ir)if(2!==o&&1&c&&s)if(0===o)s.beforeEnter(l),i(l,t,n),cr((()=>s.enter(l)),r);else{let{leave:e,delayLeave:o,afterLeave:r}=s,a=()=>i(l,t,n),u=()=>{e(l,(()=>{a(),r&&r()}))};o?o(l,a,u):u()}else i(l,t,n);else(({el:e,anchor:t},n,o)=>{let r;for(;e&&e!==t;)r=v(e),i(e,n,o),e=r;i(t,n,o)})(e,t,n);else{i(l,t,n);for(let e=0;e<u.length;e++)G(u[e],t,n,o);i(e.anchor,t,n)}},q=(e,t,n,o=!1,r=!1)=>{let i,{type:l,props:a,ref:s,children:u,dynamicChildren:c,shapeFlag:d,patchFlag:f,dirs:p,cacheIndex:v}=e;if(-2===f&&(r=!1),null!=s&&Kn(s,null,n,e,!0),null!=v&&(t.renderCache[v]=void 0),256&d)return void t.ctx.deactivate(e);let h=1&d&&p,g=!Jn(e);if(g&&(i=a&&a.onVnodeBeforeUnmount)&&ni(i,t,e),6&d)J(e.component,n,o);else{if(128&d)return void e.suspense.unmount(n,o);h&&kn(e,null,t,"beforeUnmount"),64&d?e.type.remove(e,t,n,re,o):c&&!c.hasOnce&&(l!==Tr||f>0&&64&f)?ee(c,t,n,!1,!0):(l===Tr&&384&f||!r&&16&d)&&ee(u,t,n),o&&Z(e)}(g&&(i=a&&a.onVnodeUnmounted)||h)&&cr((()=>{i&&ni(i,t,e),h&&kn(e,null,t,"unmounted")}),n)},Z=e=>{let{type:t,el:n,anchor:o,transition:r}=e;if(t===Tr)return void K(n,o);if(t===Ir)return void(({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=v(e),l(e),e=n;l(t)})(e);let i=()=>{l(n),r&&!r.persisted&&r.afterLeave&&r.afterLeave()};if(1&e.shapeFlag&&r&&!r.persisted){let{leave:t,delayLeave:o}=r,l=()=>t(n,i);o?o(e.el,i,l):l()}else i()},K=(e,t)=>{let n;for(;e!==t;)n=v(e),l(e),e=n;l(t)},J=(e,t,n)=>{let{bum:o,scope:r,job:i,subTree:l,um:a,m:s,a:u}=e;hr(s),hr(u),o&&W(o),r.stop(),i&&(i.flags|=8,q(l,e,t,n)),a&&cr(a,t),cr((()=>{e.isUnmounted=!0}),t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,0===t.deps&&t.resolve())},ee=(e,t,n,o=!1,r=!1,i=0)=>{for(let l=i;l<e.length;l++)q(e[l],t,n,o,r)},te=e=>{if(6&e.shapeFlag)return te(e.component.subTree);if(128&e.shapeFlag)return e.suspense.next();let t=v(e.anchor||e.el),n=t&&t[Cn];return n?v(n):t},ne=!1,oe=(e,t,n)=>{null==e?t._vnode&&q(t._vnode,null,null,!0):w(t._vnode||null,e,t,null,null,null,n),t._vnode=e,ne||(ne=!0,mn(),yn(),ne=!1)},re={p:w,um:q,m:G,r:Z,mt:j,mc:P,pc:V,pbc:D,n:te,o:e};return t&&([o,r]=t(re)),{render:oe,hydrate:o,createApp:(n=o,function(e,t=null){M(e)||(e=x({},e)),null==t||A(t)||(t=null);let o=Uo(),r=new WeakSet,i=[],l=!1,a=o.app={_uid:Yo++,_component:e,_props:t,_container:null,_context:o,_instance:null,version:wi,get config(){return o.config},set config(e){},use:(e,...t)=>(r.has(e)||(e&&M(e.install)?(r.add(e),e.install(a,...t)):M(e)&&(r.add(e),e(a,...t))),a),mixin:e=>(o.mixins.includes(e)||o.mixins.push(e),a),component:(e,t)=>t?(o.components[e]=t,a):o.components[e],directive:(e,t)=>t?(o.directives[e]=t,a):o.directives[e],mount(r,i,s){if(!l){let u=a._ceVNode||qr(e,t);return u.appContext=o,!0===s?s="svg":!1===s&&(s=void 0),i&&n?n(u,r):oe(u,r,s),l=!0,a._container=r,r.__vue_app__=a,mi(u.component)}},onUnmount(e){i.push(e)},unmount(){l&&(nn(i,a._instance,16),oe(null,a._container),delete a._container.__vue_app__)},provide:(e,t)=>(o.provides[e]=t,a),runWithContext(e){let t=Xo;Xo=a;try{return e()}finally{Xo=t}}};return a})}}(e)}function fr({type:e,props:t},n){return"svg"===n&&"foreignObject"===e||"mathml"===n&&"annotation-xml"===e&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function pr({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function vr(e,t,n=!1){let o=e.children,r=t.children;if(k(o)&&k(r))for(let e=0;e<o.length;e++){let t=o[e],i=r[e];!(1&i.shapeFlag)||i.dynamicChildren||((i.patchFlag<=0||32===i.patchFlag)&&((i=r[e]=Qr(r[e])).el=t.el),n||-2===i.patchFlag||vr(t,i)),i.type===Ar&&(i.el=t.el)}}function hr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}let gr=Symbol.for("v-scx"),mr=()=>qo(gr);function yr(e,t){return wr(e,null,t)}function br(e,t,n){return wr(e,t,n)}function wr(e,t,n=h){let o,{immediate:r,deep:i,flush:l,once:a}=n,s=x({},n);if(di)if("sync"===l){let e=mr();o=e.__watcherHandles||(e.__watcherHandles=[])}else{if(t&&!r)return{stop:m,resume:m,pause:m};s.once=!0}let u=li;s.call=(e,t,n)=>nn(e,u,t,n);let c=!1;"post"===l?s.scheduler=e=>{cr(e,u&&u.suspense)}:"sync"!==l&&(c=!0,s.scheduler=(e,t)=>{t?e():hn(e)}),s.augmentJob=e=>{t&&(e.flags|=4),c&&(e.flags|=2,u&&(e.id=u.uid,e.i=u))};let d=function(e,t,n=h){let o,r,i,l,{immediate:a,deep:s,once:u,scheduler:c,augmentJob:d,call:p}=n,v=e=>s?e:Tt(e)||!1===s||0===s?en(e,1):en(e),g=!1,y=!1;if(jt(e)?(r=()=>e.value,g=Tt(e)):Mt(e)?(r=()=>v(e),g=!0):k(e)?(y=!0,g=e.some((e=>Mt(e)||Tt(e))),r=()=>e.map((e=>jt(e)?e.value:Mt(e)?v(e):M(e)?p?p(e,2):e():void 0))):r=M(e)?t?p?()=>p(e,2):e:()=>{if(i){Oe();try{i()}finally{Ne()}}let t=f;f=o;try{return p?p(e,3,[l]):e(l)}finally{f=t}}:m,t&&s){let e=r,t=!0===s?1/0:s;r=()=>en(e(),t)}let b=ve(),w=()=>{o.stop(),b&&_(b.effects,o)};if(u)if(t){let e=t;t=(...t)=>{e(...t),w()}}else{let e=r;r=()=>{e(),w()}}let x=y?Array(e.length).fill(Jt):Jt,S=e=>{if(1&o.flags&&(o.dirty||e))if(t){let e=o.run();if(s||g||(y?e.some(((e,t)=>q(e,x[t]))):q(e,x))){i&&i();let n=f;f=o;try{let n=[e,x===Jt?void 0:y&&x[0]===Jt?[]:x,l];p?p(t,3,n):t(...n),x=e}finally{f=n}}}else o.run()};return d&&d(S),(o=new me(r)).scheduler=c?()=>c(S,!1):S,l=e=>function(e,t=!1,n=f){if(n){let t=Qt.get(n);t||Qt.set(n,t=[]),t.push(e)}}(e,!1,o),i=o.onStop=()=>{let e=Qt.get(o);if(e){if(p)p(e,4);else for(let t of e)t();Qt.delete(o)}},t?a?S(!0):x=o.run():c?c(S.bind(null,!0),!0):o.run(),w.pause=o.pause.bind(o),w.resume=o.resume.bind(o),w.stop=w,w}(e,t,s);return o&&o.push(d),d}function xr(e,t,n){let o,r=this.proxy,i=P(e)?e.includes(".")?_r(r,e):()=>r[e]:e.bind(r,r);M(t)?o=t:(o=t.handler,n=t);let l=si(this),a=wr(i,o.bind(r),n);return l(),a}function _r(e,t){let n=t.split(".");return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}let Sr=(e,t)=>"modelValue"===t||"model-value"===t?e.modelModifiers:e[`${t}Modifiers`]||e[`${H(t)}Modifiers`]||e[`${Y(t)}Modifiers`];function Er(e,t,...n){let o;if(e.isUnmounted)return;let r=e.vnode.props||h,i=n,l=t.startsWith("update:"),a=l&&Sr(r,t.slice(7));a&&(a.trim&&(i=n.map((e=>P(e)?e.trim():e))),a.number&&(i=n.map(K)));let s=r[o=G(t)]||r[o=G(H(t))];!s&&l&&(s=r[o=G(Y(t))]),s&&nn(s,e,6,i);let u=r[o+"Once"];if(u){if(e.emitted){if(e.emitted[o])return}else e.emitted={};e.emitted[o]=!0,nn(u,e,6,i)}}function kr(e,t){return!(!e||!b(t))&&(E(e,(t=t.slice(2).replace(/Once$/,""))[0].toLowerCase()+t.slice(1))||E(e,Y(t))||E(e,t))}function Cr(e){let t,n,{type:o,vnode:r,proxy:i,withProxy:l,propsOptions:[a],slots:s,attrs:u,emit:c,render:d,renderCache:f,props:p,data:v,setupState:h,ctx:g,inheritAttrs:m}=e,y=_n(e);try{if(4&r.shapeFlag){let e=l||i;t=Jr(d.call(e,e,f,p,h,v,g)),n=u}else t=Jr(o.length>1?o(p,{attrs:u,slots:s,emit:c}):o(p,null)),n=o.props?u:Or(u)}catch(n){$r.length=0,on(n,e,1),t=qr(Dr)}let b=t;if(n&&!1!==m){let e=Object.keys(n),{shapeFlag:t}=b;e.length&&7&t&&(a&&e.some(w)&&(n=Nr(n,a)),b=Wr(b,n,!1,!0))}return r.dirs&&((b=Wr(b,null,!1,!0)).dirs=b.dirs?b.dirs.concat(r.dirs):r.dirs),r.transition&&(b.transition=r.transition),t=b,_n(y),t}let Or=e=>{let t;for(let n in e)("class"===n||"style"===n||b(n))&&((t||(t={}))[n]=e[n]);return t},Nr=(e,t)=>{let n={};for(let o in e)w(o)&&o.slice(9)in t||(n[o]=e[o]);return n};function Mr(e,t,n){let o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){let i=o[r];if(t[i]!==e[i]&&!kr(n,i))return!0}return!1}let Pr=e=>e.__isSuspense;let Tr=Symbol.for("v-fgt"),Ar=Symbol.for("v-txt"),Dr=Symbol.for("v-cmt"),Ir=Symbol.for("v-stc"),$r=[],zr=null;function jr(e=!1){$r.push(zr=e?null:[])}let Br=1;function Lr(e){Br+=e,e<0&&zr&&(zr.hasOnce=!0)}function Rr(e){return e.dynamicChildren=Br>0?zr||g:null,$r.pop(),zr=$r[$r.length-1]||null,Br>0&&zr&&zr.push(e),e}function Fr(e,t,n,o,r,i){return Rr(Gr(e,t,n,o,r,i,!0))}function Vr(e,t,n,o,r){return Rr(qr(e,t,n,o,r,!0))}function Hr(e){return!!e&&!0===e.__v_isVNode}function Ur(e,t){return e.type===t.type&&e.key===t.key}let Yr=({key:e})=>null!=e?e:null,Xr=({ref:e,ref_key:t,ref_for:n})=>("number"==typeof e&&(e=""+e),null!=e?P(e)||jt(e)||M(e)?{i:wn,r:e,k:t,f:!!n}:e:null);function Gr(e,t=null,n=null,o=0,r=null,i=(e===Tr?0:1),l=!1,a=!1){let s={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Yr(t),ref:t&&Xr(t),scopeId:xn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:wn};return a?(ei(s,n),128&i&&e.normalize(s)):n&&(s.shapeFlag|=P(n)?8:16),Br>0&&!l&&zr&&(s.patchFlag>0||6&i)&&32!==s.patchFlag&&zr.push(s),s}let qr=function(e,t=null,n=null,o=0,r=null,i=!1){var l;if(e&&e!==yo||(e=Dr),Hr(e)){let o=Wr(e,t,!0);return n&&ei(o,n),Br>0&&!i&&zr&&(6&o.shapeFlag?zr[zr.indexOf(e)]=o:zr.push(o)),o.patchFlag=-2,o}if(M(l=e)&&"__vccOpts"in l&&(e=e.__vccOpts),t){let{class:e,style:n}=t=function(e){return e?At(e)||Ko(e)?x({},e):e:null}(t);e&&!P(e)&&(t.class=re(e)),A(n)&&(At(n)&&!k(n)&&(n=x({},n)),t.style=ee(n))}return Gr(e,t,n,o,r,P(e)?1:Pr(e)?128:On(e)?64:A(e)?4:M(e)?2:0,i,!0)};function Wr(e,t,n=!1,o=!1){let{props:r,ref:i,patchFlag:l,children:a,transition:s}=e,u=t?ti(r||{},t):r,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Yr(u),ref:t&&t.ref?n&&i?k(i)?i.concat(Xr(t)):[i,Xr(t)]:Xr(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Tr?-1===l?16:16|l:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:s,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Wr(e.ssContent),ssFallback:e.ssFallback&&Wr(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return s&&o&&Gn(c,s.clone(c)),c}function Zr(e=" ",t=0){return qr(Ar,null,e,t)}function Kr(e="",t=!1){return t?(jr(),Vr(Dr,null,e)):qr(Dr,null,e)}function Jr(e){return null==e||"boolean"==typeof e?qr(Dr):k(e)?qr(Tr,null,e.slice()):"object"==typeof e?Qr(e):qr(Ar,null,String(e))}function Qr(e){return null===e.el&&-1!==e.patchFlag||e.memo?e:Wr(e)}function ei(e,t){let n=0,{shapeFlag:o}=e;if(null==t)t=null;else if(k(t))n=16;else if("object"==typeof t){if(65&o){let n=t.default;return void(n&&(n._c&&(n._d=!1),ei(e,n()),n._c&&(n._d=!0)))}{n=32;let o=t._;o||Ko(t)?3===o&&wn&&(1===wn.slots._?t._=1:(t._=2,e.patchFlag|=1024)):t._ctx=wn}}else M(t)?(t={default:t,_ctx:wn},n=32):(t=String(t),64&o?(n=16,t=[Zr(t)]):n=8);e.children=t,e.shapeFlag|=n}function ti(...e){let t={};for(let n=0;n<e.length;n++){let o=e[n];for(let e in o)if("class"===e)t.class!==o.class&&(t.class=re([t.class,o.class]));else if("style"===e)t.style=ee([t.style,o.style]);else if(b(e)){let n=t[e],r=o[e];r&&n!==r&&(!k(n)||!n.includes(r))&&(t[e]=n?[].concat(n,r):r)}else""!==e&&(t[e]=o[e])}return t}function ni(e,t,n,o=null){nn(e,t,7,[n,o])}let oi=Uo(),ri=0;function ii(e,t,n){let o=e.type,r=(t?t.appContext:e.appContext)||oi,i={uid:ri++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new fe(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:function e(t,n,o=!1){let r=o?er:n.propsCache,i=r.get(t);if(i)return i;let l=t.props,a={},s=[],u=!1;if(!M(t)){let r=t=>{u=!0;let[o,r]=e(t,n,!0);x(a,o),r&&s.push(...r)};!o&&n.mixins.length&&n.mixins.forEach(r),t.extends&&r(t.extends),t.mixins&&t.mixins.forEach(r)}if(!l&&!u)return A(t)&&r.set(t,g),g;if(k(l))for(let e=0;e<l.length;e++){let t=H(l[e]);tr(t)&&(a[t]=h)}else if(l)for(let e in l){let t=H(e);if(tr(t)){let n=l[e],o=a[t]=k(n)||M(n)?{type:n}:x({},n),r=o.type,i=!1,u=!0;if(k(r))for(let e=0;e<r.length;++e){let t=r[e],n=M(t)&&t.name;if("Boolean"===n){i=!0;break}"String"===n&&(u=!1)}else i=M(r)&&"Boolean"===r.name;o[0]=i,o[1]=u,(i||E(o,"default"))&&s.push(t)}}let c=[a,s];return A(t)&&r.set(t,c),c}(o,r),emitsOptions:function e(t,n,o=!1){let r=n.emitsCache,i=r.get(t);if(void 0!==i)return i;let l=t.emits,a={},s=!1;if(!M(t)){let r=t=>{let o=e(t,n,!0);o&&(s=!0,x(a,o))};!o&&n.mixins.length&&n.mixins.forEach(r),t.extends&&r(t.extends),t.mixins&&t.mixins.forEach(r)}return l||s?(k(l)?l.forEach((e=>a[e]=null)):x(a,l),A(t)&&r.set(t,a),a):(A(t)&&r.set(t,null),null)}(o,r),emit:null,emitted:null,propsDefaults:h,inheritAttrs:o.inheritAttrs,ctx:h,data:h,props:h,attrs:h,slots:h,refs:h,setupState:h,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Er.bind(null,i),e.ce&&e.ce(i),i}let li=null,ai=()=>li||wn;{let e=Q(),t=(t,n)=>{let o;return(o=e[t])||(o=e[t]=[]),o.push(n),e=>{o.length>1?o.forEach((t=>t(e))):o[0](e)}};a=t("__VUE_INSTANCE_SETTERS__",(e=>li=e)),s=t("__VUE_SSR_SETTERS__",(e=>di=e))}let si=e=>{let t=li;return a(e),e.scope.on(),()=>{e.scope.off(),a(t)}},ui=()=>{li&&li.scope.off(),a(null)};function ci(e){return 4&e.vnode.shapeFlag}let di=!1;function fi(e,t=!1,n=!1){t&&s(t);let{props:o,children:r}=e.vnode,i=ci(e);!function(e,t,n,o=!1){let r={},i=Zo();for(let n in e.propsDefaults=Object.create(null),Jo(e,t,r,i),e.propsOptions[0])n in r||(r[n]=void 0);n?e.props=o?r:function(e){return Nt(e,!1,tt,wt,St)}(r):e.type.props?e.props=r:e.props=i,e.attrs=i}(e,o,i,t),sr(e,r,n);let l=i?function(e,t){let n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,No);let{setup:o}=n;if(o){let n=e.setupContext=o.length>1?gi(e):null,r=si(e);Oe();let i=tn(o,e,0,[e.props,n]);if(Ne(),r(),D(i)){if(Jn(e)||Zn(e),i.then(ui,ui),t)return i.then((n=>{pi(e,n,t)})).catch((t=>{on(t,e,0)}));e.asyncDep=i}else pi(e,i,t)}else vi(e,t)}(e,t):void 0;return t&&s(!1),l}function pi(e,t,n){M(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:A(t)&&(e.setupState=Ut(t)),vi(e,n)}function vi(e,t,n){let o=e.type;if(!e.render){if(!t&&u&&!o.render){let t=o.template||zo(e).template;if(t){let{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:i,compilerOptions:l}=o,a=x(x({isCustomElement:n,delimiters:i},r),l);o.render=u(t,a)}}e.render=o.render||m,c&&c(e)}{let t=si(e);Oe();try{!function(e){let t=zo(e),n=e.proxy,o=e.ctx;Io=!1,t.beforeCreate&&$o(t.beforeCreate,e,"bc");let{data:r,computed:i,methods:l,watch:a,provide:s,inject:u,created:c,beforeMount:d,mounted:f,beforeUpdate:p,updated:v,activated:h,deactivated:g,beforeDestroy:y,beforeUnmount:b,destroyed:w,unmounted:x,render:_,renderTracked:S,renderTriggered:E,errorCaptured:C,serverPrefetch:O,expose:N,inheritAttrs:T,components:D,directives:I,filters:$}=t;if(u&&function(e,t){for(let n in k(e)&&(e=Ro(e)),e){let o,r=e[n];jt(o=A(r)?"default"in r?qo(r.from||n,r.default,!0):qo(r.from||n):qo(r))?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:e=>o.value=e}):t[n]=o}}(u,o),l)for(let e in l){let t=l[e];M(t)&&(o[e]=t.bind(n))}if(r){let t=r.call(n,n);A(t)&&(e.data=Ct(t))}if(Io=!0,i)for(let e in i){let t=i[e],r=M(t)?t.bind(n,n):M(t.get)?t.get.bind(n,n):m,l=yi({get:r,set:!M(t)&&M(t.set)?t.set.bind(n):m});Object.defineProperty(o,e,{enumerable:!0,configurable:!0,get:()=>l.value,set:e=>l.value=e})}if(a)for(let e in a)!function e(t,n,o,r){let i=r.includes(".")?_r(o,r):()=>o[r];if(P(t)){let e=n[t];M(e)&&br(i,e)}else if(M(t))br(i,t.bind(o));else if(A(t))if(k(t))t.forEach((t=>e(t,n,o,r)));else{let e=M(t.handler)?t.handler.bind(o):n[t.handler];M(e)&&br(i,e,t)}}(a[e],o,n,e);if(s){let e=M(s)?s.call(n):s;Reflect.ownKeys(e).forEach((t=>{Go(t,e[t])}))}function z(e,t){k(t)?t.forEach((t=>e(t.bind(n)))):t&&e(t.bind(n))}if(c&&$o(c,e,"c"),z(io,d),z(lo,f),z(ao,p),z(so,v),z(eo,h),z(to,g),z(ho,C),z(vo,S),z(po,E),z(uo,b),z(co,x),z(fo,O),k(N))if(N.length){let t=e.exposed||(e.exposed={});N.forEach((e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t})}))}else e.exposed||(e.exposed={});_&&e.render===m&&(e.render=_),null!=T&&(e.inheritAttrs=T),D&&(e.components=D),I&&(e.directives=I),O&&Zn(e)}(e)}finally{Ne(),t()}}}let hi={get:(e,t)=>(ze(e,0,""),e[t])};function gi(e){return{attrs:new Proxy(e.attrs,hi),slots:e.slots,emit:e.emit,expose:t=>{e.exposed=t||{}}}}function mi(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Ut(It(e.exposed)),{get:(t,n)=>n in t?t[n]:n in Co?Co[n](e):void 0,has:(e,t)=>t in e||t in Co})):e.proxy}let yi=(e,t)=>function(e,t,n=!1){let o,r;return M(e)?o=e:(o=e.get,r=e.set),new Kt(o,r,n)}(e,0,di);function bi(e,t,n){let o=arguments.length;return 2!==o?(o>3?n=Array.prototype.slice.call(arguments,2):3===o&&Hr(n)&&(n=[n]),qr(e,t,n)):!A(t)||k(t)?qr(e,null,t):Hr(t)?qr(e,null,[t]):qr(e,t)}let wi="3.5.0",xi="undefined"!=typeof window&&window.trustedTypes;if(xi)try{p=xi.createPolicy("vue",{createHTML:e=>e})}catch(o){}let _i=p?e=>p.createHTML(e):e=>e,Si="undefined"!=typeof document?document:null,Ei=Si&&Si.createElement("template"),ki="transition",Ci="animation",Oi=Symbol("_vtc"),Ni=(e,{slots:t})=>bi(Vn,function(e){let t={};for(let n in e)n in Mi||(t[n]=e[n]);if(!1===e.css)return t;let{name:n="v",type:o,duration:r,enterFromClass:i=`${n}-enter-from`,enterActiveClass:l=`${n}-enter-active`,enterToClass:a=`${n}-enter-to`,appearFromClass:s=i,appearActiveClass:u=l,appearToClass:c=a,leaveFromClass:d=`${n}-leave-from`,leaveActiveClass:f=`${n}-leave-active`,leaveToClass:p=`${n}-leave-to`}=e,v=function(e){if(null==e)return null;if(A(e))return[J(e.enter),J(e.leave)];{let t=J(e);return[t,t]}}(r),h=v&&v[0],g=v&&v[1],{onBeforeEnter:m,onEnter:y,onEnterCancelled:b,onLeave:w,onLeaveCancelled:_,onBeforeAppear:S=m,onAppear:E=y,onAppearCancelled:k=b}=t,C=(e,t,n)=>{Di(e,t?c:a),Di(e,t?u:l),n&&n()},O=(e,t)=>{e._isLeaving=!1,Di(e,d),Di(e,p),Di(e,f),t&&t()},N=e=>(t,n)=>{let r=e?E:y,l=()=>C(t,e,n);Pi(r,[t,l]),Ii((()=>{Di(t,e?s:i),Ai(t,e?c:a),Ti(r)||zi(t,o,h,l)}))};return x(t,{onBeforeEnter(e){Pi(m,[e]),Ai(e,i),Ai(e,l)},onBeforeAppear(e){Pi(S,[e]),Ai(e,s),Ai(e,u)},onEnter:N(!1),onAppear:N(!0),onLeave(e,t){e._isLeaving=!0;let n=()=>O(e,t);Ai(e,d),Ai(e,f),document.body.offsetHeight,Ii((()=>{e._isLeaving&&(Di(e,d),Ai(e,p),Ti(w)||zi(e,o,g,n))})),Pi(w,[e,n])},onEnterCancelled(e){C(e,!1),Pi(b,[e])},onAppearCancelled(e){C(e,!0),Pi(k,[e])},onLeaveCancelled(e){O(e),Pi(_,[e])}})}(e),t);Ni.displayName="Transition";let Mi={name:String,type:String,css:{type:Boolean,default:!0},duration:[String,Number,Object],enterFromClass:String,enterActiveClass:String,enterToClass:String,appearFromClass:String,appearActiveClass:String,appearToClass:String,leaveFromClass:String,leaveActiveClass:String,leaveToClass:String},Pi=(Ni.props=x({},Ln,Mi),(e,t=[])=>{k(e)?e.forEach((e=>e(...t))):e&&e(...t)}),Ti=e=>!!e&&(k(e)?e.some((e=>e.length>1)):e.length>1);function Ai(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.add(t))),(e[Oi]||(e[Oi]=new Set)).add(t)}function Di(e,t){t.split(/\s+/).forEach((t=>t&&e.classList.remove(t)));let n=e[Oi];n&&(n.delete(t),n.size||(e[Oi]=void 0))}function Ii(e){requestAnimationFrame((()=>{requestAnimationFrame(e)}))}let $i=0;function zi(e,t,n,o){let r=e._endId=++$i,i=()=>{r===e._endId&&o()};if(n)return setTimeout(i,n);let{type:l,timeout:a,propCount:s}=function(e,t){let n=window.getComputedStyle(e),o=e=>(n[e]||"").split(", "),r=o(`${ki}Delay`),i=o(`${ki}Duration`),l=ji(r,i),a=o(`${Ci}Delay`),s=o(`${Ci}Duration`),u=ji(a,s),c=null,d=0,f=0;return t===ki?l>0&&(c=ki,d=l,f=i.length):t===Ci?u>0&&(c=Ci,d=u,f=s.length):f=(c=(d=Math.max(l,u))>0?l>u?ki:Ci:null)?c===ki?i.length:s.length:0,{type:c,timeout:d,propCount:f,hasTransform:c===ki&&/\b(transform|all)(,|$)/.test(o(`${ki}Property`).toString())}}(e,t);if(!l)return o();let u=l+"end",c=0,d=()=>{e.removeEventListener(u,f),i()},f=t=>{t.target===e&&++c>=s&&d()};setTimeout((()=>{c<s&&d()}),a+1),e.addEventListener(u,f)}function ji(e,t){for(;e.length<t.length;)e=e.concat(e);return Math.max(...t.map(((t,n)=>Bi(t)+Bi(e[n]))))}function Bi(e){return"auto"===e?0:1e3*Number(e.slice(0,-1).replace(",","."))}let Li=Symbol("_vod"),Ri=Symbol("_vsh"),Fi=Symbol(""),Vi=/(^|;)\s*display\s*:/,Hi=/\s*!important$/;function Ui(e,t,n){if(k(n))n.forEach((n=>Ui(e,t,n)));else if(null==n&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{let o=function(e,t){let n=Xi[t];if(n)return n;let o=H(t);if("filter"!==o&&o in e)return Xi[t]=o;o=X(o);for(let n=0;n<Yi.length;n++){let r=Yi[n]+o;if(r in e)return Xi[t]=r}return t}(e,t);Hi.test(n)?e.setProperty(Y(o),n.replace(Hi,""),"important"):e[o]=n}}let Yi=["Webkit","Moz","ms"],Xi={},Gi="http://www.w3.org/1999/xlink";function qi(e,t,n,o,r,i=ie(t)){o&&t.startsWith("xlink:")?null==n?e.removeAttributeNS(Gi,t.slice(6,t.length)):e.setAttributeNS(Gi,t,n):null==n||i&&!n&&""!==n?e.removeAttribute(t):e.setAttribute(t,i?"":T(n)?String(n):n)}function Wi(e,t,n,o){e.addEventListener(t,n,o)}let Zi=Symbol("_vei"),Ki=/(?:Once|Passive|Capture)$/,Ji=0,Qi=Promise.resolve(),el=e=>111===e.charCodeAt(0)&&110===e.charCodeAt(1)&&e.charCodeAt(2)>96&&123>e.charCodeAt(2);"undefined"!=typeof HTMLElement&&HTMLElement,Symbol("_moveCb"),Symbol("_enterCb");let tl=e=>{let t=e.props["onUpdate:modelValue"]||!1;return k(t)?e=>W(t,e):t};function nl(e){e.target.composing=!0}function ol(e){let t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}let rl=Symbol("_assign"),il={created(e,{modifiers:{lazy:t,trim:n,number:o}},r){e[rl]=tl(r);let i=o||r.props&&"number"===r.props.type;Wi(e,t?"change":"input",(t=>{if(t.target.composing)return;let o=e.value;n&&(o=o.trim()),i&&(o=K(o)),e[rl](o)})),n&&Wi(e,"change",(()=>{e.value=e.value.trim()})),t||(Wi(e,"compositionstart",nl),Wi(e,"compositionend",ol),Wi(e,"change",ol))},mounted(e,{value:t}){e.value=null==t?"":t},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:o,trim:r,number:i}},l){if(e[rl]=tl(l),e.composing)return;let a=null==t?"":t;(!i&&"number"!==e.type||/^0\d/.test(e.value)?e.value:K(e.value))===a||document.activeElement===e&&"range"!==e.type&&(o&&t===n||r&&e.value.trim()===a)||(e.value=a)}},ll={deep:!0,created(e,{value:t,modifiers:{number:n}},o){let r=O(t);Wi(e,"change",(()=>{let t=Array.prototype.filter.call(e.options,(e=>e.selected)).map((e=>n?K(sl(e)):sl(e)));e[rl](e.multiple?r?new Set(t):t:t[0]),e._assigning=!0,vn((()=>{e._assigning=!1}))})),e[rl]=tl(o)},mounted(e,{value:t}){al(e,t)},beforeUpdate(e,t,n){e[rl]=tl(n)},updated(e,{value:t}){e._assigning||al(e,t)}};function al(e,t,n){let o=e.multiple,r=k(t);if(!o||r||O(t)){for(let n=0,i=e.options.length;n<i;n++){let i=e.options[n],l=sl(i);if(o)if(r){let e=typeof l;i.selected="string"===e||"number"===e?t.some((e=>String(e)===String(l))):ae(t,l)>-1}else i.selected=t.has(l);else if(le(sl(i),t))return void(e.selectedIndex!==n&&(e.selectedIndex=n))}o||-1===e.selectedIndex||(e.selectedIndex=-1)}}function sl(e){return"_value"in e?e._value:e.value}let ul=x({patchProp:(e,t,n,o,r,i)=>{let l="svg"===r;"class"===t?function(e,t,n){let o=e[Oi];o&&(t=(t?[t,...o]:[...o]).join(" ")),null==t?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}(e,o,l):"style"===t?function(e,t,n){let o=e.style,r=P(n),i=!1;if(n&&!r){if(t)if(P(t))for(let e of t.split(";")){let t=e.slice(0,e.indexOf(":")).trim();null==n[t]&&Ui(o,t,"")}else for(let e in t)null==n[e]&&Ui(o,e,"");for(let e in n)"display"===e&&(i=!0),Ui(o,e,n[e])}else if(r){if(t!==n){let e=o[Fi];e&&(n+=";"+e),o.cssText=n,i=Vi.test(n)}}else t&&e.removeAttribute("style");Li in e&&(e[Li]=i?o.display:"",e[Ri]&&(o.display="none"))}(e,n,o):b(t)?w(t)||function(e,t,n,o,r=null){let i=e[Zi]||(e[Zi]={}),l=i[t];if(o&&l)l.value=o;else{let[n,a]=function(e){let t;if(Ki.test(e)){let n;for(t={};n=e.match(Ki);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[":"===e[2]?e.slice(3):Y(e.slice(2)),t]}(t);o?Wi(e,n,i[t]=function(e,t){let n=e=>{if(e._vts){if(e._vts<=n.attached)return}else e._vts=Date.now();nn(function(e,t){if(!k(t))return t;{let n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map((e=>t=>!t._stopped&&e&&e(t)))}}(e,n.value),t,5,[e])};return n.value=e,n.attached=Ji||(Qi.then((()=>Ji=0)),Ji=Date.now()),n}(o,r),a):l&&(function(e,t,n,o){e.removeEventListener(t,n,o)}(e,n,l,a),i[t]=void 0)}}(e,t,0,o,i):("."===t[0]?(t=t.slice(1),0):"^"===t[0]?(t=t.slice(1),1):!function(e,t,n,o){if(o)return!!("innerHTML"===t||"textContent"===t||t in e&&el(t)&&M(n));if("spellcheck"===t||"draggable"===t||"translate"===t||"form"===t||"list"===t&&"INPUT"===e.tagName||"type"===t&&"TEXTAREA"===e.tagName)return!1;if("width"===t||"height"===t){let t=e.tagName;if("IMG"===t||"VIDEO"===t||"CANVAS"===t||"SOURCE"===t)return!1}return!(el(t)&&P(n)||!(t in e)&&(!e._isVueCE||!/[A-Z]/.test(t)&&P(n)))}(e,t,o,l))?("true-value"===t?e._trueValue=o:"false-value"===t&&(e._falseValue=o),qi(e,t,o,l)):(function(e,t,n){if("innerHTML"===t||"textContent"===t)return void(null!=n&&(e[t]="innerHTML"===t?_i(n):n));let o=e.tagName;if("value"===t&&"PROGRESS"!==o&&!o.includes("-")){let r="OPTION"===o?e.getAttribute("value")||"":e.value,i=null==n?"checkbox"===e.type?"on":"":String(n);return r===i&&"_value"in e||(e.value=i),null==n&&e.removeAttribute(t),void(e._value=n)}let r=!1;if(""===n||null==n){let o=typeof e[t];var i;"boolean"===o?n=!!(i=n)||""===i:null==n&&"string"===o?(n="",r=!0):"number"===o&&(n=0,r=!0)}try{e[t]=n}catch(e){}r&&e.removeAttribute(t)}(e,t,o),e.tagName.includes("-")||"value"!==t&&"checked"!==t&&"selected"!==t||qi(e,t,o,l,0,"value"!==t))}},{insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{let t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,o)=>{let r="svg"===t?Si.createElementNS("http://www.w3.org/2000/svg",e):"mathml"===t?Si.createElementNS("http://www.w3.org/1998/Math/MathML",e):n?Si.createElement(e,{is:n}):Si.createElement(e);return"select"===e&&o&&null!=o.multiple&&r.setAttribute("multiple",o.multiple),r},createText:e=>Si.createTextNode(e),createComment:e=>Si.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Si.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,o,r,i){let l=n?n.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),n),r!==i&&(r=r.nextSibling););else{Ei.innerHTML=_i("svg"===o?`<svg>${e}</svg>`:"mathml"===o?`<math>${e}</math>`:e);let r=Ei.content;if("svg"===o||"mathml"===o){let e=r.firstChild;for(;e.firstChild;)r.appendChild(e.firstChild);r.removeChild(e)}t.insertBefore(r,n)}return[l?l.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}});function cl(e){return!!ve()&&(he(e),!0)}function dl(e){return"function"==typeof e?e():Ft(e)}const fl="undefined"!=typeof window&&"undefined"!=typeof document,pl=Object.prototype.toString,vl=e=>"[object Object]"===pl.call(e),hl=()=>{},gl=e=>e();function ml(e,t=!1,n="Timeout"){return new Promise(((o,r)=>{t?setTimeout((()=>r(n)),e):setTimeout(o,e)}))}function yl(e,t,n={}){const{eventFilter:o=gl,...r}=n;return br(e,(i=o,l=t,function(...e){return new Promise(((t,n)=>{Promise.resolve(i((()=>l.apply(this,e)),{fn:l,thisArg:this,args:e})).then(t).catch(n)}))}),r);var i,l}function bl(e,t,n={}){const{eventFilter:o,...r}=n,{eventFilter:i,pause:l,resume:a,isActive:s}=function(e=gl){const t=Bt(!0);return{isActive:Ot(t),pause:function(){t.value=!1},resume:function(){t.value=!0},eventFilter:(...n)=>{t.value&&e(...n)}}}(o);return{stop:yl(e,t,{...r,eventFilter:i}),pause:l,resume:a,isActive:s}}function wl(e,t={}){if(!jt(e))return function(e){let t=k(e)?Array(e.length):{};for(let n in e)t[n]=Zt(e,n);return t}(e);const n=Array.isArray(e.value)?Array.from({length:e.value.length}):{};for(const o in e.value)n[o]=Xt((()=>({get:()=>e.value[o],set(n){var r;if(null==(r=dl(t.replaceRef))||r)if(Array.isArray(e.value)){const t=[...e.value];t[o]=n,e.value=t}else{const t={...e.value,[o]:n};Object.setPrototypeOf(t,Object.getPrototypeOf(e.value)),e.value=t}else e.value[o]=n}})));return n}function xl(e,t=!1){function n(n,{flush:o="sync",deep:r=!1,timeout:i,throwOnTimeout:l}={}){let a=null;const s=[new Promise((i=>{a=br(e,(e=>{n(e)!==t&&(null==a||a(),i(e))}),{flush:o,deep:r,immediate:!0})}))];return null!=i&&s.push(ml(i,l).then((()=>dl(e))).finally((()=>null==a?void 0:a()))),Promise.race(s)}function o(o,r){if(!jt(o))return n((e=>e===o),r);const{flush:i="sync",deep:l=!1,timeout:a,throwOnTimeout:s}=null!=r?r:{};let u=null;const c=[new Promise((n=>{u=br([e,o],(([e,o])=>{t!==(e===o)&&(null==u||u(),n(e))}),{flush:i,deep:l,immediate:!0})}))];return null!=a&&c.push(ml(a,s).then((()=>dl(e))).finally((()=>(null==u||u(),dl(e))))),Promise.race(c)}function r(e){return i(1,e)}function i(e=1,t){let o=-1;return n((()=>(o+=1,o>=e)),t)}if(Array.isArray(dl(e))){const o={toMatch:n,toContains:function(e,t){return n((t=>{const n=Array.from(t);return n.includes(e)||n.includes(dl(e))}),t)},changed:r,changedTimes:i,get not(){return xl(e,!t)}};return o}{const l={toMatch:n,toBe:o,toBeTruthy:function(e){return n((e=>Boolean(e)),e)},toBeNull:function(e){return o(null,e)},toBeNaN:function(e){return n(Number.isNaN,e)},toBeUndefined:function(e){return o(void 0,e)},changed:r,changedTimes:i,get not(){return xl(e,!t)}};return l}}function _l(e){return xl(e)}function Sl(e){var t;const n=dl(e);return null!=(t=null==n?void 0:n.$el)?t:n}const El=fl?window:void 0;function kl(...e){let t,n,o,r;if("string"==typeof e[0]||Array.isArray(e[0])?([n,o,r]=e,t=El):[t,n,o,r]=e,!t)return hl;Array.isArray(n)||(n=[n]),Array.isArray(o)||(o=[o]);const i=[],l=()=>{i.forEach((e=>e())),i.length=0},a=br((()=>[Sl(t),dl(r)]),(([e,t])=>{if(l(),!e)return;const r=vl(t)?{...t}:t;i.push(...n.flatMap((t=>o.map((n=>((e,t,n,o)=>(e.addEventListener(t,n,o),()=>e.removeEventListener(t,n,o)))(e,t,n,r))))))}),{immediate:!0,flush:"post"}),s=()=>{a(),l()};return cl(s),s}function Cl(...e){let t,n,o={};3===e.length?(t=e[0],n=e[1],o=e[2]):2===e.length?"object"==typeof e[1]?(t=!0,n=e[0],o=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:r=El,eventName:i="keydown",passive:l=!1,dedupe:a=!1}=o,s="function"==typeof(u=t)?u:"string"==typeof u?e=>e.key===u:Array.isArray(u)?e=>u.includes(e.key):()=>!0;var u;return kl(r,i,(e=>{e.repeat&&dl(a)||s(e)&&n(e)}),l)}function Ol(e,t,n,o={}){var r,i,l;const{clone:a=!1,passive:s=!1,eventName:u,deep:c=!1,defaultValue:d,shouldEmit:f}=o,p=ai(),v=n||(null==p?void 0:p.emit)||(null==(r=null==p?void 0:p.$emit)?void 0:r.bind(p))||(null==(l=null==(i=null==p?void 0:p.proxy)?void 0:i.$emit)?void 0:l.bind(null==p?void 0:p.proxy));let h=u;t||(t="modelValue"),h=h||`update:${t.toString()}`;const g=e=>{return a?"function"==typeof a?a(e):(t=e,JSON.parse(JSON.stringify(t))):e;var t},m=()=>void 0!==e[t]?g(e[t]):d,y=e=>{f?f(e)&&v(h,e):v(h,e)};if(s){const n=Bt(m());let o=!1;return br((()=>e[t]),(e=>{o||(o=!0,n.value=g(e),vn((()=>o=!1)))})),br(n,(n=>{o||n===e[t]&&!c||y(n)}),{deep:c}),n}return yi({get:()=>m(),set(e){y(e)}})}var Nl={value:()=>{}};function Ml(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new Pl(o)}function Pl(e){this._=e}function Tl(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function Al(e,t,n){for(var o=0,r=e.length;o<r;++o)if(e[o].name===t){e[o]=Nl,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}Pl.prototype=Ml.prototype={constructor:Pl,on:function(e,t){var n,o,r=this._,i=(o=r,(e+"").trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!o.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:t}}))),l=-1,a=i.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++l<a;)if(n=(e=i[l]).type)r[n]=Al(r[n],e.name,t);else if(null==t)for(n in r)r[n]=Al(r[n],e.name,null);return this}for(;++l<a;)if((n=(e=i[l]).type)&&(n=Tl(r[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new Pl(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var Dl="http://www.w3.org/1999/xhtml";const Il={svg:"http://www.w3.org/2000/svg",xhtml:Dl,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function $l(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),Il.hasOwnProperty(t)?{space:Il[t],local:e}:e}function zl(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===Dl&&t.documentElement.namespaceURI===Dl?t.createElement(e):t.createElementNS(n,e)}}function jl(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function Bl(e){var t=$l(e);return(t.local?jl:zl)(t)}function Ll(){}function Rl(e){return null==e?Ll:function(){return this.querySelector(e)}}function Fl(){return[]}function Vl(e){return null==e?Fl:function(){return this.querySelectorAll(e)}}function Hl(e){return function(){return this.matches(e)}}function Ul(e){return function(t){return t.matches(e)}}var Yl=Array.prototype.find;function Xl(){return this.firstElementChild}var Gl=Array.prototype.filter;function ql(){return Array.from(this.children)}function Wl(e){return new Array(e.length)}function Zl(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function Kl(e,t,n,o,r,i){for(var l,a=0,s=t.length,u=i.length;a<u;++a)(l=t[a])?(l.__data__=i[a],o[a]=l):n[a]=new Zl(e,i[a]);for(;a<s;++a)(l=t[a])&&(r[a]=l)}function Jl(e,t,n,o,r,i,l){var a,s,u,c=new Map,d=t.length,f=i.length,p=new Array(d);for(a=0;a<d;++a)(s=t[a])&&(p[a]=u=l.call(s,s.__data__,a,t)+"",c.has(u)?r[a]=s:c.set(u,s));for(a=0;a<f;++a)u=l.call(e,i[a],a,i)+"",(s=c.get(u))?(o[a]=s,s.__data__=i[a],c.delete(u)):n[a]=new Zl(e,i[a]);for(a=0;a<d;++a)(s=t[a])&&c.get(p[a])===s&&(r[a]=s)}function Ql(e){return e.__data__}function ea(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function ta(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function na(e){return function(){this.removeAttribute(e)}}function oa(e){return function(){this.removeAttributeNS(e.space,e.local)}}function ra(e,t){return function(){this.setAttribute(e,t)}}function ia(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function la(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function aa(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function sa(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function ua(e){return function(){this.style.removeProperty(e)}}function ca(e,t,n){return function(){this.style.setProperty(e,t,n)}}function da(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function fa(e,t){return e.style.getPropertyValue(t)||sa(e).getComputedStyle(e,null).getPropertyValue(t)}function pa(e){return function(){delete this[e]}}function va(e,t){return function(){this[e]=t}}function ha(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function ga(e){return e.trim().split(/^|\s+/)}function ma(e){return e.classList||new ya(e)}function ya(e){this._node=e,this._names=ga(e.getAttribute("class")||"")}function ba(e,t){for(var n=ma(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function wa(e,t){for(var n=ma(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function xa(e){return function(){ba(this,e)}}function _a(e){return function(){wa(this,e)}}function Sa(e,t){return function(){(t.apply(this,arguments)?ba:wa)(this,e)}}function Ea(){this.textContent=""}function ka(e){return function(){this.textContent=e}}function Ca(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function Oa(){this.innerHTML=""}function Na(e){return function(){this.innerHTML=e}}function Ma(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function Pa(){this.nextSibling&&this.parentNode.appendChild(this)}function Ta(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function Aa(){return null}function Da(){var e=this.parentNode;e&&e.removeChild(this)}function Ia(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function $a(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function za(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function ja(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var l=0,a=r.length;l<a;++l)if((o=r[l]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function Ba(e,t,n){var o=sa(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function La(e,t){return function(){return Ba(this,e,t)}}function Ra(e,t){return function(){return Ba(this,e,t.apply(this,arguments))}}Zl.prototype={constructor:Zl,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},ya.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var Fa=[null];function Va(e,t){this._groups=e,this._parents=t}function Ha(){return new Va([[document.documentElement]],Fa)}function Ua(e){return"string"==typeof e?new Va([[document.querySelector(e)]],[document.documentElement]):new Va([[e]],Fa)}function Ya(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}Va.prototype=Ha.prototype={constructor:Va,select:function(e){"function"!=typeof e&&(e=Rl(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,l,a=t[r],s=a.length,u=o[r]=new Array(s),c=0;c<s;++c)(i=a[c])&&(l=e.call(i,i.__data__,c,a))&&("__data__"in i&&(l.__data__=i.__data__),u[c]=l);return new Va(o,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return function(e){return null==e?[]:Array.isArray(e)?e:Array.from(e)}(e.apply(this,arguments))}}(e):Vl(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var l,a=t[i],s=a.length,u=0;u<s;++u)(l=a[u])&&(o.push(e.call(l,l.__data__,u,a)),r.push(l));return new Va(o,r)},selectChild:function(e){return this.select(null==e?Xl:function(e){return function(){return Yl.call(this.children,e)}}("function"==typeof e?e:Ul(e)))},selectChildren:function(e){return this.selectAll(null==e?ql:function(e){return function(){return Gl.call(this.children,e)}}("function"==typeof e?e:Ul(e)))},filter:function(e){"function"!=typeof e&&(e=Hl(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,l=t[r],a=l.length,s=o[r]=[],u=0;u<a;++u)(i=l[u])&&e.call(i,i.__data__,u,l)&&s.push(i);return new Va(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,Ql);var n=t?Jl:Kl,o=this._parents,r=this._groups;"function"!=typeof e&&(e=function(e){return function(){return e}}(e));for(var i=r.length,l=new Array(i),a=new Array(i),s=new Array(i),u=0;u<i;++u){var c=o[u],d=r[u],f=d.length,p=ea(e.call(c,c&&c.__data__,u,o)),v=p.length,h=a[u]=new Array(v),g=l[u]=new Array(v);n(c,d,h,g,s[u]=new Array(f),p,t);for(var m,y,b=0,w=0;b<v;++b)if(m=h[b]){for(b>=w&&(w=b+1);!(y=g[w])&&++w<v;);m._next=y||null}}return(l=new Va(l,o))._enter=a,l._exit=s,l},enter:function(){return new Va(this._enter||this._groups.map(Wl),this._parents)},exit:function(){return new Va(this._exit||this._groups.map(Wl),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,l=Math.min(r,i),a=new Array(r),s=0;s<l;++s)for(var u,c=n[s],d=o[s],f=c.length,p=a[s]=new Array(f),v=0;v<f;++v)(u=c[v]||d[v])&&(p[v]=u);for(;s<r;++s)a[s]=n[s];return new Va(a,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,l=r[i];--i>=0;)(o=r[i])&&(l&&4^o.compareDocumentPosition(l)&&l.parentNode.insertBefore(o,l),l=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=ta);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var l,a=n[i],s=a.length,u=r[i]=new Array(s),c=0;c<s;++c)(l=a[c])&&(u[c]=l);u.sort(t)}return new Va(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var l=o[r];if(l)return l}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],l=0,a=i.length;l<a;++l)(r=i[l])&&e.call(r,r.__data__,l,i);return this},attr:function(e,t){var n=$l(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?oa:na:"function"==typeof t?n.local?aa:la:n.local?ia:ra)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?ua:"function"==typeof t?da:ca)(e,t,null==n?"":n)):fa(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?pa:"function"==typeof t?ha:va)(e,t)):this.node()[e]},classed:function(e,t){var n=ga(e+"");if(arguments.length<2){for(var o=ma(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?Sa:t?xa:_a)(n,t))},text:function(e){return arguments.length?this.each(null==e?Ea:("function"==typeof e?Ca:ka)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?Oa:("function"==typeof e?Ma:Na)(e)):this.node().innerHTML},raise:function(){return this.each(Pa)},lower:function(){return this.each(Ta)},append:function(e){var t="function"==typeof e?e:Bl(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:Bl(e),o=null==t?Aa:"function"==typeof t?t:Rl(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(Da)},clone:function(e){return this.select(e?$a:Ia)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=function(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}(e+""),l=i.length;if(!(arguments.length<2)){for(a=t?ja:za,o=0;o<l;++o)this.each(a(i[o],t,n));return this}var a=this.node().__on;if(a)for(var s,u=0,c=a.length;u<c;++u)for(o=0,s=a[u];o<l;++o)if((r=i[o]).type===s.type&&r.name===s.name)return s.value},dispatch:function(e,t){return this.each(("function"==typeof t?Ra:La)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,l=r.length;i<l;++i)(o=r[i])&&(yield o)}};const Xa={passive:!1},Ga={capture:!0,passive:!1};function qa(e){e.stopImmediatePropagation()}function Wa(e){e.preventDefault(),e.stopImmediatePropagation()}function Za(e){var t=e.document.documentElement,n=Ua(e).on("dragstart.drag",Wa,Ga);"onselectstart"in t?n.on("selectstart.drag",Wa,Ga):(t.__noselect=t.style.MozUserSelect,t.style.MozUserSelect="none")}function Ka(e,t){var n=e.document.documentElement,o=Ua(e).on("dragstart.drag",null);t&&(o.on("click.drag",Wa,Ga),setTimeout((function(){o.on("click.drag",null)}),0)),"onselectstart"in n?o.on("selectstart.drag",null):(n.style.MozUserSelect=n.__noselect,delete n.__noselect)}const Ja=e=>()=>e;function Qa(e,{sourceEvent:t,subject:n,target:o,identifier:r,active:i,x:l,y:a,dx:s,dy:u,dispatch:c}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},subject:{value:n,enumerable:!0,configurable:!0},target:{value:o,enumerable:!0,configurable:!0},identifier:{value:r,enumerable:!0,configurable:!0},active:{value:i,enumerable:!0,configurable:!0},x:{value:l,enumerable:!0,configurable:!0},y:{value:a,enumerable:!0,configurable:!0},dx:{value:s,enumerable:!0,configurable:!0},dy:{value:u,enumerable:!0,configurable:!0},_:{value:c}})}function es(e){return!e.ctrlKey&&!e.button}function ts(){return this.parentNode}function ns(e,t){return null==t?{x:e.x,y:e.y}:t}function os(){return navigator.maxTouchPoints||"ontouchstart"in this}function rs(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function is(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function ls(){}Qa.prototype.on=function(){var e=this._.on.apply(this._,arguments);return e===this._?this:e};var as=.7,ss=1/as,us="\\s*([+-]?\\d+)\\s*",cs="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",ds="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",fs=/^#([0-9a-f]{3,8})$/,ps=new RegExp(`^rgb\\(${us},${us},${us}\\)$`),vs=new RegExp(`^rgb\\(${ds},${ds},${ds}\\)$`),hs=new RegExp(`^rgba\\(${us},${us},${us},${cs}\\)$`),gs=new RegExp(`^rgba\\(${ds},${ds},${ds},${cs}\\)$`),ms=new RegExp(`^hsl\\(${cs},${ds},${ds}\\)$`),ys=new RegExp(`^hsla\\(${cs},${ds},${ds},${cs}\\)$`),bs={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function ws(){return this.rgb().formatHex()}function xs(){return this.rgb().formatRgb()}function _s(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=fs.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?Ss(t):3===n?new Cs(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?Es(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?Es(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=ps.exec(e))?new Cs(t[1],t[2],t[3],1):(t=vs.exec(e))?new Cs(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=hs.exec(e))?Es(t[1],t[2],t[3],t[4]):(t=gs.exec(e))?Es(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=ms.exec(e))?As(t[1],t[2]/100,t[3]/100,1):(t=ys.exec(e))?As(t[1],t[2]/100,t[3]/100,t[4]):bs.hasOwnProperty(e)?Ss(bs[e]):"transparent"===e?new Cs(NaN,NaN,NaN,0):null}function Ss(e){return new Cs(e>>16&255,e>>8&255,255&e,1)}function Es(e,t,n,o){return o<=0&&(e=t=n=NaN),new Cs(e,t,n,o)}function ks(e,t,n,o){return 1===arguments.length?function(e){return e instanceof ls||(e=_s(e)),e?new Cs((e=e.rgb()).r,e.g,e.b,e.opacity):new Cs}(e):new Cs(e,t,n,null==o?1:o)}function Cs(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function Os(){return`#${Ts(this.r)}${Ts(this.g)}${Ts(this.b)}`}function Ns(){const e=Ms(this.opacity);return`${1===e?"rgb(":"rgba("}${Ps(this.r)}, ${Ps(this.g)}, ${Ps(this.b)}${1===e?")":`, ${e})`}`}function Ms(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Ps(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Ts(e){return((e=Ps(e))<16?"0":"")+e.toString(16)}function As(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Is(e,t,n,o)}function Ds(e){if(e instanceof Is)return new Is(e.h,e.s,e.l,e.opacity);if(e instanceof ls||(e=_s(e)),!e)return new Is;if(e instanceof Is)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),l=NaN,a=i-r,s=(i+r)/2;return a?(l=t===i?(n-o)/a+6*(n<o):n===i?(o-t)/a+2:(t-n)/a+4,a/=s<.5?i+r:2-i-r,l*=60):a=s>0&&s<1?0:l,new Is(l,a,s,e.opacity)}function Is(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function $s(e){return(e=(e||0)%360)<0?e+360:e}function zs(e){return Math.max(0,Math.min(1,e||0))}function js(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}rs(ls,_s,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:ws,formatHex:ws,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Ds(this).formatHsl()},formatRgb:xs,toString:xs}),rs(Cs,ks,is(ls,{brighter(e){return e=null==e?ss:Math.pow(ss,e),new Cs(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?as:Math.pow(as,e),new Cs(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Cs(Ps(this.r),Ps(this.g),Ps(this.b),Ms(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Os,formatHex:Os,formatHex8:function(){return`#${Ts(this.r)}${Ts(this.g)}${Ts(this.b)}${Ts(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Ns,toString:Ns})),rs(Is,(function(e,t,n,o){return 1===arguments.length?Ds(e):new Is(e,t,n,null==o?1:o)}),is(ls,{brighter(e){return e=null==e?ss:Math.pow(ss,e),new Is(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?as:Math.pow(as,e),new Is(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new Cs(js(e>=240?e-240:e+120,r,o),js(e,r,o),js(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new Is($s(this.h),zs(this.s),zs(this.l),Ms(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Ms(this.opacity);return`${1===e?"hsl(":"hsla("}${$s(this.h)}, ${100*zs(this.s)}%, ${100*zs(this.l)}%${1===e?")":`, ${e})`}`}}));const Bs=e=>()=>e;function Ls(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):Bs(isNaN(e)?t:e)}const Rs=function e(t){var n=function(e){return 1==(e=+e)?Ls:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):Bs(isNaN(t)?n:t)}}(t);function o(e,t){var o=n((e=ks(e)).r,(t=ks(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),l=Ls(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=l(t),e+""}}return o.gamma=e,o}(1);function Fs(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var Vs=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,Hs=new RegExp(Vs.source,"g");function Us(e,t){var n,o,r,i=Vs.lastIndex=Hs.lastIndex=0,l=-1,a=[],s=[];for(e+="",t+="";(n=Vs.exec(e))&&(o=Hs.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),a[l]?a[l]+=r:a[++l]=r),(n=n[0])===(o=o[0])?a[l]?a[l]+=o:a[++l]=o:(a[++l]=null,s.push({i:l,x:Fs(n,o)})),i=Hs.lastIndex;return i<t.length&&(r=t.slice(i),a[l]?a[l]+=r:a[++l]=r),a.length<2?s[0]?function(e){return function(t){return e(t)+""}}(s[0].x):function(e){return function(){return e}}(t):(t=s.length,function(e){for(var n,o=0;o<t;++o)a[(n=s[o]).i]=n.x(e);return a.join("")})}var Ys,Xs=180/Math.PI,Gs={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function qs(e,t,n,o,r,i){var l,a,s;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(s=e*n+t*o)&&(n-=e*s,o-=t*s),(a=Math.sqrt(n*n+o*o))&&(n/=a,o/=a,s/=a),e*o<t*n&&(e=-e,t=-t,s=-s,l=-l),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*Xs,skewX:Math.atan(s)*Xs,scaleX:l,scaleY:a}}function Ws(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,l){var a=[],s=[];return i=e(i),l=e(l),function(e,o,r,i,l,a){if(e!==r||o!==i){var s=l.push("translate(",null,t,null,n);a.push({i:s-4,x:Fs(e,r)},{i:s-2,x:Fs(o,i)})}else(r||i)&&l.push("translate("+r+t+i+n)}(i.translateX,i.translateY,l.translateX,l.translateY,a,s),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:Fs(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,l.rotate,a,s),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:Fs(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,l.skewX,a,s),function(e,t,n,o,i,l){if(e!==n||t!==o){var a=i.push(r(i)+"scale(",null,",",null,")");l.push({i:a-4,x:Fs(e,n)},{i:a-2,x:Fs(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,l.scaleX,l.scaleY,a,s),i=l=null,function(e){for(var t,n=-1,o=s.length;++n<o;)a[(t=s[n]).i]=t.x(e);return a.join("")}}}var Zs=Ws((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?Gs:qs(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),Ks=Ws((function(e){return null==e?Gs:(Ys||(Ys=document.createElementNS("http://www.w3.org/2000/svg","g")),Ys.setAttribute("transform",e),(e=Ys.transform.baseVal.consolidate())?qs((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):Gs)}),", ",")",")");function Js(e){return((e=Math.exp(e))+1/e)/2}const Qs=function e(t,n,o){function r(e,r){var i,l,a=e[0],s=e[1],u=e[2],c=r[0],d=r[1],f=r[2],p=c-a,v=d-s,h=p*p+v*v;if(h<1e-12)l=Math.log(f/u)/t,i=function(e){return[a+e*p,s+e*v,u*Math.exp(t*e*l)]};else{var g=Math.sqrt(h),m=(f*f-u*u+o*h)/(2*u*n*g),y=(f*f-u*u-o*h)/(2*f*n*g),b=Math.log(Math.sqrt(m*m+1)-m),w=Math.log(Math.sqrt(y*y+1)-y);l=(w-b)/t,i=function(e){var o=e*l,r=Js(b),i=u/(n*g)*(r*function(e){return((e=Math.exp(2*e))-1)/(e+1)}(t*o+b)-function(e){return((e=Math.exp(e))-1/e)/2}(b));return[a+i*p,s+i*v,u*r/Js(t*o+b)]}}return i.duration=1e3*l*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4);var eu,tu,nu=0,ou=0,ru=0,iu=1e3,lu=0,au=0,su=0,uu="object"==typeof performance&&performance.now?performance:Date,cu="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function du(){return au||(cu(fu),au=uu.now()+su)}function fu(){au=0}function pu(){this._call=this._time=this._next=null}function vu(e,t,n){var o=new pu;return o.restart(e,t,n),o}function hu(){au=(lu=uu.now())+su,nu=ou=0;try{!function(){du(),++nu;for(var e,t=eu;t;)(e=au-t._time)>=0&&t._call.call(void 0,e),t=t._next;--nu}()}finally{nu=0,function(){for(var e,t,n=eu,o=1/0;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:eu=t);tu=e,mu(o)}(),au=0}}function gu(){var e=uu.now(),t=e-lu;t>iu&&(su-=t,lu=e)}function mu(e){nu||(ou&&(ou=clearTimeout(ou)),e-au>24?(e<1/0&&(ou=setTimeout(hu,e-uu.now()-su)),ru&&(ru=clearInterval(ru))):(ru||(lu=uu.now(),ru=setInterval(gu,iu)),nu=1,cu(hu)))}function yu(e,t,n){var o=new pu;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}pu.prototype=vu.prototype={constructor:pu,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?du():+n)+(null==t?0:+t),this._next||tu===this||(tu?tu._next=this:eu=this,tu=this),this._call=e,this._time=n,mu()},stop:function(){this._call&&(this._call=null,this._time=1/0,mu())}};var bu=Ml("start","end","cancel","interrupt"),wu=[],xu=0,_u=3;function Su(e,t,n,o,r,i){var l=e.__transition;if(l){if(n in l)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(s){var u,c,d,f;if(1!==n.state)return a();for(u in r)if((f=r[u]).name===n.name){if(f.state===_u)return yu(i);4===f.state?(f.state=6,f.timer.stop(),f.on.call("interrupt",e,e.__data__,f.index,f.group),delete r[u]):+u<t&&(f.state=6,f.timer.stop(),f.on.call("cancel",e,e.__data__,f.index,f.group),delete r[u])}if(yu((function(){n.state===_u&&(n.state=4,n.timer.restart(l,n.delay,n.time),l(s))})),n.state=2,n.on.call("start",e,e.__data__,n.index,n.group),2===n.state){for(n.state=_u,o=new Array(d=n.tween.length),u=0,c=-1;u<d;++u)(f=n.tween[u].value.call(e,e.__data__,n.index,n.group))&&(o[++c]=f);o.length=c+1}}function l(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(a),n.state=5,1),i=-1,l=o.length;++i<l;)o[i].call(e,r);5===n.state&&(n.on.call("end",e,e.__data__,n.index,n.group),a())}function a(){for(var o in n.state=6,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=vu((function(e){n.state=1,n.timer.restart(i,n.delay,n.time),n.delay<=e&&i(e-n.delay)}),0,n.time)}(e,n,{name:t,index:o,group:r,on:bu,tween:wu,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:xu})}function Eu(e,t){var n=Cu(e,t);if(n.state>xu)throw new Error("too late; already scheduled");return n}function ku(e,t){var n=Cu(e,t);if(n.state>_u)throw new Error("too late; already running");return n}function Cu(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Ou(e,t){var n,o,r,i=e.__transition,l=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>2&&n.state<5,n.state=6,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):l=!1;l&&delete e.__transition}}function Nu(e,t){var n,o;return function(){var r=ku(this,e),i=r.tween;if(i!==n)for(var l=0,a=(o=n=i).length;l<a;++l)if(o[l].name===t){(o=o.slice()).splice(l,1);break}r.tween=o}}function Mu(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=ku(this,e),l=i.tween;if(l!==o){r=(o=l).slice();for(var a={name:t,value:n},s=0,u=r.length;s<u;++s)if(r[s].name===t){r[s]=a;break}s===u&&r.push(a)}i.tween=r}}function Pu(e,t,n){var o=e._id;return e.each((function(){var e=ku(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return Cu(e,o).value[t]}}function Tu(e,t){var n;return("number"==typeof t?Fs:t instanceof _s?Rs:(n=_s(t))?(t=n,Rs):Us)(e,t)}function Au(e){return function(){this.removeAttribute(e)}}function Du(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Iu(e,t,n){var o,r,i=n+"";return function(){var l=this.getAttribute(e);return l===i?null:l===o?r:r=t(o=l,n)}}function $u(e,t,n){var o,r,i=n+"";return function(){var l=this.getAttributeNS(e.space,e.local);return l===i?null:l===o?r:r=t(o=l,n)}}function zu(e,t,n){var o,r,i;return function(){var l,a,s=n(this);if(null!=s)return(l=this.getAttribute(e))===(a=s+"")?null:l===o&&a===r?i:(r=a,i=t(o=l,s));this.removeAttribute(e)}}function ju(e,t,n){var o,r,i;return function(){var l,a,s=n(this);if(null!=s)return(l=this.getAttributeNS(e.space,e.local))===(a=s+"")?null:l===o&&a===r?i:(r=a,i=t(o=l,s));this.removeAttributeNS(e.space,e.local)}}function Bu(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&function(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}(e,r)),n}return r._value=t,r}function Lu(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&function(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}(e,r)),n}return r._value=t,r}function Ru(e,t){return function(){Eu(this,e).delay=+t.apply(this,arguments)}}function Fu(e,t){return t=+t,function(){Eu(this,e).delay=t}}function Vu(e,t){return function(){ku(this,e).duration=+t.apply(this,arguments)}}function Hu(e,t){return t=+t,function(){ku(this,e).duration=t}}var Uu=Ha.prototype.constructor;function Yu(e){return function(){this.style.removeProperty(e)}}var Xu=0;function Gu(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function qu(){return++Xu}var Wu=Ha.prototype;Gu.prototype={constructor:Gu,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=Rl(e));for(var o=this._groups,r=o.length,i=new Array(r),l=0;l<r;++l)for(var a,s,u=o[l],c=u.length,d=i[l]=new Array(c),f=0;f<c;++f)(a=u[f])&&(s=e.call(a,a.__data__,f,u))&&("__data__"in a&&(s.__data__=a.__data__),d[f]=s,Su(d[f],t,n,f,d,Cu(a,n)));return new Gu(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=Vl(e));for(var o=this._groups,r=o.length,i=[],l=[],a=0;a<r;++a)for(var s,u=o[a],c=u.length,d=0;d<c;++d)if(s=u[d]){for(var f,p=e.call(s,s.__data__,d,u),v=Cu(s,n),h=0,g=p.length;h<g;++h)(f=p[h])&&Su(f,t,n,h,p,v);i.push(p),l.push(s)}return new Gu(i,l,t,n)},selectChild:Wu.selectChild,selectChildren:Wu.selectChildren,filter:function(e){"function"!=typeof e&&(e=Hl(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,l=t[r],a=l.length,s=o[r]=[],u=0;u<a;++u)(i=l[u])&&e.call(i,i.__data__,u,l)&&s.push(i);return new Gu(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),l=new Array(o),a=0;a<i;++a)for(var s,u=t[a],c=n[a],d=u.length,f=l[a]=new Array(d),p=0;p<d;++p)(s=u[p]||c[p])&&(f[p]=s);for(;a<o;++a)l[a]=t[a];return new Gu(l,this._parents,this._name,this._id)},selection:function(){return new Uu(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=qu(),o=this._groups,r=o.length,i=0;i<r;++i)for(var l,a=o[i],s=a.length,u=0;u<s;++u)if(l=a[u]){var c=Cu(l,t);Su(l,e,n,u,a,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new Gu(o,this._parents,e,n)},call:Wu.call,nodes:Wu.nodes,node:Wu.node,size:Wu.size,empty:Wu.empty,each:Wu.each,on:function(e,t){var n=this._id;return arguments.length<2?Cu(this.node(),n).on.on(e):this.each(function(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?Eu:ku;return function(){var l=i(this,e),a=l.on;a!==o&&(r=(o=a).copy()).on(t,n),l.on=r}}(n,e,t))},attr:function(e,t){var n=$l(e),o="transform"===n?Ks:Tu;return this.attrTween(e,"function"==typeof t?(n.local?ju:zu)(n,o,Pu(this,"attr."+e,t)):null==t?(n.local?Du:Au)(n):(n.local?$u:Iu)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=$l(e);return this.tween(n,(o.local?Bu:Lu)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?Zs:Tu;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=fa(this,e),l=(this.style.removeProperty(e),fa(this,e));return i===l?null:i===n&&l===o?r:r=t(n=i,o=l)}}(e,o)).on("end.style."+e,Yu(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var l=fa(this,e),a=n(this),s=a+"";return null==a&&(this.style.removeProperty(e),s=a=fa(this,e)),l===s?null:l===o&&s===r?i:(r=s,i=t(o=l,a))}}(e,o,Pu(this,"style."+e,t))).each(function(e,t){var n,o,r,i,l="style."+t,a="end."+l;return function(){var s=ku(this,e),u=s.on,c=null==s.value[l]?i||(i=Yu(t)):void 0;u===n&&r===c||(o=(n=u).copy()).on(a,r=c),s.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var l=fa(this,e);return l===i?null:l===o?r:r=t(o=l,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,function(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&function(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}(e,i,n)),o}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(Pu(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,function(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&function(e){return function(t){this.textContent=e.call(this,t)}}(o)),t}return o._value=e,o}(e))},remove:function(){return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}));var e},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=Cu(this.node(),n).tween,i=0,l=r.length;i<l;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?Nu:Mu)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Ru:Fu)(t,e)):Cu(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?Vu:Hu)(t,e)):Cu(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw new Error;return function(){ku(this,e).ease=t}}(t,e)):Cu(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;ku(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,l){var a={value:l},s={value:function(){0==--r&&i()}};n.each((function(){var n=ku(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(a),t._.interrupt.push(a),t._.end.push(s)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:Wu[Symbol.iterator]};var Zu={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function Ku(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}Ha.prototype.interrupt=function(e){return this.each((function(){Ou(this,e)}))},Ha.prototype.transition=function(e){var t,n;e instanceof Gu?(t=e._id,e=e._name):(t=qu(),(n=Zu).time=du(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var l,a=o[i],s=a.length,u=0;u<s;++u)(l=a[u])&&Su(l,e,t,u,a,n||Ku(l,t));return new Gu(o,this._parents,e,t)};const Ju=e=>()=>e;function Qu(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function ec(e,t,n){this.k=e,this.x=t,this.y=n}ec.prototype={constructor:ec,scale:function(e){return 1===e?this:new ec(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new ec(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var tc=new ec(1,0,0);function nc(e){e.stopImmediatePropagation()}function oc(e){e.preventDefault(),e.stopImmediatePropagation()}function rc(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function ic(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function lc(){return this.__zoom||tc}function ac(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function sc(){return navigator.maxTouchPoints||"ontouchstart"in this}function uc(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],l=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),l>i?(i+l)/2:Math.min(0,i)||Math.max(0,l))}ec.prototype;var cc=(e=>(e.Left="left",e.Top="top",e.Right="right",e.Bottom="bottom",e))(cc||{}),dc=(e=>(e.Partial="partial",e.Full="full",e))(dc||{}),fc=(e=>(e.Bezier="default",e.SimpleBezier="simple-bezier",e.Straight="straight",e.Step="step",e.SmoothStep="smoothstep",e))(fc||{}),pc=(e=>(e.Strict="strict",e.Loose="loose",e))(pc||{}),vc=(e=>(e.Arrow="arrow",e.ArrowClosed="arrowclosed",e))(vc||{}),hc=(e=>(e.Free="free",e.Vertical="vertical",e.Horizontal="horizontal",e))(hc||{}),gc=(e=>(e.TopLeft="top-left",e.TopCenter="top-center",e.TopRight="top-right",e.BottomLeft="bottom-left",e.BottomCenter="bottom-center",e.BottomRight="bottom-right",e))(gc||{});function mc(e){var t,n;const o=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target,r="function"==typeof(null==o?void 0:o.hasAttribute)&&o.hasAttribute("contenteditable"),i="function"==typeof(null==o?void 0:o.closest)?o.closest(".nokey"):null;return["INPUT","SELECT","TEXTAREA"].includes(null==o?void 0:o.nodeName)||r||!!i}function yc(e,t,n,o){const r=t.split("+").map((e=>e.trim().toLowerCase()));return 1===r.length?e.toLowerCase()===t.toLowerCase():(o?n.delete(e.toLowerCase()):n.add(e.toLowerCase()),r.every(((e,t)=>n.has(e)&&Array.from(n.values())[t]===r[t])))}function bc(e,t){const n=Wt((()=>Vt(null==t?void 0:t.actInsideInputWithModifier)??!1)),o=Wt((()=>Vt(null==t?void 0:t.target)??window)),r=Bt(!0===Vt(e));let i=!1;const l=new Set;let a=u(Vt(e));function s(){i=!1,l.clear(),r.value=!1}function u(e){return null===e?(s(),()=>!1):"boolean"==typeof e?(s(),r.value=e,()=>!1):Array.isArray(e)||"string"==typeof e?function(e,t){return n=>{if(!n.code&&!n.key)return!1;const o=(r=n.code,"string"==typeof(i=e)?r===i?"code":"key":i.includes(r)?"code":"key");var r,i;return Array.isArray(e)?e.some((e=>yc(n[o],e,t,"keyup"===n.type))):yc(n[o],e,t,"keyup"===n.type)}}(e,l):e}return br((()=>Vt(e)),((e,t)=>{"boolean"==typeof t&&"boolean"!=typeof e&&s(),a=u(e)}),{immediate:!0}),lo((()=>{kl(window,["blur","contextmenu"],s)})),Cl(((...e)=>a(...e)),(e=>{var t;i=(t=e).ctrlKey||t.metaKey||t.shiftKey,(!i||i&&!n.value)&&mc(e)||(e.preventDefault(),r.value=!0)}),{eventName:"keydown",target:o}),Cl(((...e)=>a(...e)),(e=>{if(r.value){if((!i||i&&!n.value)&&mc(e))return;s()}}),{eventName:"keyup",target:o}),r}const wc="vue-flow__node-desc",xc="vue-flow__edge-desc",_c=["Enter"," ","Escape"],Sc={ArrowUp:{x:0,y:-1},ArrowDown:{x:0,y:1},ArrowLeft:{x:-1,y:0},ArrowRight:{x:1,y:0}};function Ec(e){return{...e.computedPosition||{x:0,y:0},width:e.dimensions.width||0,height:e.dimensions.height||0}}function kc(e,t){const n=Math.max(0,Math.min(e.x+e.width,t.x+t.width)-Math.max(e.x,t.x)),o=Math.max(0,Math.min(e.y+e.height,t.y+t.height)-Math.max(e.y,t.y));return Math.ceil(n*o)}function Cc(e){return{width:e.offsetWidth,height:e.offsetHeight}}function Oc(e,t=0,n=1){return Math.min(Math.max(e,t),n)}function Nc(e,t){return{x:Oc(e.x,t[0][0],t[1][0]),y:Oc(e.y,t[0][1],t[1][1])}}function Mc(e){const t=e.getRootNode();return"elementFromPoint"in t?t:window.document}function Pc(e){return e&&"object"==typeof e&&"id"in e&&"source"in e&&"target"in e}function Tc(e){return e&&"object"==typeof e&&"id"in e&&"position"in e&&!Pc(e)}function Ac(e){return Tc(e)&&"computedPosition"in e}function Dc(e){return!Number.isNaN(e)&&Number.isFinite(e)}function Ic(e,t,n){const o={id:e.id.toString(),type:e.type??"default",dimensions:It({width:0,height:0}),computedPosition:It({z:0,...e.position}),handleBounds:{source:[],target:[]},draggable:void 0,selectable:void 0,connectable:void 0,focusable:void 0,selected:!1,dragging:!1,resizing:!1,initialized:!1,isParent:!1,position:{x:0,y:0},data:Nd(e.data)?e.data:{},events:It(Nd(e.events)?e.events:{})};return Object.assign(t??o,e,{id:e.id.toString(),parentNode:n})}function $c(e,t,n){var o,r;const i={id:e.id.toString(),type:e.type??(null==t?void 0:t.type)??"default",source:e.source.toString(),target:e.target.toString(),sourceHandle:null==(o=e.sourceHandle)?void 0:o.toString(),targetHandle:null==(r=e.targetHandle)?void 0:r.toString(),updatable:e.updatable??(null==n?void 0:n.updatable),selectable:e.selectable??(null==n?void 0:n.selectable),focusable:e.focusable??(null==n?void 0:n.focusable),data:Nd(e.data)?e.data:{},events:It(Nd(e.events)?e.events:{}),label:e.label??"",interactionWidth:e.interactionWidth??(null==n?void 0:n.interactionWidth),...n??{}};return Object.assign(t??i,e,{id:e.id.toString()})}function zc(e,t,n,o){const r="string"==typeof e?e:e.id,i=new Set,l="source"===o?"target":"source";for(const e of n)e[l]===r&&i.add(e[o]);return t.filter((e=>i.has(e.id)))}function jc({source:e,sourceHandle:t,target:n,targetHandle:o}){return`vueflow__edge-${e}${t??""}-${n}${o??""}`}function Bc({x:e,y:t},{x:n,y:o,zoom:r}){return{x:e*r+n,y:t*r+o}}function Lc({x:e,y:t},{x:n,y:o,zoom:r},i=!1,[l,a]=[1,1]){const s={x:(e-n)/r,y:(t-o)/r};return i?{x:l*Math.round(s.x/l),y:a*Math.round(s.y/a)}:s}function Rc(e,t){return{x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x2,t.x2),y2:Math.max(e.y2,t.y2)}}function Fc({x:e,y:t,width:n,height:o}){return{x:e,y:t,x2:e+n,y2:t+o}}function Vc({x:e,y:t,x2:n,y2:o}){return{x:e,y:t,width:n-e,height:o-t}}function Hc(e){let t={x:Number.POSITIVE_INFINITY,y:Number.POSITIVE_INFINITY,x2:Number.NEGATIVE_INFINITY,y2:Number.NEGATIVE_INFINITY};for(let n=0;n<e.length;n++){const o=e[n];t=Rc(t,Fc({...o.computedPosition,...o.dimensions}))}return Vc(t)}function Uc(e,t,n={x:0,y:0,zoom:1},o=!1,r=!1){const i={...Lc(t,n),width:t.width/n.zoom,height:t.height/n.zoom},l=[];for(const t of e){const{dimensions:e,selectable:n=!0,hidden:a=!1}=t,s=e.width??t.width??null,u=e.height??t.height??null;if(r&&!n||a)continue;const c=kc(i,Ec(t));(null===s||null===u||o&&c>0||c>=(s??0)*(u??0)||t.dragging)&&l.push(t)}return l}function Yc(e,t){const n=new Set;if("string"==typeof e)n.add(e);else if(e.length>=1)for(const t of e)n.add(t.id);return t.filter((e=>n.has(e.source)||n.has(e.target)))}function Xc(e,t,n,o,r,i=.1,l={x:0,y:0}){const a=t/(e.width*(1+i)),s=n/(e.height*(1+i)),u=Oc(Math.min(a,s),o,r),c=e.x+e.width/2,d=e.y+e.height/2;return{x:t/2-c*u+(l.x??0),y:n/2-d*u+(l.y??0),zoom:u}}function Gc(e,t){if(!e.parentNode)return!1;const n=t(e.parentNode);return!!n&&(!!n.selected||Gc(n,t))}function qc(e,t){return void 0===e?"":"string"==typeof e?e:`${t?`${t}__`:""}${Object.keys(e).sort().map((t=>`${t}=${e[t]}`)).join("&")}`}function Wc(e,t,n){return e<t?Oc(Math.abs(e-t),1,t)/t:e>n?-Oc(Math.abs(e-n),1,t)/t:0}function Zc(e,t,n=15,o=40){return[Wc(e.x,o,t.width-o)*n,Wc(e.y,o,t.height-o)*n]}function Kc(e,t){if(t){const n=e.position.x+e.dimensions.width-t.dimensions.width,o=e.position.y+e.dimensions.height-t.dimensions.height;if(n>0||o>0||e.position.x<0||e.position.y<0){let r={};if("function"==typeof t.style?r={...t.style(t)}:t.style&&(r={...t.style}),r.width=r.width??`${t.dimensions.width}px`,r.height=r.height??`${t.dimensions.height}px`,n>0)if("string"==typeof r.width){const e=Number(r.width.replace("px",""));r.width=`${e+n}px`}else r.width+=n;if(o>0)if("string"==typeof r.height){const e=Number(r.height.replace("px",""));r.height=`${e+o}px`}else r.height+=o;if(e.position.x<0){const n=Math.abs(e.position.x);if(t.position.x=t.position.x-n,"string"==typeof r.width){const e=Number(r.width.replace("px",""));r.width=`${e+n}px`}else r.width+=n;e.position.x=0}if(e.position.y<0){const n=Math.abs(e.position.y);if(t.position.y=t.position.y-n,"string"==typeof r.height){const e=Number(r.height.replace("px",""));r.height=`${e+n}px`}else r.height+=n;e.position.y=0}t.dimensions.width=Number(r.width.toString().replace("px","")),t.dimensions.height=Number(r.height.toString().replace("px","")),"function"==typeof t.style?t.style=e=>({...(0,t.style)(e),...r}):t.style={...t.style,...r}}}}function Jc(e,t){var n,o;const r=e.filter((e=>"add"===e.type||"remove"===e.type));for(const e of r)if("add"===e.type)-1===t.findIndex((t=>t.id===e.item.id))&&t.push(e.item);else if("remove"===e.type){const n=t.findIndex((t=>t.id===e.id));-1!==n&&t.splice(n,1)}const i=t.map((e=>e.id));for(const r of t)for(const l of e)if(l.id===r.id)switch(l.type){case"select":r.selected=l.selected;break;case"position":if(Ac(r)&&(void 0!==l.position&&(r.position=l.position),void 0!==l.dragging&&(r.dragging=l.dragging),r.expandParent&&r.parentNode)){const e=t[i.indexOf(r.parentNode)];e&&Ac(e)&&Kc(r,e)}break;case"dimensions":if(Ac(r)&&(void 0!==l.dimensions&&(r.dimensions=l.dimensions),void 0!==l.updateStyle&&(r.style={...r.style||{},width:`${null==(n=l.dimensions)?void 0:n.width}px`,height:`${null==(o=l.dimensions)?void 0:o.height}px`}),void 0!==l.resizing&&(r.resizing=l.resizing),r.expandParent&&r.parentNode)){const e=t[i.indexOf(r.parentNode)];e&&Ac(e)&&(e.dimensions.width&&e.dimensions.height?Kc(r,e):vn((()=>{Kc(r,e)})))}}return t}function Qc(e,t){return{id:e,type:"select",selected:t}}function ed(e){return{item:e,type:"add"}}function td(e){return{id:e,type:"remove"}}function nd(e,t,n,o,r){return{id:e,source:t,target:n,sourceHandle:o||null,targetHandle:r||null,type:"remove"}}function od(e,t=new Set,n=!1){const o=[];for(const[r,i]of e){const e=t.has(r);void 0===i.selected&&!e||i.selected===e||(n&&(i.selected=e),o.push(Qc(i.id,e)))}return o}function rd(e){const t=new Set;let n=!1;e&&(n=!0,t.add(e));const o=e=>{t.delete(e)};return{on:r=>{e&&n&&t.delete(e),t.add(r);const i=()=>{o(r),e&&n&&t.add(e)};return cl(i),{off:i}},off:o,trigger:e=>Promise.all(Array.from(t).map((t=>t(e)))),hasListeners:()=>t.size>0,fns:t}}function id(e,t,n){let o=e;do{if(o&&o.matches(t))return!0;if(o===n)return!1;o=o.parentElement}while(o);return!1}function ld({id:e,dragItems:t,findNode:n}){const o=[];for(const e of t){const t=n(e.id);t&&o.push(t)}return[e?o.find((t=>t.id===e)):o[0],o]}function ad(e){if(Array.isArray(e))switch(e.length){case 1:return[e[0],e[0],e[0],e[0]];case 2:return[e[0],e[1],e[0],e[1]];case 3:return[e[0],e[1],e[2],e[1]];case 4:return e;default:return[0,0,0,0]}return[e,e,e,e]}function sd(e,t,n,o,r){const i=Nc(t,function({width:e,height:t},n){return[n[0],[n[1][0]-(e||0),n[1][1]-(t||0)]]}(e.dimensions,function(e,t,n,o){let r=e.extent||n;if("parent"!==r&&(Array.isArray(r)||"parent"!==(null==r?void 0:r.range))||e.expandParent){if(Array.isArray(r)){const e=(null==o?void 0:o.computedPosition.x)||0,t=(null==o?void 0:o.computedPosition.y)||0;r=[[r[0][0]+e,r[0][1]+t],[r[1][0]+e,r[1][1]+t]]}else if("parent"!==r&&(null==r?void 0:r.range)&&Array.isArray(r.range)){const[e,t,n,i]=ad(r.padding),l=(null==o?void 0:o.computedPosition.x)||0,a=(null==o?void 0:o.computedPosition.y)||0;r=[[r.range[0][0]+l+i,r.range[0][1]+a+e],[r.range[1][0]+l-t,r.range[1][1]+a-n]]}}else if(e.parentNode&&o&&e.dimensions.width&&e.dimensions.height){const e=function(e,t,n){const[o,r,i,l]="string"!=typeof e?ad(e.padding):[0,0,0,0];return!(!n||void 0===n.computedPosition.x||void 0===n.computedPosition.y||void 0===n.dimensions.width||void 0===n.dimensions.height)&&[[n.computedPosition.x+l,n.computedPosition.y+o],[n.computedPosition.x+n.dimensions.width-r,n.computedPosition.y+n.dimensions.height-i]]}(r,0,o);e&&(r=e)}else t(new hd(pd.NODE_EXTENT_INVALID,e.id)),r=n;return"parent"===r?[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]]:r}(e,n,o,r)));return{position:{x:i.x-((null==r?void 0:r.computedPosition.x)||0),y:i.y-((null==r?void 0:r.computedPosition.y)||0)},computedPosition:i}}function ud(e,t,n=cc.Left){const o=((null==t?void 0:t.x)??0)+e.computedPosition.x,r=((null==t?void 0:t.y)??0)+e.computedPosition.y,{width:i,height:l}=t??function(e){var t,n;return{width:(null==(t=e.dimensions)?void 0:t.width)??e.width??0,height:(null==(n=e.dimensions)?void 0:n.height)??e.height??0}}(e);switch((null==t?void 0:t.position)??n){case cc.Top:return{x:o+i/2,y:r};case cc.Right:return{x:o+i,y:r+l/2};case cc.Bottom:return{x:o+i/2,y:r+l};case cc.Left:return{x:o,y:r+l/2}}}function cd(e=[],t){return e.length&&(t?e.find((e=>e.id===t)):e[0])||null}function dd({sourcePos:e,targetPos:t,sourceWidth:n,sourceHeight:o,targetWidth:r,targetHeight:i,width:l,height:a,viewport:s}){const u={x:Math.min(e.x,t.x),y:Math.min(e.y,t.y),x2:Math.max(e.x+n,t.x+r),y2:Math.max(e.y+o,t.y+i)};u.x===u.x2&&(u.x2+=1),u.y===u.y2&&(u.y2+=1);const c=Fc({x:(0-s.x)/s.zoom,y:(0-s.y)/s.zoom,width:l/s.zoom,height:a/s.zoom}),d=Math.max(0,Math.min(c.x2,u.x2)-Math.max(c.x,u.x)),f=Math.max(0,Math.min(c.y2,u.y2)-Math.max(c.y,u.y));return Math.ceil(d*f)>0}function fd(e,t,n=!1){const o="number"==typeof e.zIndex;let r=o?e.zIndex:0;const i=t(e.source),l=t(e.target);return i&&l?(n&&(r=o?e.zIndex:Math.max(i.computedPosition.z||0,l.computedPosition.z||0)),r):0}var pd=(e=>(e.MISSING_STYLES="MISSING_STYLES",e.MISSING_VIEWPORT_DIMENSIONS="MISSING_VIEWPORT_DIMENSIONS",e.NODE_INVALID="NODE_INVALID",e.NODE_NOT_FOUND="NODE_NOT_FOUND",e.NODE_MISSING_PARENT="NODE_MISSING_PARENT",e.NODE_TYPE_MISSING="NODE_TYPE_MISSING",e.NODE_EXTENT_INVALID="NODE_EXTENT_INVALID",e.EDGE_INVALID="EDGE_INVALID",e.EDGE_NOT_FOUND="EDGE_NOT_FOUND",e.EDGE_SOURCE_MISSING="EDGE_SOURCE_MISSING",e.EDGE_TARGET_MISSING="EDGE_TARGET_MISSING",e.EDGE_TYPE_MISSING="EDGE_TYPE_MISSING",e.EDGE_SOURCE_TARGET_SAME="EDGE_SOURCE_TARGET_SAME",e.EDGE_SOURCE_TARGET_MISSING="EDGE_SOURCE_TARGET_MISSING",e.EDGE_ORPHANED="EDGE_ORPHANED",e.USEVUEFLOW_OPTIONS="USEVUEFLOW_OPTIONS",e))(pd||{});const vd={MISSING_STYLES:()=>"It seems that you haven't loaded the necessary styles. Please import '@vue-flow/core/dist/style.css' to ensure that the graph is rendered correctly",MISSING_VIEWPORT_DIMENSIONS:()=>"The Vue Flow parent container needs a width and a height to render the graph",NODE_INVALID:e=>`Node is invalid\nNode: ${e}`,NODE_NOT_FOUND:e=>`Node not found\nNode: ${e}`,NODE_MISSING_PARENT:(e,t)=>`Node is missing a parent\nNode: ${e}\nParent: ${t}`,NODE_TYPE_MISSING:e=>`Node type is missing\nType: ${e}`,NODE_EXTENT_INVALID:e=>`Only child nodes can use a parent extent\nNode: ${e}`,EDGE_INVALID:e=>`An edge needs a source and a target\nEdge: ${e}`,EDGE_SOURCE_MISSING:(e,t)=>`Edge source is missing\nEdge: ${e} \nSource: ${t}`,EDGE_TARGET_MISSING:(e,t)=>`Edge target is missing\nEdge: ${e} \nTarget: ${t}`,EDGE_TYPE_MISSING:e=>`Edge type is missing\nType: ${e}`,EDGE_SOURCE_TARGET_SAME:(e,t,n)=>`Edge source and target are the same\nEdge: ${e} \nSource: ${t} \nTarget: ${n}`,EDGE_SOURCE_TARGET_MISSING:(e,t,n)=>`Edge source or target is missing\nEdge: ${e} \nSource: ${t} \nTarget: ${n}`,EDGE_ORPHANED:e=>`Edge was orphaned (suddenly missing source or target) and has been removed\nEdge: ${e}`,EDGE_NOT_FOUND:e=>`Edge not found\nEdge: ${e}`,USEVUEFLOW_OPTIONS:()=>"The options parameter is deprecated and will be removed in the next major version. Please use the id parameter instead"};class hd extends Error{constructor(e,...t){var n;super(null==(n=vd[e])?void 0:n.call(vd,...t)),this.name="VueFlowError",this.code=e,this.args=t}}function gd(e){return"clientX"in e}function md(e,t){var n,o;const r=gd(e),i=r?e.clientX:null==(n=e.touches)?void 0:n[0].clientX,l=r?e.clientY:null==(o=e.touches)?void 0:o[0].clientY;return{x:i-((null==t?void 0:t.left)??0),y:l-((null==t?void 0:t.top)??0)}}const yd=()=>{var e;return"undefined"!=typeof navigator&&(null==(e=null==navigator?void 0:navigator.userAgent)?void 0:e.indexOf("Mac"))>=0};function bd(e){null==e||e.classList.remove("valid","connecting","vue-flow__handle-valid","vue-flow__handle-connecting")}function wd(e,t,n,o){const r=[];for(const i of t[n]||[])if(`${e.id}-${i.id}-${n}`!==o){const{x:t,y:o}=ud(e,i);r.push({id:i.id||null,type:n,nodeId:e.id,x:t,y:o})}return r}function xd(e,t,n,o,r,i,l,a,s,u,c){const d="target"===i,f=a.querySelector(`.vue-flow__handle[data-id="${null==t?void 0:t.nodeId}-${null==t?void 0:t.id}-${null==t?void 0:t.type}"]`),{x:p,y:v}=md(e),h=a.elementFromPoint(p,v),g=(null==h?void 0:h.classList.contains("vue-flow__handle"))?h:f,m={handleDomNode:null,isValid:!1,connection:{source:"",target:"",sourceHandle:null,targetHandle:null},endHandle:null};if(g){m.handleDomNode=g;const e=_d(void 0,g),t=g.getAttribute("data-nodeid"),i=g.getAttribute("data-handleid"),a=g.classList.contains("connectable"),f=g.classList.contains("connectableend"),p={source:d?t:o,sourceHandle:d?i:r,target:d?o:t,targetHandle:d?r:i};m.connection=p,a&&f&&(n===pc.Strict?d&&"source"===e||!d&&"target"===e:t!==o||i!==r)&&(m.isValid=l(p,{edges:s,nodes:u,sourceNode:c(p.source),targetNode:c(p.target)}),m.endHandle={nodeId:t,handleId:i,type:e,position:m.isValid?g.getAttribute("data-handlepos"):null})}return m}function _d(e,t){return e||((null==t?void 0:t.classList.contains("target"))?"target":(null==t?void 0:t.classList.contains("source"))?"source":null)}const Sd=["production","prod"];function Ed(e,...t){kd()&&console.warn(`[Vue Flow]: ${e}`,...t)}function kd(){return!Sd.includes("production")}function Cd(e,t,n,o){const r=t.querySelectorAll(`.vue-flow__handle${e}`);return Array.from(r).map((e=>{const t=e.getBoundingClientRect();return{id:e.getAttribute("data-handleid"),position:e.getAttribute("data-handlepos"),x:(t.left-n.left)/o,y:(t.top-n.top)/o,...Cc(e)}}))}function Od(e,t,n,o,r,i=!1,l){r.value=!1,e.selected?(i||e.selected&&t)&&(o([e]),vn((()=>{l.blur()}))):n([e])}function Nd(e){return void 0!==Ft(e)}function Md(e,t,n,o){if(!e||!e.source||!e.target)return n(new hd(pd.EDGE_INVALID,(null==e?void 0:e.id)??"[ID UNKNOWN]")),!1;let r;return r=Pc(e)?e:{...e,id:jc(e)},r=$c(r,void 0,o),!function(e,t){return t.some((t=>Pc(t)&&t.source===e.source&&t.target===e.target&&(t.sourceHandle===e.sourceHandle||!t.sourceHandle&&!e.sourceHandle)&&(t.targetHandle===e.targetHandle||!t.targetHandle&&!e.targetHandle)))}(r,t)&&r}function Pd(e,t,n){const o={},r=[];for(let i=0;i<e.length;++i){const l=e[i];if(!Tc(l)){n(new hd(pd.NODE_INVALID,null==l?void 0:l.id)||`[ID UNKNOWN|INDEX ${i}]`);continue}const a=Ic(l,t(l.id),l.parentNode);l.parentNode&&(o[l.parentNode]=!0),r[i]=a}for(const e of r){const i=t(e.parentNode)||r.find((t=>t.id===e.parentNode));e.parentNode&&!i&&n(new hd(pd.NODE_MISSING_PARENT,e.id,e.parentNode)),(e.parentNode||o[e.id])&&(o[e.id]&&(e.isParent=!0),i&&(i.isParent=!0))}return r}function Td(e,t){e.clear();for(const n of t){const{id:t,source:o,target:r,sourceHandle:i=null,targetHandle:l=null}=n,a=`${o}-source-${i}`,s=`${r}-target-${l}`,u=e.get(a)||new Map,c=e.get(s)||new Map,d=It({edgeId:t,source:o,target:r,sourceHandle:i,targetHandle:l});e.set(a,u.set(`${r}-${l}`,d)),e.set(s,c.set(`${o}-${i}`,d))}}function Ad(e,t,n,o,r,i,l,a){const s=[];for(const u of e){const e=Pc(u)?u:Md(u,a,r,i);if(!e)continue;const c=n(e.source),d=n(e.target);if(!c||!d){r(new hd(pd.EDGE_SOURCE_TARGET_MISSING,e.id,e.source,e.target));continue}if(!c){r(new hd(pd.EDGE_SOURCE_MISSING,e.id,e.source));continue}if(!d){r(new hd(pd.EDGE_TARGET_MISSING,e.id,e.target));continue}if(t&&!t(e,{edges:a,nodes:l,sourceNode:c,targetNode:d})){r(new hd(pd.EDGE_INVALID,e.id));continue}const f=o(e.id);s.push({...$c(e,f,i),sourceNode:c,targetNode:d})}return s}const Dd=Symbol("vueFlow"),Id=Symbol("nodeId"),$d=Symbol("nodeRef"),zd=Symbol("edgeId"),jd=Symbol("edgeRef"),Bd=Symbol("slots");function Ld(e){const{vueFlowRef:t,snapToGrid:n,snapGrid:o,noDragClassName:r,nodes:i,nodeExtent:l,nodeDragThreshold:a,viewport:s,autoPanOnNodeDrag:u,autoPanSpeed:c,nodesDraggable:d,panBy:f,findNode:p,multiSelectionActive:v,nodesSelectionActive:h,selectNodesOnDrag:g,removeSelectedElements:m,addSelectedNodes:y,updateNodePositions:b,emits:w}=Tf(),{onStart:x,onDrag:_,onStop:S,onClick:E,el:k,disabled:C,id:O,selectable:N,dragHandle:M}=e,P=Bt(!1);let T,A=[],D=null,I={x:void 0,y:void 0},$={x:0,y:0},z=null,j=!1,B=0,L=!1;const R=function(){const{viewport:e,snapGrid:t,snapToGrid:n}=Tf();return({sourceEvent:o})=>{const r=o.touches?o.touches[0].clientX:o.clientX,i=o.touches?o.touches[0].clientY:o.clientY,l={x:(r-e.value.x)/e.value.zoom,y:(i-e.value.y)/e.value.zoom};return{xSnapped:n.value?t.value[0]*Math.round(l.x/t.value[0]):l.x,ySnapped:n.value?t.value[1]*Math.round(l.y/t.value[1]):l.y,...l}}}(),F=({x:e,y:t})=>{I={x:e,y:t};let r=!1;if(A=A.map((i=>{const a={x:e-i.distance.x,y:t-i.distance.y};n.value&&(a.x=o.value[0]*Math.round(a.x/o.value[0]),a.y=o.value[1]*Math.round(a.y/o.value[1]));const{computedPosition:s}=sd(i,a,w.error,l.value,i.parentNode?p(i.parentNode):void 0);return r=r||i.position.x!==s.x||i.position.y!==s.y,i.position=s,i})),r&&(b(A,!0,!0),P.value=!0,z)){const[e,t]=ld({id:O,dragItems:A,findNode:p});_({event:z,node:e,nodes:t})}},V=()=>{if(!D)return;const[e,t]=Zc($,D,c.value);if(0!==e||0!==t){const n={x:(I.x??0)-e/s.value.zoom,y:(I.y??0)-t/s.value.zoom};f({x:e,y:t})&&F(n)}B=requestAnimationFrame(V)},H=(e,t)=>{j=!0;const n=p(O);g.value||v.value||!n||n.selected||m(),n&&Vt(N)&&g.value&&Od(n,v.value,y,m,h,!1,t);const o=R(e);if(I=o,A=function(e,t,n,o,r){var i,l;const a=[];for(const s of e)!s.selected&&s.id!==r||s.parentNode&&Gc(s,o)||!(s.draggable||t&&void 0===s.draggable)||a.push(It({id:s.id,position:s.position||{x:0,y:0},distance:{x:n.x-(null==(i=s.computedPosition)?void 0:i.x)||0,y:n.y-(null==(l=s.computedPosition)?void 0:l.y)||0},from:s.computedPosition,extent:s.extent,parentNode:s.parentNode,dimensions:s.dimensions,expandParent:s.expandParent}));return a}(i.value,d.value,o,p,O),A.length){const[t,n]=ld({id:O,dragItems:A,findNode:p});x({event:e.sourceEvent,node:t,nodes:n})}};return br([()=>Vt(C),k],(([e,n],o,i)=>{if(n){const o=Ua(n);e||(T=function(){var e,t,n,o,r=es,i=ts,l=ns,a=os,s={},u=Ml("start","drag","end"),c=0,d=0;function f(e){e.on("mousedown.drag",p).filter(a).on("touchstart.drag",g).on("touchmove.drag",m,Xa).on("touchend.drag touchcancel.drag",y).style("touch-action","none").style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function p(l,a){if(!o&&r.call(this,l,a)){var s=b(this,i.call(this,l,a),l,a,"mouse");s&&(Ua(l.view).on("mousemove.drag",v,Ga).on("mouseup.drag",h,Ga),Za(l.view),qa(l),n=!1,e=l.clientX,t=l.clientY,s("start",l))}}function v(o){if(Wa(o),!n){var r=o.clientX-e,i=o.clientY-t;n=r*r+i*i>d}s.mouse("drag",o)}function h(e){Ua(e.view).on("mousemove.drag mouseup.drag",null),Ka(e.view,n),Wa(e),s.mouse("end",e)}function g(e,t){if(r.call(this,e,t)){var n,o,l=e.changedTouches,a=i.call(this,e,t),s=l.length;for(n=0;n<s;++n)(o=b(this,a,e,t,l[n].identifier,l[n]))&&(qa(e),o("start",e,l[n]))}}function m(e){var t,n,o=e.changedTouches,r=o.length;for(t=0;t<r;++t)(n=s[o[t].identifier])&&(Wa(e),n("drag",e,o[t]))}function y(e){var t,n,r=e.changedTouches,i=r.length;for(o&&clearTimeout(o),o=setTimeout((function(){o=null}),500),t=0;t<i;++t)(n=s[r[t].identifier])&&(qa(e),n("end",e,r[t]))}function b(e,t,n,o,r,i){var a,d,p,v=u.copy(),h=Ya(i||n,t);if(null!=(p=l.call(e,new Qa("beforestart",{sourceEvent:n,target:f,identifier:r,active:c,x:h[0],y:h[1],dx:0,dy:0,dispatch:v}),o)))return a=p.x-h[0]||0,d=p.y-h[1]||0,function n(i,l,u){var g,m=h;switch(i){case"start":s[r]=n,g=c++;break;case"end":delete s[r],--c;case"drag":h=Ya(u||l,t),g=c}v.call(i,e,new Qa(i,{sourceEvent:l,subject:p,target:f,identifier:r,active:g,x:h[0]+a,y:h[1]+d,dx:h[0]-m[0],dy:h[1]-m[1],dispatch:v}),o)}}return f.filter=function(e){return arguments.length?(r="function"==typeof e?e:Ja(!!e),f):r},f.container=function(e){return arguments.length?(i="function"==typeof e?e:Ja(e),f):i},f.subject=function(e){return arguments.length?(l="function"==typeof e?e:Ja(e),f):l},f.touchable=function(e){return arguments.length?(a="function"==typeof e?e:Ja(!!e),f):a},f.on=function(){var e=u.on.apply(u,arguments);return e===u?f:e},f.clickDistance=function(e){return arguments.length?(d=(e=+e)*e,f):Math.sqrt(d)},f}().on("start",(e=>((e,n)=>{var o;"touchmove"===e.sourceEvent.type&&e.sourceEvent.touches.length>1||(0===a.value&&H(e,n),I=R(e),D=(null==(o=t.value)?void 0:o.getBoundingClientRect())||null,$=md(e.sourceEvent,D))})(e,n))).on("drag",(e=>((e,t)=>{const n=R(e);if(!L&&j&&u.value&&(L=!0,V()),!j){const o=n.xSnapped-(I.x??0),r=n.ySnapped-(I.y??0);Math.sqrt(o*o+r*r)>a.value&&H(e,t)}(I.x!==n.xSnapped||I.y!==n.ySnapped)&&A.length&&j&&(z=e.sourceEvent,$=md(e.sourceEvent,D),F(n))})(e,n))).on("end",(e=>(e=>{if(j){if(P.value=!1,L=!1,j=!1,cancelAnimationFrame(B),A.length){b(A,!1,!1);const[t,n]=ld({id:O,dragItems:A,findNode:p});S({event:e.sourceEvent,node:t,nodes:n})}}else{const t=R(e),n=t.xSnapped-(I.x??0),o=t.ySnapped-(I.y??0),r=Math.sqrt(n*n+o*o);0!==r&&r<=a.value&&(null==E||E(e.sourceEvent))}})(e))).filter((e=>{const t=e.target,o=Vt(M);return!e.button&&(!r.value||!id(t,`.${r.value}`,n)&&(!o||id(t,o,n)))})),o.call(T)),i((()=>{o.on(".drag",null),T&&(T.on("start",null),T.on("drag",null),T.on("end",null))}))}})),P}function Rd(){return!0}function Fd({handleId:e,nodeId:t,type:n,isValidConnection:o,edgeUpdaterType:r,onEdgeUpdate:i,onEdgeUpdateEnd:l}){const{vueFlowRef:a,connectionMode:s,connectionRadius:u,connectOnClick:c,connectionClickStartHandle:d,nodesConnectable:f,autoPanOnConnect:p,autoPanSpeed:v,findNode:h,panBy:g,startConnection:m,updateConnection:y,endConnection:b,emits:w,viewport:x,edges:_,nodes:S,isValidConnection:E}=Tf();let k=null,C=!1,O=null,N=null;return{handlePointerDown:function(c){var d;const f="target"===Vt(n),M=gd(c),P=Mc(c.target);if(M&&0===c.button||!M){let n=function(n){F=md(n,L);const{handle:o,validHandleResult:r}=function(e,t,n,o,r,i){const{x:l,y:a}=md(e),s=t.elementsFromPoint(l,a).find((e=>e.classList.contains("vue-flow__handle")));if(s){const e=s.getAttribute("data-nodeid");if(e){const t=_d(void 0,s),o=s.getAttribute("data-handleid"),l=i({nodeId:e,id:o,type:t});if(l){const i=r.find((n=>n.nodeId===e&&n.type===t&&n.id===o));return{handle:{id:o,type:t,nodeId:e,x:(null==i?void 0:i.x)||n.x,y:(null==i?void 0:i.y)||n.y},validHandleResult:l}}}}let u=[],c=Number.POSITIVE_INFINITY;for(const e of r){const t=Math.sqrt((e.x-n.x)**2+(e.y-n.y)**2);if(t<=o){const n=i(e);t<=c&&(t<c?u=[{handle:e,validHandleResult:n}]:t===c&&u.push({handle:e,validHandleResult:n}),c=t)}}if(!u.length)return{handle:null,validHandleResult:{handleDomNode:null,isValid:!1,connection:{source:"",target:"",sourceHandle:null,targetHandle:null},endHandle:null}};if(1===u.length)return u[0];const d=u.some((({validHandleResult:e})=>e.isValid)),f=u.some((({handle:e})=>"target"===e.type));return u.find((({handle:e,validHandleResult:t})=>f?"target"===e.type:!d||t.isValid))||u[0]}(n,P,Lc(F,x.value,!1,[1,1]),u.value,H,(o=>xd(n,o,s.value,Vt(t),Vt(e),f?"target":"source",D,P,_.value,S.value,h)));if(A=o,V||(U(),V=!0),k=r.connection,C=r.isValid,O=r.handleDomNode,!(C&&A&&(null==N?void 0:N.endHandle)&&r.endHandle&&N.endHandle.type===r.endHandle.type&&N.endHandle.nodeId===r.endHandle.nodeId&&N.endHandle.handleId===r.endHandle.handleId)){if(y(A&&C?Bc({x:A.x,y:A.y},x.value):F,r.endHandle,function(e,t){let n=null;return t?n="valid":e&&!t&&(n="invalid"),n}(!!A,C)),N=r,!A&&!C&&!O)return bd(R);k&&k.source!==k.target&&O&&(bd(R),R=O,O.classList.add("connecting","vue-flow__handle-connecting"),O.classList.toggle("valid",C),O.classList.toggle("vue-flow__handle-valid",C))}},M=function(e){(A||O)&&k&&C&&(i?i(e,k):w.connect(k)),w.connectEnd(e),r&&(null==l||l(e)),bd(R),cancelAnimationFrame(I),b(e),V=!1,C=!1,k=null,O=null,P.removeEventListener("mousemove",n),P.removeEventListener("mouseup",M),P.removeEventListener("touchmove",n),P.removeEventListener("touchend",M)};const T=h(Vt(t));let A,D=Vt(o)||E.value||Rd;!D&&T&&(D=(f?T.isValidSourcePos:T.isValidTargetPos)||Rd);let I=0;const{x:$,y:z}=md(c),j=null==P?void 0:P.elementFromPoint($,z),B=_d(Vt(r),j),L=null==(d=a.value)?void 0:d.getBoundingClientRect();if(!L||!B)return;let R,F=md(c,L),V=!1;const H=function({nodes:e,nodeId:t,handleId:n,handleType:o}){const r=[];for(let i=0;i<e.length;i++){const l=e[i],{handleBounds:a}=l;let s=[],u=[];a&&(s=wd(l,a,"source",`${t}-${n}-${o}`),u=wd(l,a,"target",`${t}-${n}-${o}`)),r.push(...s,...u)}return r}({nodes:S.value,nodeId:Vt(t),handleId:Vt(e),handleType:B}),U=()=>{if(!p.value)return;const[e,t]=Zc(F,L,v.value);g({x:e,y:t}),I=requestAnimationFrame(U)};m({nodeId:Vt(t),handleId:Vt(e),type:B,position:(null==j?void 0:j.getAttribute("data-handlepos"))||cc.Top},{x:$-L.left,y:z-L.top}),w.connectStart({event:c,nodeId:Vt(t),handleId:Vt(e),handleType:B}),P.addEventListener("mousemove",n),P.addEventListener("mouseup",M),P.addEventListener("touchmove",n),P.addEventListener("touchend",M)}},handleClick:function(r){if(!c.value)return;const i="target"===Vt(n);if(d.value){let l=Vt(o)||E.value||Rd;const a=h(Vt(t));if(!l&&a&&(l=(i?a.isValidSourcePos:a.isValidTargetPos)||Rd),a&&!1===(void 0===a.connectable?f.value:a.connectable))return;const u=Mc(r.target),{connection:c,isValid:p}=xd(r,{nodeId:Vt(t),id:Vt(e),type:Vt(n)},s.value,d.value.nodeId,d.value.handleId||null,d.value.type,l,u,_.value,S.value,h),v=c.source===c.target;p&&!v&&w.connect(c),w.clickConnectEnd(r),b(r,!0)}else w.clickConnectStart({event:r,nodeId:Vt(t),handleId:Vt(e)}),m({nodeId:Vt(t),type:Vt(n),handleId:Vt(e)},void 0,!0)}}}function Vd(e){const t=e??qo(Id,"")??"",n=qo($d,Bt(null)),{findNode:o,edges:r,emits:i}=Tf(),l=o(t);return l||i.error(new hd(pd.NODE_NOT_FOUND,t)),{id:t,nodeEl:n,node:l,parentNode:yi((()=>o(l.parentNode))),connectedEdges:yi((()=>Yc([l],r.value)))}}function Hd(){const{getSelectedNodes:e,nodeExtent:t,updateNodePositions:n,findNode:o,snapGrid:r,snapToGrid:i,nodesDraggable:l,emits:a}=Tf();return(s,u=!1)=>{const c=i.value?r.value[0]:5,d=i.value?r.value[1]:5,f=u?4:1,p=s.x*c*f,v=s.y*d*f,h=[];for(const n of e.value)if(n.draggable||l&&void 0===n.draggable){const e={x:n.computedPosition.x+p,y:n.computedPosition.y+v},{computedPosition:r}=sd(n,e,a.error,t.value,n.parentNode?o(n.parentNode):void 0);h.push({id:n.id,position:r,from:n.position,distance:{x:s.x,y:s.y},dimensions:n.dimensions})}n(h,!0,!1)}}const Ud=.1;function Yd(){return Ed("Viewport not initialized yet."),Promise.resolve(!1)}const Xd={zoomIn:Yd,zoomOut:Yd,zoomTo:Yd,fitView:Yd,setCenter:Yd,fitBounds:Yd,project:e=>e,screenToFlowCoordinate:e=>e,flowToScreenCoordinate:e=>e,setViewport:Yd,setTransform:Yd,getViewport:()=>({x:0,y:0,zoom:1}),getTransform:()=>({x:0,y:0,zoom:1}),viewportInitialized:!1};function Gd(e,t=0,n){return e.transition().duration(t).on("end",n)}function qd(){return{vueFlowRef:null,viewportRef:null,nodes:[],edges:[],connectionLookup:new Map,nodeTypes:{},edgeTypes:{},initialized:!1,dimensions:{width:0,height:0},viewport:{x:0,y:0,zoom:1},d3Zoom:null,d3Selection:null,d3ZoomHandler:null,minZoom:.5,maxZoom:2,translateExtent:[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],nodeExtent:[[Number.NEGATIVE_INFINITY,Number.NEGATIVE_INFINITY],[Number.POSITIVE_INFINITY,Number.POSITIVE_INFINITY]],selectionMode:dc.Full,paneDragging:!1,preventScrolling:!0,zoomOnScroll:!0,zoomOnPinch:!0,zoomOnDoubleClick:!0,panOnScroll:!1,panOnScrollSpeed:.5,panOnScrollMode:hc.Free,paneClickDistance:0,panOnDrag:!0,edgeUpdaterRadius:10,onlyRenderVisibleElements:!1,defaultViewport:{x:0,y:0,zoom:1},nodesSelectionActive:!1,userSelectionActive:!1,userSelectionRect:null,defaultMarkerColor:"#b1b1b7",connectionLineStyle:{},connectionLineType:null,connectionLineOptions:{type:fc.Bezier,style:{}},connectionMode:pc.Loose,connectionStartHandle:null,connectionEndHandle:null,connectionClickStartHandle:null,connectionPosition:{x:Number.NaN,y:Number.NaN},connectionRadius:20,connectOnClick:!0,connectionStatus:null,isValidConnection:null,snapGrid:[15,15],snapToGrid:!1,edgesUpdatable:!1,edgesFocusable:!0,nodesFocusable:!0,nodesConnectable:!0,nodesDraggable:!0,nodeDragThreshold:1,elementsSelectable:!0,selectNodesOnDrag:!0,multiSelectionActive:!1,selectionKeyCode:"Shift",multiSelectionKeyCode:yd()?"Meta":"Control",zoomActivationKeyCode:yd()?"Meta":"Control",deleteKeyCode:"Backspace",panActivationKeyCode:"Space",hooks:{edgesChange:rd(),nodesChange:rd(),nodeDoubleClick:rd(),nodeClick:rd(),nodeMouseEnter:rd(),nodeMouseMove:rd(),nodeMouseLeave:rd(),nodeContextMenu:rd(),nodeDragStart:rd(),nodeDrag:rd(),nodeDragStop:rd(),nodesInitialized:rd(),miniMapNodeClick:rd(),miniMapNodeDoubleClick:rd(),miniMapNodeMouseEnter:rd(),miniMapNodeMouseMove:rd(),miniMapNodeMouseLeave:rd(),connect:rd(),connectStart:rd(),connectEnd:rd(),clickConnectStart:rd(),clickConnectEnd:rd(),paneReady:rd(),init:rd(),move:rd(),moveStart:rd(),moveEnd:rd(),selectionDragStart:rd(),selectionDrag:rd(),selectionDragStop:rd(),selectionContextMenu:rd(),selectionStart:rd(),selectionEnd:rd(),viewportChangeStart:rd(),viewportChange:rd(),viewportChangeEnd:rd(),paneScroll:rd(),paneClick:rd(),paneContextMenu:rd(),paneMouseEnter:rd(),paneMouseMove:rd(),paneMouseLeave:rd(),edgeContextMenu:rd(),edgeMouseEnter:rd(),edgeMouseMove:rd(),edgeMouseLeave:rd(),edgeDoubleClick:rd(),edgeClick:rd(),edgeUpdateStart:rd(),edgeUpdate:rd(),edgeUpdateEnd:rd(),updateNodeInternals:rd(),error:rd((e=>Ed(e.message)))},applyDefault:!0,autoConnect:!1,fitViewOnInit:!1,fitViewOnInitDone:!1,noDragClassName:"nodrag",noWheelClassName:"nowheel",noPanClassName:"nopan",defaultEdgeOptions:void 0,elevateEdgesOnSelect:!1,elevateNodesOnSelect:!0,autoPanOnNodeDrag:!0,autoPanOnConnect:!0,autoPanSpeed:15,disableKeyboardA11y:!1,ariaLiveMessage:""}}const Wd=["id","vueFlowRef","viewportRef","initialized","modelValue","nodes","edges","maxZoom","minZoom","translateExtent","hooks","defaultEdgeOptions"];function Zd(e,t,n){const o=function(e){function t(t,n){return new Promise((o=>{e.d3Selection&&e.d3Zoom?e.d3Zoom.scaleBy(Gd(e.d3Selection,n,(()=>{o(!0)})),t):o(!1)}))}function n(t,n,o,r){return new Promise((i=>{const{x:l,y:a}=Nc({x:-t,y:-n},e.translateExtent),s=tc.translate(-l,-a).scale(o);e.d3Selection&&e.d3Zoom?e.d3Zoom.transform(Gd(e.d3Selection,r,(()=>{i(!0)})),s):i(!1)}))}return yi((()=>e.d3Zoom&&e.d3Selection&&e.dimensions.width&&e.dimensions.height?{viewportInitialized:!0,zoomIn:e=>t(1.2,null==e?void 0:e.duration),zoomOut:e=>t(1/1.2,null==e?void 0:e.duration),zoomTo:(t,n)=>new Promise((o=>{e.d3Selection&&e.d3Zoom?e.d3Zoom.scaleTo(Gd(e.d3Selection,null==n?void 0:n.duration,(()=>{o(!0)})),t):o(!1)})),setViewport:(e,t)=>n(e.x,e.y,e.zoom,null==t?void 0:t.duration),setTransform:(e,t)=>n(e.x,e.y,e.zoom,null==t?void 0:t.duration),getViewport:()=>({x:e.viewport.x,y:e.viewport.y,zoom:e.viewport.zoom}),getTransform:()=>({x:e.viewport.x,y:e.viewport.y,zoom:e.viewport.zoom}),fitView:(t={padding:Ud,includeHiddenNodes:!1,duration:0})=>{var o,r;const i=[];for(const n of e.nodes)n.dimensions.width&&n.dimensions.height&&((null==t?void 0:t.includeHiddenNodes)||!n.hidden)&&(!(null==(o=t.nodes)?void 0:o.length)||(null==(r=t.nodes)?void 0:r.length)&&t.nodes.includes(n.id))&&i.push(n);if(!i.length)return Promise.resolve(!1);const l=Hc(i),{x:a,y:s,zoom:u}=Xc(l,e.dimensions.width,e.dimensions.height,t.minZoom??e.minZoom,t.maxZoom??e.maxZoom,t.padding??Ud,t.offset);return n(a,s,u,null==t?void 0:t.duration)},setCenter:(t,o,r)=>{const i=void 0!==(null==r?void 0:r.zoom)?r.zoom:e.maxZoom;return n(e.dimensions.width/2-t*i,e.dimensions.height/2-o*i,i,null==r?void 0:r.duration)},fitBounds:(t,o={padding:Ud})=>{const{x:r,y:i,zoom:l}=Xc(t,e.dimensions.width,e.dimensions.height,e.minZoom,e.maxZoom,o.padding);return n(r,i,l,null==o?void 0:o.duration)},project:t=>Lc(t,e.viewport,e.snapToGrid,e.snapGrid),screenToFlowCoordinate:t=>{if(e.vueFlowRef){const{x:n,y:o}=e.vueFlowRef.getBoundingClientRect();return Lc({x:t.x-n,y:t.y-o},e.viewport,e.snapToGrid,e.snapGrid)}return{x:0,y:0}},flowToScreenCoordinate:t=>{if(e.vueFlowRef){const{x:n,y:o}=e.vueFlowRef.getBoundingClientRect();return Bc({x:t.x+n,y:t.y+o},e.viewport)}return{x:0,y:0}}}:Xd))}(e),r=t=>{const n=t??[];e.hooks.updateNodeInternals.trigger(n)},i=t=>Yc(t,e.edges),l=e=>{if(e)return t.value.get(e)},a=e=>{if(e)return n.value.get(e)},s=(o,r)=>{const i=new Set,l=new Set;for(const e of o)Tc(e)?i.add(e.id):Pc(e)&&l.add(e.id);const a=od(t.value,i,!0),s=od(n.value,l);if(e.multiSelectionActive){for(const e of i)a.push(Qc(e,r));for(const e of l)s.push(Qc(e,r))}a.length&&e.hooks.nodesChange.trigger(a),s.length&&e.hooks.edgesChange.trigger(s)},u=t=>{var n;null==(n=e.d3Zoom)||n.scaleExtent([t,e.maxZoom]),e.minZoom=t},c=t=>{var n;null==(n=e.d3Zoom)||n.scaleExtent([e.minZoom,t]),e.maxZoom=t},d=t=>{var n;null==(n=e.d3Zoom)||n.translateExtent(t),e.translateExtent=t},f=t=>{const n=t instanceof Function?t(e.nodes):t;(e.initialized||n.length)&&(e.nodes=Pd(n,l,e.hooks.error.trigger))},p=t=>{const n=t instanceof Function?t(e.edges):t;if(!e.initialized&&!n.length)return;const o=Ad(n,e.isValidConnection,l,a,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges);Td(e.connectionLookup,o),e.edges=o},v=t=>{const n=t instanceof Function?t([...e.nodes,...e.edges]):t;(e.initialized||n.length)&&(f(n.filter(Tc)),p(n.filter(Pc)))},h=e=>{const t=Dc((n=e).width)&&Dc(n.height)&&Dc(n.x)&&Dc(n.y);var n;const o=t?null:Ac(e)?e:l(e.id);return t||o?[t?e:Ec(o),o,t]:[null,null,t]},g=t=>{const n=t instanceof Function?t(e):t,o=["d3Zoom","d3Selection","d3ZoomHandler","viewportRef","vueFlowRef","dimensions","hooks"];Nd(n.defaultEdgeOptions)&&(e.defaultEdgeOptions=n.defaultEdgeOptions);const r=n.modelValue||n.nodes||n.edges?[]:void 0;r&&(n.modelValue&&r.push(...n.modelValue),n.nodes&&r.push(...n.nodes),n.edges&&r.push(...n.edges),v(r));for(const t of Object.keys(n)){const r=t,i=n[r];![...Wd,...o].includes(r)&&Nd(i)&&(e[r]=i)}_l((()=>e.d3Zoom)).not.toBeNull().then((()=>{Nd(n.maxZoom)&&c(n.maxZoom),Nd(n.minZoom)&&u(n.minZoom),Nd(n.translateExtent)&&d(n.translateExtent)})),e.initialized||(e.initialized=!0)};return{updateNodePositions:(t,n,o)=>{var r,i;const a=[];for(const e of t){const t={id:e.id,type:"position",dragging:o,from:e.from};if(n&&(t.position=e.position,e.parentNode)){const n=l(e.parentNode);t.position={x:t.position.x-((null==(r=null==n?void 0:n.computedPosition)?void 0:r.x)??0),y:t.position.y-((null==(i=null==n?void 0:n.computedPosition)?void 0:i.y)??0)}}a.push(t)}(null==a?void 0:a.length)&&e.hooks.nodesChange.trigger(a)},updateNodeDimensions:t=>{if(!e.vueFlowRef)return;const n=e.vueFlowRef.querySelector(".vue-flow__transformationpane");if(!n)return;const r=window.getComputedStyle(n),{m22:i}=new window.DOMMatrixReadOnly(r.transform),a=[];for(let e=0;e<t.length;++e){const n=t[e],o=l(n.id);if(o){const e=Cc(n.nodeElement);if(e.width&&e.height&&(o.dimensions.width!==e.width||o.dimensions.height!==e.height||n.forceUpdate)){const t=n.nodeElement.getBoundingClientRect();o.dimensions=e,o.handleBounds.source=Cd(".source",n.nodeElement,t,i),o.handleBounds.target=Cd(".target",n.nodeElement,t,i),a.push({id:o.id,type:"dimensions",dimensions:e})}}}!e.fitViewOnInitDone&&e.fitViewOnInit&&o.value.fitView().then((()=>{e.fitViewOnInitDone=!0})),a.length&&e.hooks.nodesChange.trigger(a)},setElements:v,setNodes:f,setEdges:p,addNodes:t=>{let n=t instanceof Function?t(e.nodes):t;n=Array.isArray(n)?n:[n];const o=Pd(n,l,e.hooks.error.trigger),r=[];for(const e of o)r.push(ed(e));r.length&&e.hooks.nodesChange.trigger(r)},addEdges:t=>{let n=t instanceof Function?t(e.edges):t;n=Array.isArray(n)?n:[n];const o=Ad(n,e.isValidConnection,l,a,e.hooks.error.trigger,e.defaultEdgeOptions,e.nodes,e.edges),r=[];for(const e of o)r.push(ed(e));r.length&&e.hooks.edgesChange.trigger(r)},removeNodes:(t,n=!0,o=!1)=>{const r=t instanceof Function?t(e.nodes):t,a=Array.isArray(r)?r:[r],s=[],u=[];function c(e){const t=i(e);for(const e of t)Nd(e.deletable)&&!e.deletable||u.push(nd(e.id,e.source,e.target,e.sourceHandle,e.targetHandle))}function d(t){const o=[];for(const n of e.nodes)n.parentNode===t&&o.push(n);if(o.length){for(const e of o)s.push(td(e.id));n&&c(o);for(const e of o)d(e.id)}}for(const e of a){const t="string"==typeof e?l(e):e;t&&(Nd(t.deletable)&&!t.deletable||(s.push(td(t.id)),n&&c([t]),o&&d(t.id)))}u.length&&e.hooks.edgesChange.trigger(u),s.length&&e.hooks.nodesChange.trigger(s)},removeEdges:t=>{const n=t instanceof Function?t(e.edges):t,o=Array.isArray(n)?n:[n],r=[];for(const e of o){const t="string"==typeof e?a(e):e;t&&(Nd(t.deletable)&&!t.deletable||r.push(nd("string"==typeof e?e:e.id,t.source,t.target,t.sourceHandle,t.targetHandle)))}e.hooks.edgesChange.trigger(r)},findNode:l,findEdge:a,updateEdge:(t,n,o=!0)=>function(e,t,n,o,r,i){if(!t.source||!t.target)return i(new hd(pd.EDGE_INVALID,e.id)),!1;const l=o(e.id);if(!l)return i(new hd(pd.EDGE_NOT_FOUND,e.id)),!1;const{id:a,...s}=e,u={...s,id:r?jc(t):a,source:t.source,target:t.target,sourceHandle:t.sourceHandle,targetHandle:t.targetHandle};return n.splice(n.indexOf(l),1,u),u}(t,n,e.edges,a,o,e.hooks.error.trigger),updateEdgeData:(e,t,n={replace:!1})=>{const o=a(e);if(!o)return;const r="function"==typeof t?t(o):t;o.data=n.replace?r:{...o.data,...r}},updateNode:(t,n,o={replace:!1})=>{const r=l(t);if(!r)return;const i="function"==typeof n?n(r):n;o.replace?e.nodes.splice(e.nodes.indexOf(r),1,i):Object.assign(r,i)},updateNodeData:(e,t,n={replace:!1})=>{const o=l(e);if(!o)return;const r="function"==typeof t?t(o):t;o.data=n.replace?r:{...o.data,...r}},applyEdgeChanges:t=>{const n=Jc(t,e.edges);return Td(e.connectionLookup,n),n},applyNodeChanges:t=>Jc(t,e.nodes),addSelectedElements:e=>{s(e,!0)},addSelectedNodes:o=>{if(e.multiSelectionActive){const t=o.map((e=>Qc(e.id,!0)));e.hooks.nodesChange.trigger(t)}else e.hooks.nodesChange.trigger(od(t.value,new Set(o.map((e=>e.id))),!0)),e.hooks.edgesChange.trigger(od(n.value))},addSelectedEdges:o=>{if(e.multiSelectionActive){const t=o.map((e=>Qc(e.id,!0)));e.hooks.edgesChange.trigger(t)}else e.hooks.edgesChange.trigger(od(n.value,new Set(o.map((e=>e.id))))),e.hooks.nodesChange.trigger(od(t.value,new Set,!0))},setMinZoom:u,setMaxZoom:c,setTranslateExtent:d,setNodeExtent:t=>{e.nodeExtent=t,r()},setPaneClickDistance:t=>{var n;null==(n=e.d3Zoom)||n.clickDistance(t)},removeSelectedElements:t=>{if(!t||!t.length)return s([],!1);const n=t.reduce(((e,t)=>{const n=Qc(t.id,!1);return Tc(t)?e.nodes.push(n):e.edges.push(n),e}),{nodes:[],edges:[]});n.nodes.length&&e.hooks.nodesChange.trigger(n.nodes),n.edges.length&&e.hooks.edgesChange.trigger(n.edges)},removeSelectedNodes:t=>{const n=(t||e.nodes).map((e=>(e.selected=!1,Qc(e.id,!1))));e.hooks.nodesChange.trigger(n)},removeSelectedEdges:t=>{const n=(t||e.edges).map((e=>(e.selected=!1,Qc(e.id,!1))));e.hooks.edgesChange.trigger(n)},startConnection:(t,n,o=!1)=>{o?e.connectionClickStartHandle=t:e.connectionStartHandle=t,e.connectionEndHandle=null,e.connectionStatus=null,n&&(e.connectionPosition=n)},updateConnection:(t,n=null,o=null)=>{e.connectionStartHandle&&(e.connectionPosition=t,e.connectionEndHandle=n,e.connectionStatus=o)},endConnection:(t,n)=>{e.connectionPosition={x:Number.NaN,y:Number.NaN},e.connectionEndHandle=null,e.connectionStatus=null,n?e.connectionClickStartHandle=null:e.connectionStartHandle=null},setInteractive:t=>{e.nodesDraggable=t,e.nodesConnectable=t,e.elementsSelectable=t},setState:g,getIntersectingNodes:(t,n=!0,o=e.nodes)=>{const[r,i,l]=h(t);if(!r)return[];const a=[];for(const t of o||e.nodes){if(!(l||t.id!==i.id&&t.computedPosition))continue;const e=kc(Ec(t),r);(n&&e>0||e>=Number(r.width)*Number(r.height))&&a.push(t)}return a},getIncomers:t=>function(...e){if(3===e.length){const[t,n,o]=e;return zc(t,n,o,"source")}const[t,n]=e,o="string"==typeof t?t:t.id;return n.filter((e=>Pc(e)&&e.target===o)).map((e=>n.find((t=>Tc(t)&&t.id===e.source))))}(t,e.nodes,e.edges),getOutgoers:t=>function(...e){if(3===e.length){const[t,n,o]=e;return zc(t,n,o,"target")}const[t,n]=e,o="string"==typeof t?t:t.id;return n.filter((e=>Pc(e)&&e.source===o)).map((e=>n.find((t=>Tc(t)&&t.id===e.target))))}(t,e.nodes,e.edges),getConnectedEdges:i,getHandleConnections:({id:t,type:n,nodeId:o})=>{var r;return Array.from((null==(r=e.connectionLookup.get(`${o}-${n}-${t??null}`))?void 0:r.values())??[])},isNodeIntersecting:(e,t,n=!0)=>{const[o]=h(e);if(!o)return!1;const r=kc(o,t);return n&&r>0||r>=Number(o.width)*Number(o.height)},panBy:t=>{const{viewport:n,dimensions:o,d3Zoom:r,d3Selection:i,translateExtent:l}=e;if(!r||!i||!t.x&&!t.y)return!1;const a=tc.translate(n.x+t.x,n.y+t.y).scale(n.zoom),s=[[0,0],[o.width,o.height]],u=r.constrain()(a,s,l),c=e.viewport.x!==u.x||e.viewport.y!==u.y||e.viewport.zoom!==u.k;return r.transform(i,u),c},fitView:e=>o.value.fitView(e),zoomIn:e=>o.value.zoomIn(e),zoomOut:e=>o.value.zoomOut(e),zoomTo:(e,t)=>o.value.zoomTo(e,t),setViewport:(e,t)=>o.value.setViewport(e,t),setTransform:(e,t)=>o.value.setTransform(e,t),getViewport:()=>o.value.getViewport(),getTransform:()=>o.value.getTransform(),setCenter:(e,t,n)=>o.value.setCenter(e,t,n),fitBounds:(e,t)=>o.value.fitBounds(e,t),project:e=>o.value.project(e),screenToFlowCoordinate:e=>o.value.screenToFlowCoordinate(e),flowToScreenCoordinate:e=>o.value.flowToScreenCoordinate(e),toObject:()=>{const t=[],n=[];for(const n of e.nodes){const{computedPosition:e,handleBounds:o,selected:r,dimensions:i,isParent:l,resizing:a,dragging:s,events:u,...c}=n;t.push(c)}for(const t of e.edges){const{selected:e,sourceNode:o,targetNode:r,events:i,...l}=t;n.push(l)}return JSON.parse(JSON.stringify({nodes:t,edges:n,position:[e.viewport.x,e.viewport.y],zoom:e.viewport.zoom,viewport:e.viewport}))},fromObject:t=>new Promise((n=>{const{nodes:r,edges:i,position:l,zoom:a,viewport:s}=t;if(r&&f(r),i&&p(i),(null==s?void 0:s.x)&&(null==s?void 0:s.y)||l){const t=(null==s?void 0:s.x)||l[0],r=(null==s?void 0:s.y)||l[1],i=(null==s?void 0:s.zoom)||a||e.viewport.zoom;return _l((()=>o.value.viewportInitialized)).toBe(!0).then((()=>{o.value.setViewport({x:t,y:r,zoom:i}).then((()=>{n(!0)}))}))}n(!0)})),updateNodeInternals:r,viewportHelper:o,$reset:()=>{const t=qd();if(e.edges=[],e.nodes=[],e.d3Zoom&&e.d3Selection){const n=tc.translate(t.defaultViewport.x??0,t.defaultViewport.y??0).scale(Oc(t.defaultViewport.zoom??1,t.minZoom,t.maxZoom)),o=e.viewportRef.getBoundingClientRect(),r=[[0,0],[o.width,o.height]],i=e.d3Zoom.constrain()(n,r,t.translateExtent);e.d3Zoom.transform(e.d3Selection,i)}g(t)},$destroy:()=>{}}}const Kd=["data-id","data-handleid","data-nodeid","data-handlepos"],Jd=Wn({name:"Handle",compatConfig:{MODE:3},props:{id:{default:null},type:{},position:{default:()=>cc.Top},isValidConnection:{type:Function},connectable:{type:[Boolean,Number,String,Function],default:void 0},connectableStart:{type:Boolean,default:!0},connectableEnd:{type:Boolean,default:!0}},setup(e,{expose:t}){const n=Do(e,["position","connectable","connectableStart","connectableEnd","id"]),o=Wt((()=>n.type??"source")),r=Wt((()=>n.isValidConnection??null)),{connectionStartHandle:i,connectionClickStartHandle:l,connectionEndHandle:a,vueFlowRef:s,nodesConnectable:u,noDragClassName:c,noPanClassName:d}=Tf(),{id:f,node:p,nodeEl:v,connectedEdges:h}=Vd(),g=Bt(),m=Wt((()=>void 0===e.connectableStart||e.connectableStart)),y=Wt((()=>void 0===e.connectableEnd||e.connectableEnd)),b=Wt((()=>{var t,n,r,l,s,u;return(null==(t=i.value)?void 0:t.nodeId)===f&&(null==(n=i.value)?void 0:n.handleId)===e.id&&(null==(r=i.value)?void 0:r.type)===o.value||(null==(l=a.value)?void 0:l.nodeId)===f&&(null==(s=a.value)?void 0:s.handleId)===e.id&&(null==(u=a.value)?void 0:u.type)===o.value})),w=Wt((()=>{var t,n,r;return(null==(t=l.value)?void 0:t.nodeId)===f&&(null==(n=l.value)?void 0:n.handleId)===e.id&&(null==(r=l.value)?void 0:r.type)===o.value})),{handlePointerDown:x,handleClick:_}=Fd({nodeId:f,handleId:e.id,isValidConnection:r,type:o}),S=yi((()=>"string"==typeof e.connectable&&"single"===e.connectable?!h.value.some((t=>{const n=t[`${o.value}Handle`];return t[o.value]===f&&(!n||n===e.id)})):"number"==typeof e.connectable?h.value.filter((t=>{const n=t[`${o.value}Handle`];return t[o.value]===f&&(!n||n===e.id)})).length<e.connectable:"function"==typeof e.connectable?e.connectable(p,h.value):Nd(e.connectable)?e.connectable:u.value));function E(e){const t=gd(e);S.value&&m.value&&(t&&0===e.button||!t)&&x(e)}function k(e){f&&(l.value||m.value)&&S.value&&_(e)}return lo((()=>{var t;if(!p.dimensions.width||!p.dimensions.height)return;const n=null==(t=p.handleBounds[o.value])?void 0:t.find((t=>t.id===e.id));if(!s.value||n)return;const r=s.value.querySelector(".vue-flow__transformationpane");if(!(v.value&&g.value&&r&&e.id))return;const i=v.value.getBoundingClientRect(),l=g.value.getBoundingClientRect(),a=window.getComputedStyle(r),{m22:u}=new window.DOMMatrixReadOnly(a.transform),c={id:e.id,position:e.position,x:(l.left-i.left)/u,y:(l.top-i.top)/u,...Cc(g.value)};p.handleBounds[o.value]=[...p.handleBounds[o.value]??[],c]})),co((()=>{const t=p.handleBounds[o.value];t&&(p.handleBounds[o.value]=t.filter((t=>t.id!==e.id)))})),t({handleClick:_,handlePointerDown:x,onClick:k,onPointerDown:E}),(t,n)=>(jr(),Fr("div",{ref_key:"handle",ref:g,"data-id":`${Ft(f)}-${e.id}-${o.value}`,"data-handleid":e.id,"data-nodeid":Ft(f),"data-handlepos":t.position,class:re(["vue-flow__handle",[`vue-flow__handle-${t.position}`,`vue-flow__handle-${e.id}`,Ft(c),Ft(d),o.value,{connectable:S.value,connecting:w.value,connectablestart:m.value,connectableend:y.value,connectionindicator:S.value&&(m.value&&!b.value||y.value&&b.value)}]]),onMousedown:E,onTouchstartPassive:E,onClick:k},[So(t.$slots,"default",{id:t.id})],42,Kd))}}),Qd=function({sourcePosition:e=cc.Bottom,targetPosition:t=cc.Top,label:n,connectable:o=!0,isValidTargetPos:r,isValidSourcePos:i,data:l}){const a=l.label||n;return[bi(Jd,{type:"target",position:t,connectable:o,isValidConnection:r}),"string"!=typeof a&&a?bi(a):bi("div",{innerHTML:a}),bi(Jd,{type:"source",position:e,connectable:o,isValidConnection:i})]};Qd.props=["sourcePosition","targetPosition","label","isValidTargetPos","isValidSourcePos","connectable","data"],Qd.inheritAttrs=!1,Qd.compatConfig={MODE:3};const ef=Qd,tf=function({targetPosition:e=cc.Top,label:t,connectable:n=!0,isValidTargetPos:o,data:r}){const i=r.label||t;return[bi(Jd,{type:"target",position:e,connectable:n,isValidConnection:o}),"string"!=typeof i&&i?bi(i):bi("div",{innerHTML:i})]};tf.props=["targetPosition","label","isValidTargetPos","connectable","data"],tf.inheritAttrs=!1,tf.compatConfig={MODE:3};const nf=tf,of=function({sourcePosition:e=cc.Bottom,label:t,connectable:n=!0,isValidSourcePos:o,data:r}){const i=r.label||t;return["string"!=typeof i&&i?bi(i):bi("div",{innerHTML:i}),bi(Jd,{type:"source",position:e,connectable:n,isValidConnection:o})]};of.props=["sourcePosition","label","isValidSourcePos","connectable","data"],of.inheritAttrs=!1,of.compatConfig={MODE:3};const rf=of,lf=["transform"],af=["width","height","x","y","rx","ry"],sf=["y"],uf=Wn({name:"EdgeText",compatConfig:{MODE:3},props:{x:{},y:{},label:{},labelStyle:{default:()=>({})},labelShowBg:{type:Boolean,default:!0},labelBgStyle:{default:()=>({})},labelBgPadding:{default:()=>[2,4]},labelBgBorderRadius:{default:2}},setup(e){const t=Bt({x:0,y:0,width:0,height:0}),n=Bt(null),o=yi((()=>`translate(${e.x-t.value.width/2} ${e.y-t.value.height/2})`));function r(){if(!n.value)return;const e=n.value.getBBox();e.width===t.value.width&&e.height===t.value.height||(t.value=e)}return lo(r),br([()=>e.x,()=>e.y,n,()=>e.label],r),(e,r)=>(jr(),Fr("g",{transform:o.value,class:"vue-flow__edge-textwrapper"},[e.labelShowBg?(jr(),Fr("rect",{key:0,class:"vue-flow__edge-textbg",width:`${t.value.width+2*e.labelBgPadding[0]}px`,height:`${t.value.height+2*e.labelBgPadding[1]}px`,x:-e.labelBgPadding[0],y:-e.labelBgPadding[1],style:ee(e.labelBgStyle),rx:e.labelBgBorderRadius,ry:e.labelBgBorderRadius},null,12,af)):Kr("",!0),Gr("text",ti(e.$attrs,{ref_key:"el",ref:n,class:"vue-flow__edge-text",y:t.value.height/2,dy:"0.3em",style:e.labelStyle}),[So(e.$slots,"default",{},(()=>["string"!=typeof e.label?(jr(),Vr(bo(e.label),{key:0})):(jr(),Fr(Tr,{key:1},[Zr(ue(e.label),1)],64))]))],16,sf)],8,lf))}}),cf=["id","d","marker-end","marker-start"],df=["d","stroke-width"],ff=Wn({name:"BaseEdge",inheritAttrs:!1,compatConfig:{MODE:3},props:{id:{},labelX:{},labelY:{},path:{},label:{},markerStart:{},markerEnd:{},interactionWidth:{default:20},style:{},labelStyle:{},labelShowBg:{type:Boolean,default:!0},labelBgStyle:{},labelBgPadding:{},labelBgBorderRadius:{}},setup(e,{expose:t}){const n=Do(e,["interactionWidth","labelShowBg"]),o=Bt(null),r=Bt(null),i=Bt(null),l=Po();return t({pathEl:o,interactionEl:r,labelEl:i}),(e,t)=>(jr(),Fr(Tr,null,[Gr("path",{id:e.id,ref_key:"pathEl",ref:o,d:e.path,style:ee(n.style),class:re(["vue-flow__edge-path",Ft(l).class]),"marker-end":e.markerEnd,"marker-start":e.markerStart},null,14,cf),e.interactionWidth?(jr(),Fr("path",{key:0,ref_key:"interactionEl",ref:r,fill:"none",d:e.path,"stroke-width":e.interactionWidth,"stroke-opacity":0,class:"vue-flow__edge-interaction"},null,8,df)):Kr("",!0),e.label&&e.labelX&&e.labelY?(jr(),Vr(uf,{key:1,ref_key:"labelEl",ref:i,x:e.labelX,y:e.labelY,label:e.label,"label-show-bg":e.labelShowBg,"label-bg-style":e.labelBgStyle,"label-bg-padding":e.labelBgPadding,"label-bg-border-radius":e.labelBgBorderRadius,"label-style":e.labelStyle},null,8,["x","y","label","label-show-bg","label-bg-style","label-bg-padding","label-bg-border-radius","label-style"])):Kr("",!0)],64))}});function pf({sourceX:e,sourceY:t,targetX:n,targetY:o}){const r=Math.abs(n-e)/2,i=n<e?n+r:n-r,l=Math.abs(o-t)/2;return[i,o<t?o+l:o-l,r,l]}function vf({sourceX:e,sourceY:t,targetX:n,targetY:o,sourceControlX:r,sourceControlY:i,targetControlX:l,targetControlY:a}){const s=.125*e+.375*r+.375*l+.125*n,u=.125*t+.375*i+.375*a+.125*o;return[s,u,Math.abs(s-e),Math.abs(u-t)]}function hf(e,t){return e>=0?.5*e:25*t*Math.sqrt(-e)}function gf({pos:e,x1:t,y1:n,x2:o,y2:r,c:i}){let l,a;switch(e){case cc.Left:l=t-hf(t-o,i),a=n;break;case cc.Right:l=t+hf(o-t,i),a=n;break;case cc.Top:l=t,a=n-hf(n-r,i);break;case cc.Bottom:l=t,a=n+hf(r-n,i)}return[l,a]}function mf(e){const{sourceX:t,sourceY:n,sourcePosition:o=cc.Bottom,targetX:r,targetY:i,targetPosition:l=cc.Top,curvature:a=.25}=e,[s,u]=gf({pos:o,x1:t,y1:n,x2:r,y2:i,c:a}),[c,d]=gf({pos:l,x1:r,y1:i,x2:t,y2:n,c:a}),[f,p,v,h]=vf({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:s,sourceControlY:u,targetControlX:c,targetControlY:d});return[`M${t},${n} C${s},${u} ${c},${d} ${r},${i}`,f,p,v,h]}function yf({pos:e,x1:t,y1:n,x2:o,y2:r}){let i,l;switch(e){case cc.Left:case cc.Right:i=.5*(t+o),l=n;break;case cc.Top:case cc.Bottom:i=t,l=.5*(n+r)}return[i,l]}function bf(e){const{sourceX:t,sourceY:n,sourcePosition:o=cc.Bottom,targetX:r,targetY:i,targetPosition:l=cc.Top}=e,[a,s]=yf({pos:o,x1:t,y1:n,x2:r,y2:i}),[u,c]=yf({pos:l,x1:r,y1:i,x2:t,y2:n}),[d,f,p,v]=vf({sourceX:t,sourceY:n,targetX:r,targetY:i,sourceControlX:a,sourceControlY:s,targetControlX:u,targetControlY:c});return[`M${t},${n} C${a},${s} ${u},${c} ${r},${i}`,d,f,p,v]}const wf={[cc.Left]:{x:-1,y:0},[cc.Right]:{x:1,y:0},[cc.Top]:{x:0,y:-1},[cc.Bottom]:{x:0,y:1}};function xf(e,t){return Math.sqrt((t.x-e.x)**2+(t.y-e.y)**2)}function _f(e){const{sourceX:t,sourceY:n,sourcePosition:o=cc.Bottom,targetX:r,targetY:i,targetPosition:l=cc.Top,borderRadius:a=5,centerX:s,centerY:u,offset:c=20}=e,[d,f,p,v,h]=function({source:e,sourcePosition:t=cc.Bottom,target:n,targetPosition:o=cc.Top,center:r,offset:i}){const l=wf[t],a=wf[o],s={x:e.x+l.x*i,y:e.y+l.y*i},u={x:n.x+a.x*i,y:n.y+a.y*i},c=function({source:e,sourcePosition:t=cc.Bottom,target:n}){return t===cc.Left||t===cc.Right?e.x<n.x?{x:1,y:0}:{x:-1,y:0}:e.y<n.y?{x:0,y:1}:{x:0,y:-1}}({source:s,sourcePosition:t,target:u}),d=0!==c.x?"x":"y",f=c[d];let p,v,h;const g={x:0,y:0},m={x:0,y:0},[y,b,w,x]=pf({sourceX:e.x,sourceY:e.y,targetX:n.x,targetY:n.y});if(l[d]*a[d]==-1){v=r.x??y,h=r.y??b;const e=[{x:v,y:s.y},{x:v,y:u.y}],t=[{x:s.x,y:h},{x:u.x,y:h}];p=l[d]===f?"x"===d?e:t:"x"===d?t:e}else{const r=[{x:s.x,y:u.y}],c=[{x:u.x,y:s.y}];if(p="x"===d?l.x===f?c:r:l.y===f?r:c,t===o){const t=Math.abs(e[d]-n[d]);if(t<=i){const o=Math.min(i-1,i-t);l[d]===f?g[d]=(s[d]>e[d]?-1:1)*o:m[d]=(u[d]>n[d]?-1:1)*o}}if(t!==o){const e="x"===d?"y":"x",t=l[d]===a[e],n=s[e]>u[e],o=s[e]<u[e];(1===l[d]&&(!t&&n||t&&o)||1!==l[d]&&(!t&&o||t&&n))&&(p="x"===d?r:c)}const y={x:s.x+g.x,y:s.y+g.y},b={x:u.x+m.x,y:u.y+m.y};Math.max(Math.abs(y.x-p[0].x),Math.abs(b.x-p[0].x))>=Math.max(Math.abs(y.y-p[0].y),Math.abs(b.y-p[0].y))?(v=(y.x+b.x)/2,h=p[0].y):(v=p[0].x,h=(y.y+b.y)/2)}return[[e,{x:s.x+g.x,y:s.y+g.y},...p,{x:u.x+m.x,y:u.y+m.y},n],v,h,w,x]}({source:{x:t,y:n},sourcePosition:o,target:{x:r,y:i},targetPosition:l,center:{x:s,y:u},offset:c}),g=d.reduce(((e,t,n)=>{let o;return o=n>0&&n<d.length-1?function(e,t,n,o){const r=Math.min(xf(e,t)/2,xf(t,n)/2,o),{x:i,y:l}=t;if(e.x===i&&i===n.x||e.y===l&&l===n.y)return`L${i} ${l}`;if(e.y===l)return`L ${i+r*(e.x<n.x?-1:1)},${l}Q ${i},${l} ${i},${l+r*(e.y<n.y?1:-1)}`;const a=e.x<n.x?1:-1;return`L ${i},${l+r*(e.y<n.y?-1:1)}Q ${i},${l} ${i+r*a},${l}`}(d[n-1],t,d[n+1],a):`${0===n?"M":"L"}${t.x} ${t.y}`,e+o}),"");return[g,f,p,v,h]}const Sf=Wn({name:"StraightEdge",props:["label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup:(e,{attrs:t})=>()=>{const[n,o,r]=function(e){const{sourceX:t,sourceY:n,targetX:o,targetY:r}=e,[i,l,a,s]=pf({sourceX:t,sourceY:n,targetX:o,targetY:r});return[`M ${t},${n}L ${o},${r}`,i,l,a,s]}(e);return bi(ff,{path:n,labelX:o,labelY:r,...t,...e})}}),Ef=Wn({name:"SmoothStepEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","borderRadius","markerEnd","markerStart","interactionWidth","offset"],compatConfig:{MODE:3},setup:(e,{attrs:t})=>()=>{const[n,o,r]=_f({...e,sourcePosition:e.sourcePosition??cc.Bottom,targetPosition:e.targetPosition??cc.Top});return bi(ff,{path:n,labelX:o,labelY:r,...t,...e})}}),kf=Wn({name:"StepEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],setup:(e,{attrs:t})=>()=>bi(Ef,{...e,...t,borderRadius:0})}),Cf=Wn({name:"BezierEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","curvature","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup:(e,{attrs:t})=>()=>{const[n,o,r]=mf({...e,sourcePosition:e.sourcePosition??cc.Bottom,targetPosition:e.targetPosition??cc.Top});return bi(ff,{path:n,labelX:o,labelY:r,...t,...e})}}),Of=Wn({name:"SimpleBezierEdge",props:["sourcePosition","targetPosition","label","labelStyle","labelShowBg","labelBgStyle","labelBgPadding","labelBgBorderRadius","sourceY","sourceX","targetX","targetY","markerEnd","markerStart","interactionWidth"],compatConfig:{MODE:3},setup:(e,{attrs:t})=>()=>{const[n,o,r]=bf({...e,sourcePosition:e.sourcePosition??cc.Bottom,targetPosition:e.targetPosition??cc.Top});return bi(ff,{path:n,labelX:o,labelY:r,...t,...e})}}),Nf={input:rf,default:ef,output:nf},Mf={default:Cf,straight:Sf,step:kf,smoothstep:Ef,simplebezier:Of};class Pf{constructor(){this.currentId=0,this.flows=new Map}static getInstance(){var e;const t=null==(e=ai())?void 0:e.appContext.app,n=(null==t?void 0:t.config.globalProperties.$vueFlowStorage)??Pf.instance;return Pf.instance=n??new Pf,t&&(t.config.globalProperties.$vueFlowStorage=Pf.instance),Pf.instance}set(e,t){return this.flows.set(e,t)}get(e){return this.flows.get(e)}remove(e){return this.flows.delete(e)}create(e,t){const n=Ct(qd()),o={};for(const[e,t]of Object.entries(n.hooks))o[`on${e.charAt(0).toUpperCase()+e.slice(1)}`]=t.on;const r={};for(const[e,t]of Object.entries(n.hooks))r[e]=t.trigger;const i=yi((()=>{const e=new Map;for(const t of n.nodes)e.set(t.id,t);return e})),l=yi((()=>{const e=new Map;for(const t of n.edges)e.set(t.id,t);return e})),a=function(e,t,n){const o=yi((()=>e=>t.value.get(e))),r=yi((()=>e=>n.value.get(e))),i=yi((()=>{const t={...Mf,...e.edgeTypes},n=Object.keys(t);for(const o of e.edges)o.type&&!n.includes(o.type)&&(t[o.type]=o.type);return t})),l=yi((()=>{const t={...Nf,...e.nodeTypes},n=Object.keys(t);for(const o of e.nodes)o.type&&!n.includes(o.type)&&(t[o.type]=o.type);return t})),a=yi((()=>e.onlyRenderVisibleElements?Uc(e.nodes,{x:0,y:0,width:e.dimensions.width,height:e.dimensions.height},e.viewport,!0):e.nodes)),s=yi((()=>{if(e.onlyRenderVisibleElements){const n=[];for(const o of e.edges){const r=t.value.get(o.source),i=t.value.get(o.target);dd({sourcePos:r.computedPosition||{x:0,y:0},targetPos:i.computedPosition||{x:0,y:0},sourceWidth:r.dimensions.width,sourceHeight:r.dimensions.height,targetWidth:i.dimensions.width,targetHeight:i.dimensions.height,width:e.dimensions.width,height:e.dimensions.height,viewport:e.viewport})&&n.push(o)}return n}return e.edges})),u=yi((()=>[...a.value,...s.value])),c=yi((()=>{const t=[];for(const n of e.nodes)n.selected&&t.push(n);return t})),d=yi((()=>{const t=[];for(const n of e.edges)n.selected&&t.push(n);return t})),f=yi((()=>[...c.value,...d.value])),p=yi((()=>{const t=[];for(const n of e.nodes)n.dimensions.width&&n.dimensions.height&&void 0!==n.handleBounds&&t.push(n);return t})),v=yi((()=>a.value.length>0&&p.value.length===a.value.length));return{getNode:o,getEdge:r,getElements:u,getEdgeTypes:i,getNodeTypes:l,getEdges:s,getNodes:a,getSelectedElements:f,getSelectedNodes:c,getSelectedEdges:d,getNodesInitialized:p,areNodesInitialized:v}}(n,i,l),s=Zd(n,i,l);s.setState({...n,...t});const u={...o,...a,...s,...wl(n),nodeLookup:i,edgeLookup:l,emits:r,id:e,vueFlowVersion:"1.41.0",$destroy:()=>{this.remove(e)}};return this.set(e,u),u}getId(){return"vue-flow-"+this.currentId++}}function Tf(e){const t=Pf.getInstance(),n=ve(),o="object"==typeof e,r=o?e:{id:e},i=r.id,l=i??(null==n?void 0:n.vueFlowId);let a;if(n){const e=qo(Dd,null);null==e||l&&e.id!==l||(a=e)}if(a||l&&(a=t.get(l)),!a||l&&a.id!==l){const e=i??t.getId(),o=t.create(e,r);a=o,(n??pe(!0)).run((()=>{br(o.applyDefault,((e,t,n)=>{const r=e=>{o.applyNodeChanges(e)},i=e=>{o.applyEdgeChanges(e)};e?(o.onNodesChange(r),o.onEdgesChange(i)):(o.hooks.value.nodesChange.off(r),o.hooks.value.edgesChange.off(i)),n((()=>{o.hooks.value.nodesChange.off(r),o.hooks.value.edgesChange.off(i)}))}),{immediate:!0}),cl((()=>{if(a){const e=t.get(a.id);e?e.$destroy():Ed(`No store instance found for id ${a.id} in storage.`)}}))}))}else o&&a.setState(r);if(n&&(Go(Dd,a),n.vueFlowId=a.id),o){const e=ai();"VueFlow"!==(null==e?void 0:e.type.name)&&a.emits.error(new hd(pd.USEVUEFLOW_OPTIONS))}return a}const Af=Wn({name:"UserSelection",compatConfig:{MODE:3},props:{userSelectionRect:{}},setup:e=>(e,t)=>(jr(),Fr("div",{class:"vue-flow__selection vue-flow__container",style:ee({width:`${e.userSelectionRect.width}px`,height:`${e.userSelectionRect.height}px`,transform:`translate(${e.userSelectionRect.x}px, ${e.userSelectionRect.y}px)`})},null,4))}),Df=["tabIndex"],If=Wn({name:"NodesSelection",compatConfig:{MODE:3},setup(e){const{emits:t,viewport:n,getSelectedNodes:o,noPanClassName:r,disableKeyboardA11y:i,userSelectionActive:l}=Tf(),a=Hd(),s=Bt(null),u=Ld({el:s,onStart(e){t.selectionDragStart(e)},onDrag(e){t.selectionDrag(e)},onStop(e){t.selectionDragStop(e)}});lo((()=>{var e;i.value||null==(e=s.value)||e.focus({preventScroll:!0})}));const c=yi((()=>Hc(o.value))),d=yi((()=>({width:`${c.value.width}px`,height:`${c.value.height}px`,top:`${c.value.y}px`,left:`${c.value.x}px`})));function f(e){t.selectionContextMenu({event:e,nodes:o.value})}function p(e){i||Sc[e.key]&&a({x:Sc[e.key].x,y:Sc[e.key].y},e.shiftKey)}return(e,t)=>!Ft(l)&&c.value.width&&c.value.height?(jr(),Fr("div",{key:0,class:re(["vue-flow__nodesselection vue-flow__container",Ft(r)]),style:ee({transform:`translate(${Ft(n).x}px,${Ft(n).y}px) scale(${Ft(n).zoom})`})},[Gr("div",{ref_key:"el",ref:s,class:re([{dragging:Ft(u)},"vue-flow__nodesselection-rect"]),style:ee(d.value),tabIndex:Ft(i)?void 0:-1,onContextmenu:f,onKeydown:p},null,46,Df)],6)):Kr("",!0)}}),$f=Wn({name:"Pane",compatConfig:{MODE:3},props:{isSelecting:{type:Boolean},selectionKeyPressed:{type:Boolean}},setup(e){const{vueFlowRef:t,nodes:n,viewport:o,emits:r,userSelectionActive:i,removeSelectedElements:l,panOnDrag:a,userSelectionRect:s,elementsSelectable:u,nodesSelectionActive:c,getSelectedEdges:d,getSelectedNodes:f,removeNodes:p,removeEdges:v,selectionMode:h,deleteKeyCode:g,multiSelectionKeyCode:m,multiSelectionActive:y,edgeLookup:b,nodeLookup:w}=Tf(),x=Bt(null),_=Bt(0),S=Bt(0),E=Bt(),k=Bt(new Map),C=Wt((()=>u.value&&(e.isSelecting||i.value)));let O=!1,N=!1;const M=bc(g,{actInsideInputWithModifier:!1}),P=bc(m);function T(e,t){return n=>{n.target===t&&(null==e||e(n))}}function A(e){O?O=!1:(r.paneClick(e),l(),c.value=!1)}function D(e){var t;Array.isArray(a.value)&&(null==(t=a.value)?void 0:t.includes(2))?e.preventDefault():r.paneContextMenu(e)}function I(e){r.paneScroll(e)}return br(M,(e=>{e&&(p(f.value),v(d.value),c.value=!1)})),br(P,(e=>{y.value=e})),(a,d)=>(jr(),Fr("div",{ref_key:"container",ref:x,class:re(["vue-flow__pane vue-flow__container",{selection:a.isSelecting}]),onClick:d[0]||(d[0]=e=>C.value?void 0:T(A,x.value)(e)),onContextmenu:d[1]||(d[1]=e=>T(D,x.value)(e)),onWheelPassive:d[2]||(d[2]=e=>T(I,x.value)(e)),onPointerenter:d[3]||(d[3]=e=>C.value?void 0:Ft(r).paneMouseEnter(e)),onPointerdown:d[4]||(d[4]=n=>C.value?function(n){var o,i,a,c,d;if(E.value=null==(o=t.value)?void 0:o.getBoundingClientRect(),!u.value||!e.isSelecting||0!==n.button||n.target!==x.value||!E.value)return;null==(a=null==(i=n.target)?void 0:i.setPointerCapture)||a.call(i,n.pointerId);const{x:f,y:p}=function(e,t){return{x:e.clientX-t.left,y:e.clientY-t.top}}(n,E.value);N=!0,O=!1,k.value=new Map;for(const[e,t]of b.value)k.value.set(t.source,(null==(c=k.value.get(t.source))?void 0:c.add(e))||new Set([e])),k.value.set(t.target,(null==(d=k.value.get(t.target))?void 0:d.add(e))||new Set([e]));l(),s.value={width:0,height:0,startX:f,startY:p,x:f,y:p},r.selectionStart(n)}(n):Ft(r).paneMouseMove(n)),onPointermove:d[5]||(d[5]=e=>C.value?function(e){if(!E.value||!s.value)return;O=!0;const{x:t,y:l}=md(e,E.value),{startX:a=0,startY:u=0}=s.value,d={startX:a,startY:u,x:t<a?t:a,y:l<u?l:u,width:Math.abs(t-a),height:Math.abs(l-u)},f=Uc(n.value,d,o.value,h.value===dc.Partial,!0),p=new Set,v=new Set;for(const e of f){v.add(e.id);const t=k.value.get(e.id);if(t)for(const e of t)p.add(e)}if(_.value!==v.size){_.value=v.size;const e=od(w.value,v,!0);r.nodesChange(e)}if(S.value!==p.size){S.value=p.size;const e=od(b.value,p);r.edgesChange(e)}s.value=d,i.value=!0,c.value=!1}(e):Ft(r).paneMouseMove(e)),onPointerup:d[6]||(d[6]=t=>C.value?function(t){var n;0===t.button&&N&&(null==(n=t.target)||n.releasePointerCapture(t.pointerId),!i.value&&s.value&&t.target===x.value&&A(t),_.value>0&&(c.value=!0),i.value=!1,s.value=null,_.value=0,S.value=0,r.selectionEnd(t),e.selectionKeyPressed&&(O=!1),N=!1)}(t):void 0),onPointerleave:d[7]||(d[7]=e=>Ft(r).paneMouseLeave(e))},[So(a.$slots,"default"),Ft(i)&&Ft(s)?(jr(),Vr(Af,{key:0,"user-selection-rect":Ft(s)},null,8,["user-selection-rect"])):Kr("",!0),Ft(c)&&Ft(f).length?(jr(),Vr(If,{key:1})):Kr("",!0)],34))}}),zf=Wn({name:"Transform",compatConfig:{MODE:3},setup(e){const{viewport:t,fitViewOnInit:n,fitViewOnInitDone:o}=Tf(),r=yi((()=>!!n.value&&!o.value)),i=yi((()=>`translate(${t.value.x}px,${t.value.y}px) scale(${t.value.zoom})`));return(e,t)=>(jr(),Fr("div",{class:"vue-flow__transformationpane vue-flow__container",style:ee({transform:i.value,opacity:r.value?0:void 0})},[So(e.$slots,"default")],4))}}),jf=Wn({name:"Viewport",compatConfig:{MODE:3},setup(e){const{minZoom:t,maxZoom:n,defaultViewport:o,translateExtent:r,zoomActivationKeyCode:i,selectionKeyCode:l,panActivationKeyCode:a,panOnScroll:s,panOnScrollMode:u,panOnScrollSpeed:c,panOnDrag:d,zoomOnDoubleClick:f,zoomOnPinch:p,zoomOnScroll:v,preventScrolling:h,noWheelClassName:g,noPanClassName:m,emits:y,connectionStartHandle:b,userSelectionActive:w,paneDragging:x,d3Zoom:_,d3Selection:S,d3ZoomHandler:E,viewport:k,viewportRef:C,paneClickDistance:O}=Tf();!function(e){const{emits:t,dimensions:n}=Tf();let o;lo((()=>{const r=e.value,i=()=>{if(!r)return;const e=Cc(r);0!==e.width&&0!==e.height||t.error(new hd(pd.MISSING_VIEWPORT_DIMENSIONS)),n.value={width:e.width||500,height:e.height||500}};i(),window.addEventListener("resize",i),r&&(o=new ResizeObserver((()=>i())),o.observe(r)),uo((()=>{window.removeEventListener("resize",i),o&&r&&o.unobserve(r)}))}))}(C);const N=Bt(!1),M=Bt(!1);let P=null,T=!1,A=0,D={x:0,y:0,zoom:0};const I=bc(a),$=bc(l),z=bc(i),j=Wt((()=>!$.value&&(I.value||d.value))),B=Wt((()=>I.value||s.value)),L=Wt((()=>$.value||!0===l.value&&!0!==j.value));function R(e,t){return 2===t&&Array.isArray(e)&&e.includes(2)}function F(e){const t=e.ctrlKey&&yd()?10:1;return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*t}function V(e){return{x:e.x,y:e.y,zoom:e.k}}function H(e,t){return e.target.closest(`.${t}`)}return lo((()=>{if(!C.value)return void Ed("Viewport element is missing");const e=C.value,i=e.getBoundingClientRect(),l=function(){var e,t,n,o=rc,r=ic,i=uc,l=ac,a=sc,s=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],c=250,d=Qs,f=Ml("start","zoom","end"),p=500,v=150,h=0,g=10;function m(e){e.property("__zoom",lc).on("wheel.zoom",E,{passive:!1}).on("mousedown.zoom",k).on("dblclick.zoom",C).filter(a).on("touchstart.zoom",O).on("touchmove.zoom",N).on("touchend.zoom touchcancel.zoom",M).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(e,t){return(t=Math.max(s[0],Math.min(s[1],t)))===e.k?e:new ec(t,e.x,e.y)}function b(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new ec(e.k,o,r)}function w(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function x(e,t,n,o){e.on("start.zoom",(function(){_(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){_(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,i=arguments,l=_(e,i).event(o),a=r.apply(e,i),s=null==n?w(a):"function"==typeof n?n.apply(e,i):n,u=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),c=e.__zoom,f="function"==typeof t?t.apply(e,i):t,p=d(c.invert(s).concat(u/c.k),f.invert(s).concat(u/f.k));return function(e){if(1===e)e=f;else{var t=p(e),n=u/t[2];e=new ec(n,s[0]-t[0]*n,s[1]-t[1]*n)}l.zoom(null,e)}}))}function _(e,t,n){return!n&&e.__zooming||new S(e,t)}function S(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function E(e,...t){if(o.apply(this,arguments)){var n=_(this,t).event(e),r=this.__zoom,a=Math.max(s[0],Math.min(s[1],r.k*Math.pow(2,l.apply(this,arguments)))),c=Ya(e);if(n.wheel)n.mouse[0][0]===c[0]&&n.mouse[0][1]===c[1]||(n.mouse[1]=r.invert(n.mouse[0]=c)),clearTimeout(n.wheel);else{if(r.k===a)return;n.mouse=[c,r.invert(c)],Ou(this),n.start()}oc(e),n.wheel=setTimeout((function(){n.wheel=null,n.end()}),v),n.zoom("mouse",i(b(y(r,a),n.mouse[0],n.mouse[1]),n.extent,u))}}function k(e,...t){if(!n&&o.apply(this,arguments)){var r=e.currentTarget,l=_(this,t,!0).event(e),a=Ua(e.view).on("mousemove.zoom",(function(e){if(oc(e),!l.moved){var t=e.clientX-c,n=e.clientY-d;l.moved=t*t+n*n>h}l.event(e).zoom("mouse",i(b(l.that.__zoom,l.mouse[0]=Ya(e,r),l.mouse[1]),l.extent,u))}),!0).on("mouseup.zoom",(function(e){a.on("mousemove.zoom mouseup.zoom",null),Ka(e.view,l.moved),oc(e),l.event(e).end()}),!0),s=Ya(e,r),c=e.clientX,d=e.clientY;Za(e.view),nc(e),l.mouse=[s,this.__zoom.invert(s)],Ou(this),l.start()}}function C(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,l=Ya(e.changedTouches?e.changedTouches[0]:e,this),a=n.invert(l),s=n.k*(e.shiftKey?.5:2),d=i(b(y(n,s),l,a),r.apply(this,t),u);oc(e),c>0?Ua(this).transition().duration(c).call(x,d,l,e):Ua(this).call(m.transform,d,l,e)}}function O(n,...r){if(o.apply(this,arguments)){var i,l,a,s,u=n.touches,c=u.length,d=_(this,r,n.changedTouches.length===c).event(n);for(nc(n),l=0;l<c;++l)s=[s=Ya(a=u[l],this),this.__zoom.invert(s),a.identifier],d.touch0?d.touch1||d.touch0[2]===s[2]||(d.touch1=s,d.taps=0):(d.touch0=s,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=s[0],e=setTimeout((function(){e=null}),p)),Ou(this),d.start())}}function N(e,...t){if(this.__zooming){var n,o,r,l,a=_(this,t).event(e),s=e.changedTouches,c=s.length;for(oc(e),n=0;n<c;++n)r=Ya(o=s[n],this),a.touch0&&a.touch0[2]===o.identifier?a.touch0[0]=r:a.touch1&&a.touch1[2]===o.identifier&&(a.touch1[0]=r);if(o=a.that.__zoom,a.touch1){var d=a.touch0[0],f=a.touch0[1],p=a.touch1[0],v=a.touch1[1],h=(h=p[0]-d[0])*h+(h=p[1]-d[1])*h,g=(g=v[0]-f[0])*g+(g=v[1]-f[1])*g;o=y(o,Math.sqrt(h/g)),r=[(d[0]+p[0])/2,(d[1]+p[1])/2],l=[(f[0]+v[0])/2,(f[1]+v[1])/2]}else{if(!a.touch0)return;r=a.touch0[0],l=a.touch0[1]}a.zoom("touch",i(b(o,r,l),a.extent,u))}}function M(e,...o){if(this.__zooming){var r,i,l=_(this,o).event(e),a=e.changedTouches,s=a.length;for(nc(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),p),r=0;r<s;++r)i=a[r],l.touch0&&l.touch0[2]===i.identifier?delete l.touch0:l.touch1&&l.touch1[2]===i.identifier&&delete l.touch1;if(l.touch1&&!l.touch0&&(l.touch0=l.touch1,delete l.touch1),l.touch0)l.touch0[1]=this.__zoom.invert(l.touch0[0]);else if(l.end(),2===l.taps&&(i=Ya(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<g)){var u=Ua(this).on("dblclick.zoom");u&&u.apply(this,arguments)}}}return m.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",lc),e!==r?x(e,t,n,o):r.interrupt().each((function(){_(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},m.scaleBy=function(e,t,n,o){m.scaleTo(e,(function(){return this.__zoom.k*("function"==typeof t?t.apply(this,arguments):t)}),n,o)},m.scaleTo=function(e,t,n,o){m.transform(e,(function(){var e=r.apply(this,arguments),o=this.__zoom,l=null==n?w(e):"function"==typeof n?n.apply(this,arguments):n,a=o.invert(l),s="function"==typeof t?t.apply(this,arguments):t;return i(b(y(o,s),l,a),e,u)}),n,o)},m.translateBy=function(e,t,n,o){m.transform(e,(function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),u)}),null,o)},m.translateTo=function(e,t,n,o,l){m.transform(e,(function(){var e=r.apply(this,arguments),l=this.__zoom,a=null==o?w(e):"function"==typeof o?o.apply(this,arguments):o;return i(tc.translate(a[0],a[1]).scale(l.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,u)}),o,l)},S.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=Ua(this.that).datum();f.call(e,this.that,new Qu(e,{sourceEvent:this.sourceEvent,target:m,type:e,transform:this.that.__zoom,dispatch:f}),t)}},m.wheelDelta=function(e){return arguments.length?(l="function"==typeof e?e:Ju(+e),m):l},m.filter=function(e){return arguments.length?(o="function"==typeof e?e:Ju(!!e),m):o},m.touchable=function(e){return arguments.length?(a="function"==typeof e?e:Ju(!!e),m):a},m.extent=function(e){return arguments.length?(r="function"==typeof e?e:Ju([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),m):r},m.scaleExtent=function(e){return arguments.length?(s[0]=+e[0],s[1]=+e[1],m):[s[0],s[1]]},m.translateExtent=function(e){return arguments.length?(u[0][0]=+e[0][0],u[1][0]=+e[1][0],u[0][1]=+e[0][1],u[1][1]=+e[1][1],m):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},m.constrain=function(e){return arguments.length?(i=e,m):i},m.duration=function(e){return arguments.length?(c=+e,m):c},m.interpolate=function(e){return arguments.length?(d=e,m):d},m.on=function(){var e=f.on.apply(f,arguments);return e===f?m:e},m.clickDistance=function(e){return arguments.length?(h=(e=+e)*e,m):Math.sqrt(h)},m.tapDistance=function(e){return arguments.length?(g=+e,m):g},m}().clickDistance(O.value).scaleExtent([t.value,n.value]).translateExtent(r.value),a=Ua(e).call(l),d=a.on("wheel.zoom"),b=tc.translate(o.value.x??0,o.value.y??0).scale(Oc(o.value.zoom??1,t.value,n.value)),I=[[0,0],[i.width,i.height]],$=l.constrain()(b,I,r.value);l.transform(a,$),l.wheelDelta(F),_.value=l,S.value=a,E.value=d,k.value={x:$.x,y:$.y,zoom:$.k},l.on("start",(e=>{var t;if(!e.sourceEvent)return null;A=e.sourceEvent.button,N.value=!0;const n=V(e.transform);"mousedown"===(null==(t=e.sourceEvent)?void 0:t.type)&&(x.value=!0),D=n,y.viewportChangeStart(n),y.moveStart({event:e,flowTransform:n})})),l.on("end",(e=>{if(!e.sourceEvent)return null;if(N.value=!1,x.value=!1,R(j.value,A??0)&&!T&&y.paneContextMenu(e.sourceEvent),T=!1,t=D,n=e.transform,t.x!==n.x&&!Number.isNaN(n.x)||t.y!==n.y&&!Number.isNaN(n.y)||t.zoom!==n.k&&!Number.isNaN(n.k)){const t=V(e.transform);D=t,y.viewportChangeEnd(t),y.moveEnd({event:e,flowTransform:t})}var t,n})),l.filter((e=>{var t,n,o;const r=z.value||v.value,i=p.value&&e.ctrlKey,l=e.button;if((!0===j.value||Array.isArray(j.value)&&j.value.includes(1))&&1===l&&"mousedown"===e.type&&((null==(t=e.target)?void 0:t.closest(".vue-flow__node"))||(null==(n=e.target)?void 0:n.closest(".vue-flow__edge"))))return!0;if(!(j.value||r||B.value||f.value||p.value))return!1;if(w.value)return!1;if(!f.value&&"dblclick"===e.type)return!1;if(H(e,g.value)&&"wheel"===e.type)return!1;if(H(e,m.value)&&("wheel"!==e.type||B.value&&"wheel"===e.type&&!z.value))return!1;if(!p.value&&e.ctrlKey&&"wheel"===e.type)return!1;if(!r&&!B.value&&!i&&"wheel"===e.type)return!1;if(!p&&"touchstart"===e.type&&(null==(o=e.touches)?void 0:o.length)>1)return e.preventDefault(),!1;if(!j.value&&("mousedown"===e.type||"touchstart"===e.type))return!1;if(Array.isArray(j.value)&&!j.value.includes(l)&&("mousedown"===e.type||"touchstart"===e.type))return!1;const a=Array.isArray(j.value)&&j.value.includes(l)||!l||l<=1;return(!e.ctrlKey||"wheel"===e.type)&&a})),br([w,j],(()=>{w.value&&!N.value?l.on("zoom",null):w.value||l.on("zoom",(e=>{k.value={x:e.transform.x,y:e.transform.y,zoom:e.transform.k};const t=V(e.transform);T=R(j.value,A??0),y.viewportChange(t),y.move({event:e,flowTransform:t})}))}),{immediate:!0}),br([w,B,u,z,p,h,g],(()=>{!B.value||z.value||w.value?void 0!==d&&a.on("wheel.zoom",(function(e,t){const n=!h.value&&"wheel"===e.type&&!e.ctrlKey,o=z.value||v.value,r=p.value&&e.ctrlKey;if(!o&&!s.value&&!r&&"wheel"===e.type||n||H(e,g.value))return null;e.preventDefault(),d.call(this,e,t)}),{passive:!1}):a.on("wheel.zoom",(e=>{if(H(e,g.value))return!1;const t=z.value||v.value,n=p.value&&e.ctrlKey;if(h.value&&!B.value&&!t&&!n)return!1;e.preventDefault(),e.stopImmediatePropagation();const o=a.property("__zoom").k||1,r=yd();if(e.ctrlKey&&p.value&&r){const t=Ya(e),n=o*2**F(e);return void l.scaleTo(a,n,t,e)}const i=1===e.deltaMode?20:1;let s=u.value===hc.Vertical?0:e.deltaX*i,d=u.value===hc.Horizontal?0:e.deltaY*i;!r&&e.shiftKey&&u.value!==hc.Vertical&&!s&&d&&(s=d,d=0),l.translateBy(a,-s/o*c.value,-d/o*c.value);const f=V(a.property("__zoom"));P&&clearTimeout(P),M.value?(y.move({event:e,flowTransform:f}),y.viewportChange(f),P=setTimeout((()=>{y.moveEnd({event:e,flowTransform:f}),y.viewportChangeEnd(f),M.value=!1}),150)):(M.value=!0,y.moveStart({event:e,flowTransform:f}),y.viewportChangeStart(f))}),{passive:!1})}),{immediate:!0})})),(e,t)=>(jr(),Fr("div",{ref_key:"viewportRef",ref:C,class:"vue-flow__viewport vue-flow__container"},[qr($f,{"is-selecting":L.value,"selection-key-pressed":Ft($),class:re({connecting:!!Ft(b),dragging:Ft(x),draggable:!0===Ft(d)||Array.isArray(Ft(d))&&Ft(d).includes(0)})},{default:Sn((()=>[qr(zf,null,{default:Sn((()=>[So(e.$slots,"default")])),_:3})])),_:3},8,["is-selecting","selection-key-pressed","class"])],512))}}),Bf=["id"],Lf=["id"],Rf=["id"],Ff=Wn({name:"A11yDescriptions",compatConfig:{MODE:3},setup(e){const{id:t,disableKeyboardA11y:n,ariaLiveMessage:o}=Tf();return(e,r)=>(jr(),Fr(Tr,null,[Gr("div",{id:`${Ft(wc)}-${Ft(t)}`,style:{display:"none"}}," Press enter or space to select a node. "+ue(Ft(n)?"":"You can then use the arrow keys to move the node around.")+" You can then use the arrow keys to move the node around, press delete to remove it and press escape to cancel. ",9,Bf),Gr("div",{id:`${Ft(xc)}-${Ft(t)}`,style:{display:"none"}}," Press enter or space to select an edge. You can then press delete to remove it or press escape to cancel. ",8,Lf),Ft(n)?Kr("",!0):(jr(),Fr("div",{key:0,id:`${Ft("vue-flow__aria-live")}-${Ft(t)}`,"aria-live":"assertive","aria-atomic":"true",style:{position:"absolute",width:"1px",height:"1px",margin:"-1px",border:"0",padding:"0",overflow:"hidden",clip:"rect(0px, 0px, 0px, 0px)","clip-path":"inset(100%)"}},ue(Ft(o)),9,Rf))],64))}});function Vf(e,t,n){return n===cc.Left?e-t:n===cc.Right?e+t:e}function Hf(e,t,n){return n===cc.Top?e-t:n===cc.Bottom?e+t:e}const Uf=function({radius:e=10,centerX:t=0,centerY:n=0,position:o=cc.Top,type:r}){return bi("circle",{class:`vue-flow__edgeupdater vue-flow__edgeupdater-${r}`,cx:Vf(t,e,o),cy:Hf(n,e,o),r:e,stroke:"transparent",fill:"transparent"})};Uf.props=["radius","centerX","centerY","position","type"],Uf.compatConfig={MODE:3};const Yf=Uf,Xf=Wn({name:"Edge",compatConfig:{MODE:3},props:["id"],setup(e){const{id:t,addSelectedEdges:n,connectionMode:o,edgeUpdaterRadius:r,emits:i,nodesSelectionActive:l,noPanClassName:a,getEdgeTypes:s,removeSelectedEdges:u,findEdge:c,findNode:d,isValidConnection:f,multiSelectionActive:p,disableKeyboardA11y:v,elementsSelectable:h,edgesUpdatable:g,edgesFocusable:m}=Tf(),y=yi((()=>c(e.id))),b=function(e,t){const n={doubleClick:rd(),click:rd(),mouseEnter:rd(),mouseMove:rd(),mouseLeave:rd(),contextMenu:rd(),updateStart:rd(),update:rd(),updateEnd:rd()};return n.doubleClick.on((n=>{var o,r;t.edgeDoubleClick(n),null==(r=null==(o=e.events)?void 0:o.doubleClick)||r.call(o,n)})),n.click.on((n=>{var o,r;t.edgeClick(n),null==(r=null==(o=e.events)?void 0:o.click)||r.call(o,n)})),n.mouseEnter.on((n=>{var o,r;t.edgeMouseEnter(n),null==(r=null==(o=e.events)?void 0:o.mouseEnter)||r.call(o,n)})),n.mouseMove.on((n=>{var o,r;t.edgeMouseMove(n),null==(r=null==(o=e.events)?void 0:o.mouseMove)||r.call(o,n)})),n.mouseLeave.on((n=>{var o,r;t.edgeMouseLeave(n),null==(r=null==(o=e.events)?void 0:o.mouseLeave)||r.call(o,n)})),n.contextMenu.on((n=>{var o,r;t.edgeContextMenu(n),null==(r=null==(o=e.events)?void 0:o.contextMenu)||r.call(o,n)})),n.updateStart.on((n=>{var o,r;t.edgeUpdateStart(n),null==(r=null==(o=e.events)?void 0:o.updateStart)||r.call(o,n)})),n.update.on((n=>{var o,r;t.edgeUpdate(n),null==(r=null==(o=e.events)?void 0:o.update)||r.call(o,n)})),n.updateEnd.on((n=>{var o,r;t.edgeUpdateEnd(n),null==(r=null==(o=e.events)?void 0:o.updateEnd)||r.call(o,n)})),Object.entries(n).reduce(((e,[t,n])=>(e.emit[t]=n.trigger,e.on[t]=n.on,e)),{emit:{},on:{}})}(y.value,i),w=qo(Bd),x=ai(),_=Bt(!1),S=Bt(!1),E=Bt(""),k=Bt(null),C=Bt("source"),O=Bt(null),N=Wt((()=>void 0===y.value.selectable?h.value:y.value.selectable)),M=Wt((()=>void 0===y.value.updatable?g.value:y.value.updatable)),P=Wt((()=>void 0===y.value.focusable?m.value:y.value.focusable));Go(zd,e.id),Go(jd,O);const T=yi((()=>y.value.class instanceof Function?y.value.class(y.value):y.value.class)),A=yi((()=>y.value.style instanceof Function?y.value.style(y.value):y.value.style)),D=yi((()=>{const e=y.value.type||"default",t=null==w?void 0:w[`edge-${e}`];if(t)return t;let n=y.value.template??s.value[e];if("string"==typeof n&&x){const t=Object.keys(x.appContext.components);t&&t.includes(e)&&(n=mo(e,!1))}return n&&"string"!=typeof n?n:(i.error(new hd(pd.EDGE_TYPE_MISSING,n)),!1)})),{handlePointerDown:I}=Fd({nodeId:E,handleId:k,type:C,isValidConnection:f,edgeUpdaterType:C,onEdgeUpdate:function(e,t){b.emit.update({event:e,edge:y.value,connection:t})},onEdgeUpdateEnd:function(e){b.emit.updateEnd({event:e,edge:y.value}),S.value=!1}});return()=>{const n=d(y.value.source),l=d(y.value.target),u="pathOptions"in y.value?y.value.pathOptions:{};if(!n&&!l)return i.error(new hd(pd.EDGE_SOURCE_TARGET_MISSING,y.value.id,y.value.source,y.value.target)),null;if(!n)return i.error(new hd(pd.EDGE_SOURCE_MISSING,y.value.id,y.value.source)),null;if(!l)return i.error(new hd(pd.EDGE_TARGET_MISSING,y.value.id,y.value.target)),null;if(!y.value||y.value.hidden||n.hidden||l.hidden)return null;let c;c=o.value===pc.Strict?n.handleBounds.source:[...n.handleBounds.source||[],...n.handleBounds.target||[]];const f=cd(c,y.value.sourceHandle);let p;p=o.value===pc.Strict?l.handleBounds.target:[...l.handleBounds.target||[],...l.handleBounds.source||[]];const v=cd(p,y.value.targetHandle),h=(null==f?void 0:f.position)||cc.Bottom,g=(null==v?void 0:v.position)||cc.Top,{x:m,y:w}=ud(n,f,h),{x,y:E}=ud(l,v,g);return y.value.sourceX=m,y.value.sourceY=w,y.value.targetX=x,y.value.targetY=E,bi("g",{ref:O,key:e.id,"data-id":e.id,class:["vue-flow__edge",`vue-flow__edge-${!1===D.value?"default":y.value.type||"default"}`,a.value,T.value,{updating:_.value,selected:y.value.selected,animated:y.value.animated,inactive:!N.value}],onClick:B,onContextmenu:L,onDblclick:R,onMouseenter:F,onMousemove:V,onMouseleave:H,onKeyDown:P.value?X:void 0,tabIndex:P.value?0:void 0,"aria-label":null===y.value.ariaLabel?void 0:y.value.ariaLabel||`Edge from ${y.value.source} to ${y.value.target}`,"aria-describedby":P.value?`${xc}-${t}`:void 0,role:P.value?"button":"img"},[S.value?null:bi(!1===D.value?s.value.default:D.value,{id:e.id,sourceNode:n,targetNode:l,source:y.value.source,target:y.value.target,type:y.value.type,updatable:M.value,selected:y.value.selected,animated:y.value.animated,label:y.value.label,labelStyle:y.value.labelStyle,labelShowBg:y.value.labelShowBg,labelBgStyle:y.value.labelBgStyle,labelBgPadding:y.value.labelBgPadding,labelBgBorderRadius:y.value.labelBgBorderRadius,data:y.value.data,events:{...y.value.events,...b.on},style:A.value,markerStart:`url('#${qc(y.value.markerStart,t)}')`,markerEnd:`url('#${qc(y.value.markerEnd,t)}')`,sourcePosition:h,targetPosition:g,sourceX:m,sourceY:w,targetX:x,targetY:E,sourceHandleId:y.value.sourceHandle,targetHandleId:y.value.targetHandle,interactionWidth:y.value.interactionWidth,...u}),["source"===M.value||!0===M.value?[bi("g",{onMousedown:U,onMouseenter:$,onMouseout:z},bi(Yf,{position:h,centerX:m,centerY:w,radius:r.value,type:"source","data-type":"source"}))]:null,"target"===M.value||!0===M.value?[bi("g",{onMousedown:Y,onMouseenter:$,onMouseout:z},bi(Yf,{position:g,centerX:x,centerY:E,radius:r.value,type:"target","data-type":"target"}))]:null]])};function $(){_.value=!0}function z(){_.value=!1}function j(e,t){0===e.button&&(S.value=!0,E.value=t?y.value.target:y.value.source,k.value=(t?y.value.targetHandle:y.value.sourceHandle)??"",C.value=t?"target":"source",b.emit.updateStart({event:e,edge:y.value}),I(e))}function B(e){var t;const o={event:e,edge:y.value};N.value&&(l.value=!1,y.value.selected&&p.value?(u([y.value]),null==(t=O.value)||t.blur()):n([y.value])),b.emit.click(o)}function L(e){b.emit.contextMenu({event:e,edge:y.value})}function R(e){b.emit.doubleClick({event:e,edge:y.value})}function F(e){b.emit.mouseEnter({event:e,edge:y.value})}function V(e){b.emit.mouseMove({event:e,edge:y.value})}function H(e){b.emit.mouseLeave({event:e,edge:y.value})}function U(e){j(e,!0)}function Y(e){j(e,!1)}function X(t){var o;!v.value&&_c.includes(t.key)&&N.value&&("Escape"===t.key?(null==(o=O.value)||o.blur(),u([c(e.id)])):n([c(e.id)]))}}}),Gf={[cc.Left]:cc.Right,[cc.Right]:cc.Left,[cc.Top]:cc.Bottom,[cc.Bottom]:cc.Top},qf=Wn({name:"ConnectionLine",compatConfig:{MODE:3},setup(){var e;const{id:t,connectionMode:n,connectionStartHandle:o,connectionEndHandle:r,connectionPosition:i,connectionLineType:l,connectionLineStyle:a,connectionLineOptions:s,connectionStatus:u,viewport:c,findNode:d}=Tf(),f=null==(e=qo(Bd))?void 0:e["connection-line"],p=yi((()=>{var e;return d(null==(e=o.value)?void 0:e.nodeId)})),v=yi((()=>{var e;return d(null==(e=r.value)?void 0:e.nodeId)??null})),h=yi((()=>({x:(i.value.x-c.value.x)/c.value.zoom,y:(i.value.y-c.value.y)/c.value.zoom}))),g=yi((()=>s.value.markerStart?`url(#${qc(s.value.markerStart,t)})`:"")),m=yi((()=>s.value.markerEnd?`url(#${qc(s.value.markerEnd,t)})`:""));return()=>{var e,t,i,c;if(!p.value||!o.value)return null;const d=o.value.handleId,y=o.value.type,b=p.value.handleBounds;let w=null==b?void 0:b[y];if(n.value===pc.Loose&&(w=w||(null==b?void 0:b["source"===y?"target":"source"])),!w)return null;const x=(d?w.find((e=>e.id===d)):w[0])??null,_=(null==x?void 0:x.position)||cc.Top,{x:S,y:E}=ud(p.value,x,_);let k=null;v.value&&(null==(e=r.value)?void 0:e.handleId)&&(k=n.value===pc.Strict?(null==(t=v.value.handleBounds["source"===y?"target":"source"])?void 0:t.find((e=>{var t;return e.id===(null==(t=r.value)?void 0:t.handleId)})))||null:(null==(i=[...v.value.handleBounds.source||[],...v.value.handleBounds.target||[]])?void 0:i.find((e=>{var t;return e.id===(null==(t=r.value)?void 0:t.handleId)})))||null);const C=(null==(c=r.value)?void 0:c.position)??(_?Gf[_]:null);if(!_||!C)return null;const O=l.value??s.value.type??fc.Bezier;let N="";const M={sourceX:S,sourceY:E,sourcePosition:_,targetX:h.value.x,targetY:h.value.y,targetPosition:C};return O===fc.Bezier?[N]=mf(M):O===fc.Step?[N]=_f({...M,borderRadius:0}):O===fc.SmoothStep?[N]=_f(M):O===fc.SimpleBezier?[N]=bf(M):N=`M${S},${E} ${h.value.x},${h.value.y}`,bi("svg",{class:"vue-flow__edges vue-flow__connectionline vue-flow__container"},bi("g",{class:"vue-flow__connection"},f?bi(f,{sourceX:S,sourceY:E,sourcePosition:_,targetX:h.value.x,targetY:h.value.y,targetPosition:C,sourceNode:p.value,sourceHandle:x,targetNode:v.value,targetHandle:k,markerEnd:m.value,markerStart:g.value,connectionStatus:u.value}):bi("path",{d:N,class:[s.value.class,u,"vue-flow__connection-path"],style:{...a.value,...s.value.style},"marker-end":m.value,"marker-start":g.value})))}}}),Wf=["id","markerWidth","markerHeight","markerUnits","orient"],Zf=Wn({name:"MarkerType",compatConfig:{MODE:3},props:{id:{},type:{},color:{default:"none"},width:{default:12.5},height:{default:12.5},markerUnits:{default:"strokeWidth"},orient:{default:"auto-start-reverse"},strokeWidth:{default:1}},setup:e=>(e,t)=>(jr(),Fr("marker",{id:e.id,class:"vue-flow__arrowhead",viewBox:"-10 -10 20 20",refX:"0",refY:"0",markerWidth:`${e.width}`,markerHeight:`${e.height}`,markerUnits:e.markerUnits,orient:e.orient},[e.type===Ft(vc).ArrowClosed?(jr(),Fr("polyline",{key:0,style:ee({stroke:e.color,fill:e.color,strokeWidth:e.strokeWidth}),"stroke-linecap":"round","stroke-linejoin":"round",points:"-5,-4 0,0 -5,4 -5,-4"},null,4)):Kr("",!0),e.type===Ft(vc).Arrow?(jr(),Fr("polyline",{key:1,style:ee({stroke:e.color,strokeWidth:e.strokeWidth}),"stroke-linecap":"round","stroke-linejoin":"round",fill:"none",points:"-5,-4 0,0 -5,4"},null,4)):Kr("",!0)],8,Wf))}),Kf={class:"vue-flow__marker vue-flow__container"},Jf=Wn({name:"MarkerDefinitions",compatConfig:{MODE:3},setup(e){const{id:t,edges:n,connectionLineOptions:o,defaultMarkerColor:r}=Tf(),i=yi((()=>{const e=new Set,i=[],l=n=>{if(n){const o=qc(n,t);e.has(o)||("object"==typeof n?i.push({...n,id:o,color:n.color||r.value}):i.push({id:o,color:r.value,type:n}),e.add(o))}};for(const e of[o.value.markerEnd,o.value.markerStart])l(e);for(const e of n.value)for(const t of[e.markerStart,e.markerEnd])l(t);return i.sort(((e,t)=>e.id.localeCompare(t.id)))}));return(e,t)=>(jr(),Fr("svg",Kf,[Gr("defs",null,[(jr(!0),Fr(Tr,null,_o(i.value,(e=>(jr(),Vr(Zf,{id:e.id,key:e.id,type:e.type,color:e.color,width:e.width,height:e.height,markerUnits:e.markerUnits,"stroke-width":e.strokeWidth,orient:e.orient},null,8,["id","type","color","width","height","markerUnits","stroke-width","orient"])))),128))])]))}}),Qf=Wn({name:"Edges",compatConfig:{MODE:3},setup(e){const{findNode:t,getEdges:n,elevateEdgesOnSelect:o}=Tf();return(e,r)=>(jr(),Fr(Tr,null,[qr(Jf),(jr(!0),Fr(Tr,null,_o(Ft(n),(e=>(jr(),Fr("svg",{key:e.id,class:"vue-flow__edges vue-flow__container",style:ee({zIndex:Ft(fd)(e,Ft(t),Ft(o))})},[qr(Ft(Xf),{id:e.id},null,8,["id"])],4)))),128)),qr(Ft(qf))],64))}}),ep=Wn({name:"Node",compatConfig:{MODE:3},props:["id","resizeObserver"],setup(e){const{id:t,noPanClassName:n,selectNodesOnDrag:o,nodesSelectionActive:r,multiSelectionActive:i,emits:l,removeSelectedNodes:a,addSelectedNodes:s,updateNodeDimensions:u,onUpdateNodeInternals:c,getNodeTypes:d,nodeExtent:f,elevateNodesOnSelect:p,disableKeyboardA11y:v,ariaLiveMessage:h,snapToGrid:g,snapGrid:m,nodeDragThreshold:y,nodesDraggable:b,elementsSelectable:w,nodesConnectable:x,nodesFocusable:_}=Tf(),S=Bt(null);Go($d,S),Go(Id,e.id);const E=qo(Bd),k=ai(),C=Hd(),{node:O,parentNode:N}=Vd(e.id),M=Wt((()=>void 0===O.draggable?b.value:O.draggable)),P=Wt((()=>void 0===O.selectable?w.value:O.selectable)),T=Wt((()=>void 0===O.connectable?x.value:O.connectable)),A=Wt((()=>void 0===O.focusable?_.value:O.focusable)),D=Wt((()=>!!O.dimensions.width&&!!O.dimensions.height)),I=yi((()=>{const e=O.type||"default",t=null==E?void 0:E[`node-${e}`];if(t)return t;let n=O.template||d.value[e];if("string"==typeof n&&k){const t=Object.keys(k.appContext.components);t&&t.includes(e)&&(n=mo(e,!1))}return n&&"string"!=typeof n?n:(l.error(new hd(pd.NODE_TYPE_MISSING,n)),!1)})),{emit:$,on:z}=function(e,t){const n={doubleClick:rd(),click:rd(),mouseEnter:rd(),mouseMove:rd(),mouseLeave:rd(),contextMenu:rd(),dragStart:rd(),drag:rd(),dragStop:rd()};return n.doubleClick.on((n=>{var o,r;t.nodeDoubleClick(n),null==(r=null==(o=e.events)?void 0:o.doubleClick)||r.call(o,n)})),n.click.on((n=>{var o,r;t.nodeClick(n),null==(r=null==(o=e.events)?void 0:o.click)||r.call(o,n)})),n.mouseEnter.on((n=>{var o,r;t.nodeMouseEnter(n),null==(r=null==(o=e.events)?void 0:o.mouseEnter)||r.call(o,n)})),n.mouseMove.on((n=>{var o,r;t.nodeMouseMove(n),null==(r=null==(o=e.events)?void 0:o.mouseMove)||r.call(o,n)})),n.mouseLeave.on((n=>{var o,r;t.nodeMouseLeave(n),null==(r=null==(o=e.events)?void 0:o.mouseLeave)||r.call(o,n)})),n.contextMenu.on((n=>{var o,r;t.nodeContextMenu(n),null==(r=null==(o=e.events)?void 0:o.contextMenu)||r.call(o,n)})),n.dragStart.on((n=>{var o,r;t.nodeDragStart(n),null==(r=null==(o=e.events)?void 0:o.dragStart)||r.call(o,n)})),n.drag.on((n=>{var o,r;t.nodeDrag(n),null==(r=null==(o=e.events)?void 0:o.drag)||r.call(o,n)})),n.dragStop.on((n=>{var o,r;t.nodeDragStop(n),null==(r=null==(o=e.events)?void 0:o.dragStop)||r.call(o,n)})),Object.entries(n).reduce(((e,[t,n])=>(e.emit[t]=n.trigger,e.on[t]=n.on,e)),{emit:{},on:{}})}(O,l),j=Ld({id:e.id,el:S,disabled:()=>!M.value,selectable:P,dragHandle:()=>O.dragHandle,onStart(e){$.dragStart(e)},onDrag(e){$.drag(e)},onStop(e){$.dragStop(e)},onClick(e){q(e)}}),B=yi((()=>O.class instanceof Function?O.class(O):O.class)),L=yi((()=>{const e=(O.style instanceof Function?O.style(O):O.style)||{},t=O.width instanceof Function?O.width(O):O.width,n=O.height instanceof Function?O.height(O):O.height;return t&&(e.width="string"==typeof t?t:`${t}px`),n&&(e.height="string"==typeof n?n:`${n}px`),e})),R=Wt((()=>Number(O.zIndex??L.value.zIndex??0)));return c((t=>{!t.includes(e.id)&&t.length||V()})),lo((()=>{br((()=>O.hidden),((t=!1,n,o)=>{!t&&S.value&&(e.resizeObserver.observe(S.value),o((()=>{S.value&&e.resizeObserver.unobserve(S.value)})))}),{immediate:!0,flush:"post"})})),br([()=>O.type,()=>O.sourcePosition,()=>O.targetPosition],(()=>{vn((()=>{u([{id:e.id,nodeElement:S.value,forceUpdate:!0}])}))})),br([()=>O.position.x,()=>O.position.y,()=>{var e;return null==(e=N.value)?void 0:e.computedPosition.x},()=>{var e;return null==(e=N.value)?void 0:e.computedPosition.y},()=>{var e;return null==(e=N.value)?void 0:e.computedPosition.z},R,()=>O.selected,()=>O.dimensions.height,()=>O.dimensions.width,()=>{var e;return null==(e=N.value)?void 0:e.dimensions.height},()=>{var e;return null==(e=N.value)?void 0:e.dimensions.width}],(([e,t,n,o,r,i])=>{const l={x:e,y:t,z:i+(p.value&&O.selected?1e3:0)};var a,s;O.computedPosition=void 0!==n&&void 0!==o?{x:(s=l).x+(a={x:n,y:o,z:r}).x,y:s.y+a.y,z:(a.z>s.z?a.z:s.z)+1}:l}),{flush:"post",immediate:!0}),br([()=>O.extent,f],(([e,t],[n,o])=>{e===n&&t===o||F()})),"parent"===O.extent||"object"==typeof O.extent&&"range"in O.extent&&"parent"===O.extent.range?_l((()=>D)).toBe(!0).then(F):F(),()=>O.hidden?null:bi("div",{ref:S,"data-id":O.id,class:["vue-flow__node",`vue-flow__node-${!1===I.value?"default":O.type||"default"}`,{[n.value]:M.value,dragging:null==j?void 0:j.value,draggable:M.value,selected:O.selected,selectable:P.value,parent:O.isParent},B.value],style:{visibility:D.value?"visible":"hidden",zIndex:O.computedPosition.z??R.value,transform:`translate(${O.computedPosition.x}px,${O.computedPosition.y}px)`,pointerEvents:P.value||M.value?"all":"none",...L.value},tabIndex:A.value?0:void 0,role:A.value?"button":void 0,"aria-describedby":v.value?void 0:`${wc}-${t}`,"aria-label":O.ariaLabel,onMouseenter:H,onMousemove:U,onMouseleave:Y,onContextmenu:X,onClick:q,onDblclick:G,onKeydown:W},[bi(!1===I.value?d.value.default:I.value,{id:O.id,type:O.type,data:O.data,events:{...O.events,...z},selected:O.selected,resizing:O.resizing,dragging:j.value,connectable:T.value,position:O.computedPosition,dimensions:O.dimensions,isValidTargetPos:O.isValidTargetPos,isValidSourcePos:O.isValidSourcePos,parent:O.parentNode,parentNodeId:O.parentNode,zIndex:O.computedPosition.z??R.value,targetPosition:O.targetPosition,sourcePosition:O.sourcePosition,label:O.label,dragHandle:O.dragHandle,onUpdateNodeInternals:V})]);function F(){const e=O.computedPosition;g.value&&(e.x=m.value[0]*Math.round(e.x/m.value[0]),e.y=m.value[1]*Math.round(e.y/m.value[1]));const{computedPosition:t,position:n}=sd(O,e,l.error,f.value,N.value);O.computedPosition.x===t.x&&O.computedPosition.y===t.y||(O.computedPosition={...O.computedPosition,...t}),O.position.x===n.x&&O.position.y===n.y||(O.position=n)}function V(){S.value&&u([{id:e.id,nodeElement:S.value,forceUpdate:!0}])}function H(e){(null==j?void 0:j.value)||$.mouseEnter({event:e,node:O})}function U(e){(null==j?void 0:j.value)||$.mouseMove({event:e,node:O})}function Y(e){(null==j?void 0:j.value)||$.mouseLeave({event:e,node:O})}function X(e){return $.contextMenu({event:e,node:O})}function G(e){return $.doubleClick({event:e,node:O})}function q(e){P.value&&(!o.value||!M.value||y.value>0)&&Od(O,i.value,s,a,r,!1,S.value),$.click({event:e,node:O})}function W(e){if(!mc(e)&&!v.value)if(_c.includes(e.key)&&P.value){const t="Escape"===e.key;Od(O,i.value,s,a,r,t,S.value)}else M.value&&O.selected&&Sc[e.key]&&(h.value=`Moved selected node ${e.key.replace("Arrow","").toLowerCase()}. New position, x: ${~~O.position.x}, y: ${~~O.position.y}`,C({x:Sc[e.key].x,y:Sc[e.key].y},e.shiftKey))}}}),tp=ep,np={height:"0",width:"0"},op=Wn({name:"EdgeLabelRenderer",compatConfig:{MODE:3},setup(e){const{viewportRef:t}=Tf(),n=Wt((()=>{var e;return null==(e=t.value)?void 0:e.getElementsByClassName("vue-flow__edge-labels")[0]}));return(e,t)=>(jr(),Fr("svg",null,[(jr(),Fr("foreignObject",np,[(jr(),Vr(Dn,{to:n.value,disabled:!n.value},[So(e.$slots,"default")],8,["to","disabled"]))]))]))}}),rp={class:"vue-flow__nodes vue-flow__container"},ip=Wn({name:"Nodes",compatConfig:{MODE:3},setup(e){const{getNodes:t,updateNodeDimensions:n,emits:o}=Tf(),r=function(e={includeHiddenNodes:!1}){const{nodes:t}=Tf();return yi((()=>{if(0===t.value.length)return!1;for(const n of t.value)if((e.includeHiddenNodes||!n.hidden)&&(void 0===(null==n?void 0:n.handleBounds)||0===n.dimensions.width||0===n.dimensions.height))return!1;return!0}))}(),i=Bt();return br(r,(e=>{e&&vn((()=>{o.nodesInitialized(t.value)}))}),{immediate:!0}),lo((()=>{i.value=new ResizeObserver((e=>{const t=e.map((e=>({id:e.target.getAttribute("data-id"),nodeElement:e.target,forceUpdate:!0})));vn((()=>n(t)))}))})),uo((()=>{var e;return null==(e=i.value)?void 0:e.disconnect()})),(e,n)=>(jr(),Fr("div",rp,[i.value?(jr(!0),Fr(Tr,{key:0},_o(Ft(t),((e,t,n,o)=>{const r=[e.id];if(o&&o.key===e.id&&function(e,t){let n=e.memo;if(n.length!=t.length)return!1;for(let e=0;e<n.length;e++)if(q(n[e],t[e]))return!1;return Br>0&&zr&&zr.push(e),!0}(o,r))return o;const l=(jr(),Vr(Ft(tp),{id:e.id,key:e.id,"resize-observer":i.value},null,8,["id","resize-observer"]));return l.memo=r,l}),n,0),128)):Kr("",!0)]))}}),lp=Gr("div",{class:"vue-flow__edge-labels"},null,-1),ap=Wn({name:"VueFlow",compatConfig:{MODE:3},props:{id:{},modelValue:{},nodes:{},edges:{},edgeTypes:{},nodeTypes:{},connectionMode:{},connectionLineType:{},connectionLineStyle:{default:void 0},connectionLineOptions:{default:void 0},connectionRadius:{},isValidConnection:{type:[Function,null],default:void 0},deleteKeyCode:{default:void 0},selectionKeyCode:{default:void 0},multiSelectionKeyCode:{default:void 0},zoomActivationKeyCode:{default:void 0},panActivationKeyCode:{default:void 0},snapToGrid:{type:Boolean,default:void 0},snapGrid:{},onlyRenderVisibleElements:{type:Boolean,default:void 0},edgesUpdatable:{type:[Boolean,String],default:void 0},nodesDraggable:{type:Boolean,default:void 0},nodesConnectable:{type:Boolean,default:void 0},nodeDragThreshold:{},elementsSelectable:{type:Boolean,default:void 0},selectNodesOnDrag:{type:Boolean,default:void 0},panOnDrag:{type:[Boolean,Array],default:void 0},minZoom:{},maxZoom:{},defaultViewport:{},translateExtent:{},nodeExtent:{},defaultMarkerColor:{},zoomOnScroll:{type:Boolean,default:void 0},zoomOnPinch:{type:Boolean,default:void 0},panOnScroll:{type:Boolean,default:void 0},panOnScrollSpeed:{},panOnScrollMode:{},paneClickDistance:{},zoomOnDoubleClick:{type:Boolean,default:void 0},preventScrolling:{type:Boolean,default:void 0},selectionMode:{},edgeUpdaterRadius:{},fitViewOnInit:{type:Boolean,default:void 0},connectOnClick:{type:Boolean,default:void 0},applyDefault:{type:Boolean,default:void 0},autoConnect:{type:[Boolean,Function],default:void 0},noDragClassName:{},noWheelClassName:{},noPanClassName:{},defaultEdgeOptions:{},elevateEdgesOnSelect:{type:Boolean,default:void 0},elevateNodesOnSelect:{type:Boolean,default:void 0},disableKeyboardA11y:{type:Boolean,default:void 0},edgesFocusable:{type:Boolean,default:void 0},nodesFocusable:{type:Boolean,default:void 0},autoPanOnConnect:{type:Boolean,default:void 0},autoPanOnNodeDrag:{type:Boolean,default:void 0},autoPanSpeed:{}},emits:["nodesChange","edgesChange","nodesInitialized","paneReady","init","updateNodeInternals","error","connect","connectStart","connectEnd","clickConnectStart","clickConnectEnd","moveStart","move","moveEnd","selectionDragStart","selectionDrag","selectionDragStop","selectionContextMenu","selectionStart","selectionEnd","viewportChangeStart","viewportChange","viewportChangeEnd","paneScroll","paneClick","paneContextMenu","paneMouseEnter","paneMouseMove","paneMouseLeave","edgeUpdate","edgeContextMenu","edgeMouseEnter","edgeMouseMove","edgeMouseLeave","edgeDoubleClick","edgeClick","edgeUpdateStart","edgeUpdateEnd","nodeContextMenu","nodeMouseEnter","nodeMouseMove","nodeMouseLeave","nodeDoubleClick","nodeClick","nodeDragStart","nodeDrag","nodeDragStop","miniMapNodeClick","miniMapNodeDoubleClick","miniMapNodeMouseEnter","miniMapNodeMouseMove","miniMapNodeMouseLeave","update:modelValue","update:nodes","update:edges"],setup(e,{expose:t,emit:n}){const o=e,r=Mo(),i=Ol(o,"modelValue",n),l=Ol(o,"nodes",n),a=Ol(o,"edges",n),s=Tf(o),u=function(e,t,n){const o=pe(!0);return o.run((()=>{o.run((()=>{let t,o,r=!(!n.nodes.value.length&&!n.edges.value.length);t=bl([e.modelValue,()=>{var t,n;return null==(n=null==(t=e.modelValue)?void 0:t.value)?void 0:n.length}],(([e])=>{e&&Array.isArray(e)&&(null==o||o.pause(),n.setElements(e),o||r||!e.length?null==o||o.resume():r=!0)})),o=bl([n.nodes,n.edges,()=>n.edges.value.length,()=>n.nodes.value.length],(([n,o])=>{var r;(null==(r=e.modelValue)?void 0:r.value)&&Array.isArray(e.modelValue.value)&&(null==t||t.pause(),e.modelValue.value=[...n,...o],vn((()=>{null==t||t.resume()})))}),{immediate:r}),he((()=>{null==t||t.stop(),null==o||o.stop()}))})),o.run((()=>{let t,o,r=!!n.nodes.value.length;t=bl([e.nodes,()=>{var t,n;return null==(n=null==(t=e.nodes)?void 0:t.value)?void 0:n.length}],(([e])=>{e&&Array.isArray(e)&&(null==o||o.pause(),n.setNodes(e),o||r||!e.length?null==o||o.resume():r=!0)})),o=bl([n.nodes,()=>n.nodes.value.length],(([n])=>{var o;(null==(o=e.nodes)?void 0:o.value)&&Array.isArray(e.nodes.value)&&(null==t||t.pause(),e.nodes.value=[...n],vn((()=>{null==t||t.resume()})))}),{immediate:r}),he((()=>{null==t||t.stop(),null==o||o.stop()}))})),o.run((()=>{let t,o,r=!!n.edges.value.length;t=bl([e.edges,()=>{var t,n;return null==(n=null==(t=e.edges)?void 0:t.value)?void 0:n.length}],(([e])=>{e&&Array.isArray(e)&&(null==o||o.pause(),n.setEdges(e),o||r||!e.length?null==o||o.resume():r=!0)})),o=bl([n.edges,()=>n.edges.value.length],(([n])=>{var o;(null==(o=e.edges)?void 0:o.value)&&Array.isArray(e.edges.value)&&(null==t||t.pause(),e.edges.value=[...n],vn((()=>{null==t||t.resume()})))}),{immediate:r}),he((()=>{null==t||t.stop(),null==o||o.stop()}))})),o.run((()=>{br((()=>t.minZoom),(()=>{t.minZoom&&Nd(t.minZoom)&&n.setMinZoom(t.minZoom)}),{immediate:!0})})),o.run((()=>{br((()=>t.maxZoom),(()=>{t.maxZoom&&Nd(t.maxZoom)&&n.setMaxZoom(t.maxZoom)}),{immediate:!0})})),o.run((()=>{br((()=>t.translateExtent),(()=>{t.translateExtent&&Nd(t.translateExtent)&&n.setTranslateExtent(t.translateExtent)}),{immediate:!0})})),o.run((()=>{br((()=>t.nodeExtent),(()=>{t.nodeExtent&&Nd(t.nodeExtent)&&n.setNodeExtent(t.nodeExtent)}),{immediate:!0})})),o.run((()=>{br((()=>t.applyDefault),(()=>{Nd(t.applyDefault)&&(n.applyDefault.value=t.applyDefault)}),{immediate:!0})})),o.run((()=>{const e=async e=>{let o=e;"function"==typeof t.autoConnect&&(o=await t.autoConnect(e)),!1!==o&&n.addEdges([o])};br((()=>t.autoConnect),(()=>{Nd(t.autoConnect)&&(n.autoConnect.value=t.autoConnect)}),{immediate:!0}),br(n.autoConnect,((t,o,r)=>{t?n.onConnect(e):n.hooks.value.connect.off(e),r((()=>{n.hooks.value.connect.off(e)}))}),{immediate:!0})})),(()=>{const e=["id","modelValue","translateExtent","nodeExtent","edges","nodes","maxZoom","minZoom","applyDefault","autoConnect"];for(const r of Object.keys(t)){const i=r;if(!e.includes(i)){const e=Wt((()=>t[i])),r=n[i];jt(r)&&o.run((()=>{br(e,(e=>{Nd(e)&&(r.value=e)}),{immediate:!0})}))}}})()})),()=>o.stop()}({modelValue:i,nodes:l,edges:a},o,s);return function(e,t){io((()=>{for(const[n,o]of Object.entries(t.value)){const t=t=>{e(n,t)};o.fns.add(t),cl((()=>{o.off(t)}))}}))}(n,s.hooks),function(){const e=Tf();br((()=>e.viewportHelper.value.viewportInitialized),(t=>{t&&setTimeout((()=>{e.emits.init(e),e.emits.paneReady(e)}),1)}))}(),function(){const{emits:e}=Tf();lo((()=>{if(kd()){const t=document.querySelector(".vue-flow__pane");t&&"1"!==window.getComputedStyle(t).zIndex&&e.error(new hd(pd.MISSING_STYLES))}}))}(),Go(Bd,r),co((()=>{u()})),t(s),(e,t)=>(jr(),Fr("div",{ref:Ft(s).vueFlowRef,class:"vue-flow"},[qr(jf,null,{default:Sn((()=>[qr(Qf),lp,qr(ip),So(e.$slots,"zoom-pane")])),_:3}),So(e.$slots,"default"),qr(Ff)],512))}}),sp=Wn({name:"Panel",compatConfig:{MODE:3},props:{position:{}},setup(e){const t=e,{userSelectionActive:n}=Tf(),o=yi((()=>`${t.position}`.split("-")));return(e,t)=>(jr(),Fr("div",{class:re(["vue-flow__panel",o.value]),style:ee({pointerEvents:Ft(n)?"none":"all"})},[So(e.$slots,"default")],6))}});var up=n(72),cp=n.n(up),dp=n(825),fp=n.n(dp),pp=n(659),vp=n.n(pp),hp=n(56),gp=n.n(hp),mp=n(540),yp=n.n(mp),bp=n(113),wp=n.n(bp),xp=n(955),_p={};_p.styleTagTransform=wp(),_p.setAttributes=gp(),_p.insert=vp().bind(null,"head"),_p.domAPI=fp(),_p.insertStyleElement=yp(),cp()(xp.A,_p),xp.A&&xp.A.locals&&xp.A.locals;var Sp=n(739),Ep={};Ep.styleTagTransform=wp(),Ep.setAttributes=gp(),Ep.insert=vp().bind(null,"head"),Ep.domAPI=fp(),Ep.insertStyleElement=yp(),cp()(Sp.A,Ep),Sp.A&&Sp.A.locals&&Sp.A.locals;var kp=n(166),Cp={};Cp.styleTagTransform=wp(),Cp.setAttributes=gp(),Cp.insert=vp().bind(null,"head"),Cp.domAPI=fp(),Cp.insertStyleElement=yp(),cp()(kp.A,Cp),kp.A&&kp.A.locals&&kp.A.locals;var Op=(e=>(e.Lines="lines",e.Dots="dots",e))(Op||{});const Np=function({dimensions:e,size:t,color:n}){return bi("path",{stroke:n,"stroke-width":t,d:`M${e[0]/2} 0 V${e[1]} M0 ${e[1]/2} H${e[0]}`})},Mp=function({radius:e,color:t}){return bi("circle",{cx:e,cy:e,r:e,fill:t})};Op.Lines,Op.Dots;const Pp={[Op.Dots]:"#81818a",[Op.Lines]:"#eee"},Tp=["id","x","y","width","height","patternTransform"],Ap={key:2,height:"100",width:"100"},Dp=["fill"],Ip=["x","y","fill"],$p=Wn({name:"Background",compatConfig:{MODE:3},props:{id:{},variant:{default:()=>Op.Dots},gap:{default:20},size:{default:1},lineWidth:{default:1},patternColor:{},color:{},bgColor:{},height:{default:100},width:{default:100},x:{default:0},y:{default:0},offset:{default:2}},setup(e){const{id:t,viewport:n}=Tf(),o=yi((()=>{const[t,o]=Array.isArray(e.gap)?e.gap:[e.gap,e.gap],r=[t*n.value.zoom||1,o*n.value.zoom||1],i=e.size*n.value.zoom;return{scaledGap:r,offset:e.variant===Op.Dots?[i/e.offset,i/e.offset]:[r[0]/e.offset,r[1]/e.offset],size:i}})),r=Wt((()=>`pattern-${t}${e.id?`-${e.id}`:""}`)),i=Wt((()=>e.color||e.patternColor||Pp[e.variant||Op.Dots]));return(e,t)=>(jr(),Fr("svg",{class:"vue-flow__background vue-flow__container",style:ee({height:`${e.height>100?100:e.height}%`,width:`${e.width>100?100:e.width}%`})},[So(e.$slots,"pattern-container",{id:r.value},(()=>[Gr("pattern",{id:r.value,x:Ft(n).x%o.value.scaledGap[0],y:Ft(n).y%o.value.scaledGap[1],width:o.value.scaledGap[0],height:o.value.scaledGap[1],patternTransform:`translate(-${o.value.offset[0]},-${o.value.offset[1]})`,patternUnits:"userSpaceOnUse"},[So(e.$slots,"pattern",{},(()=>[e.variant===Ft(Op).Lines?(jr(),Vr(Ft(Np),{key:0,size:e.lineWidth,color:i.value,dimensions:o.value.scaledGap},null,8,["size","color","dimensions"])):e.variant===Ft(Op).Dots?(jr(),Vr(Ft(Mp),{key:1,color:i.value,radius:o.value.size/e.offset},null,8,["color","radius"])):Kr("",!0),e.bgColor?(jr(),Fr("svg",Ap,[Gr("rect",{width:"100%",height:"100%",fill:e.bgColor},null,8,Dp)])):Kr("",!0)]))],8,Tp)])),Gr("rect",{x:e.x,y:e.y,width:"100%",height:"100%",fill:`url(#${r.value})`},null,8,Ip),So(e.$slots,"default",{id:r.value})],4))}}),zp={class:"vue-flow__controls-button"},jp=((e,t)=>{const n=e.__vccOpts||e;for(const[e,o]of t)n[e]=o;return n})({name:"ControlButton",compatConfig:{MODE:3}},[["render",function(e,t,n,o,r,i){return jr(),Fr("button",zp,[So(e.$slots,"default")])}]]),Bp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 32"},Lp=[Gr("path",{d:"M32 18.133H18.133V32h-4.266V18.133H0v-4.266h13.867V0h4.266v13.867H32z"},null,-1)],Rp={render:function(e,t){return jr(),Fr("svg",Bp,Lp)}},Fp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 5"},Vp=[Gr("path",{d:"M0 0h32v4.2H0z"},null,-1)],Hp={render:function(e,t){return jr(),Fr("svg",Fp,Vp)}},Up={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 32 30"},Yp=[Gr("path",{d:"M3.692 4.63c0-.53.4-.938.939-.938h5.215V0H4.708C2.13 0 0 2.054 0 4.63v5.216h3.692V4.631zM27.354 0h-5.2v3.692h5.17c.53 0 .984.4.984.939v5.215H32V4.631A4.624 4.624 0 0 0 27.354 0zm.954 24.83c0 .532-.4.94-.939.94h-5.215v3.768h5.215c2.577 0 4.631-2.13 4.631-4.707v-5.139h-3.692v5.139zm-23.677.94a.919.919 0 0 1-.939-.94v-5.138H0v5.139c0 2.577 2.13 4.707 4.708 4.707h5.138V25.77H4.631z"},null,-1)],Xp={render:function(e,t){return jr(),Fr("svg",Up,Yp)}},Gp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},qp=[Gr("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0 8 0 4.571 3.429 4.571 7.619v3.048H3.048A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047zm4.724-13.866H7.467V7.619c0-2.59 2.133-4.724 4.723-4.724 2.591 0 4.724 2.133 4.724 4.724v3.048z"},null,-1)],Wp={render:function(e,t){return jr(),Fr("svg",Gp,qp)}},Zp={xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 25 32"},Kp=[Gr("path",{d:"M21.333 10.667H19.81V7.619C19.81 3.429 16.38 0 12.19 0c-4.114 1.828-1.37 2.133.305 2.438 1.676.305 4.42 2.59 4.42 5.181v3.048H3.047A3.056 3.056 0 0 0 0 13.714v15.238A3.056 3.056 0 0 0 3.048 32h18.285a3.056 3.056 0 0 0 3.048-3.048V13.714a3.056 3.056 0 0 0-3.048-3.047zM12.19 24.533a3.056 3.056 0 0 1-3.047-3.047 3.056 3.056 0 0 1 3.047-3.048 3.056 3.056 0 0 1 3.048 3.048 3.056 3.056 0 0 1-3.048 3.047z"},null,-1)],Jp={render:function(e,t){return jr(),Fr("svg",Zp,Kp)}},Qp=Wn({name:"Controls",compatConfig:{MODE:3},props:{showZoom:{type:Boolean,default:!0},showFitView:{type:Boolean,default:!0},showInteractive:{type:Boolean,default:!0},fitViewParams:{},position:{default:()=>gc.BottomLeft}},emits:["zoomIn","zoomOut","fitView","interactionChange"],setup(e,{emit:t}){const{nodesDraggable:n,nodesConnectable:o,elementsSelectable:r,setInteractive:i,zoomIn:l,zoomOut:a,fitView:s,viewport:u,minZoom:c,maxZoom:d}=Tf(),f=Wt((()=>n.value||o.value||r.value)),p=Wt((()=>u.value.zoom<=c.value)),v=Wt((()=>u.value.zoom>=d.value));function h(){l(),t("zoomIn")}function g(){a(),t("zoomOut")}function m(){s(e.fitViewParams),t("fitView")}function y(){i(!f.value),t("interactionChange",!f.value)}return(e,t)=>(jr(),Vr(Ft(sp),{class:"vue-flow__controls",position:e.position},{default:Sn((()=>[So(e.$slots,"top"),e.showZoom?(jr(),Fr(Tr,{key:0},[So(e.$slots,"control-zoom-in",{},(()=>[qr(jp,{class:"vue-flow__controls-zoomin",disabled:v.value,onClick:h},{default:Sn((()=>[So(e.$slots,"icon-zoom-in",{},(()=>[(jr(),Vr(bo(Ft(Rp))))]))])),_:3},8,["disabled"])])),So(e.$slots,"control-zoom-out",{},(()=>[qr(jp,{class:"vue-flow__controls-zoomout",disabled:p.value,onClick:g},{default:Sn((()=>[So(e.$slots,"icon-zoom-out",{},(()=>[(jr(),Vr(bo(Ft(Hp))))]))])),_:3},8,["disabled"])]))],64)):Kr("",!0),e.showFitView?So(e.$slots,"control-fit-view",{key:1},(()=>[qr(jp,{class:"vue-flow__controls-fitview",onClick:m},{default:Sn((()=>[So(e.$slots,"icon-fit-view",{},(()=>[(jr(),Vr(bo(Ft(Xp))))]))])),_:3})])):Kr("",!0),e.showInteractive?So(e.$slots,"control-interactive",{key:2},(()=>[e.showInteractive?(jr(),Vr(jp,{key:0,class:"vue-flow__controls-interactive",onClick:y},{default:Sn((()=>[f.value?So(e.$slots,"icon-unlock",{key:0},(()=>[(jr(),Vr(bo(Ft(Jp))))])):Kr("",!0),f.value?Kr("",!0):So(e.$slots,"icon-lock",{key:1},(()=>[(jr(),Vr(bo(Ft(Wp))))]))])),_:3})):Kr("",!0)])):Kr("",!0),So(e.$slots,"default")])),_:3},8,["position"]))}});var ev={value:()=>{}};function tv(){for(var e,t=0,n=arguments.length,o={};t<n;++t){if(!(e=arguments[t]+"")||e in o||/[\s.]/.test(e))throw new Error("illegal type: "+e);o[e]=[]}return new nv(o)}function nv(e){this._=e}function ov(e,t){for(var n,o=0,r=e.length;o<r;++o)if((n=e[o]).name===t)return n.value}function rv(e,t,n){for(var o=0,r=e.length;o<r;++o)if(e[o].name===t){e[o]=ev,e=e.slice(0,o).concat(e.slice(o+1));break}return null!=n&&e.push({name:t,value:n}),e}nv.prototype=tv.prototype={constructor:nv,on:function(e,t){var n,o,r=this._,i=(o=r,(e+"").trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");if(n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),e&&!o.hasOwnProperty(e))throw new Error("unknown type: "+e);return{type:e,name:t}}))),l=-1,a=i.length;if(!(arguments.length<2)){if(null!=t&&"function"!=typeof t)throw new Error("invalid callback: "+t);for(;++l<a;)if(n=(e=i[l]).type)r[n]=rv(r[n],e.name,t);else if(null==t)for(n in r)r[n]=rv(r[n],e.name,null);return this}for(;++l<a;)if((n=(e=i[l]).type)&&(n=ov(r[n],e.name)))return n},copy:function(){var e={},t=this._;for(var n in t)e[n]=t[n].slice();return new nv(e)},call:function(e,t){if((n=arguments.length-2)>0)for(var n,o,r=new Array(n),i=0;i<n;++i)r[i]=arguments[i+2];if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(i=0,n=(o=this._[e]).length;i<n;++i)o[i].value.apply(t,r)},apply:function(e,t,n){if(!this._.hasOwnProperty(e))throw new Error("unknown type: "+e);for(var o=this._[e],r=0,i=o.length;r<i;++r)o[r].value.apply(t,n)}};var iv="http://www.w3.org/1999/xhtml";const lv={svg:"http://www.w3.org/2000/svg",xhtml:iv,xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"};function av(e){var t=e+="",n=t.indexOf(":");return n>=0&&"xmlns"!==(t=e.slice(0,n))&&(e=e.slice(n+1)),lv.hasOwnProperty(t)?{space:lv[t],local:e}:e}function sv(e){return function(){var t=this.ownerDocument,n=this.namespaceURI;return n===iv&&t.documentElement.namespaceURI===iv?t.createElement(e):t.createElementNS(n,e)}}function uv(e){return function(){return this.ownerDocument.createElementNS(e.space,e.local)}}function cv(e){var t=av(e);return(t.local?uv:sv)(t)}function dv(){}function fv(e){return null==e?dv:function(){return this.querySelector(e)}}function pv(){return[]}function vv(e){return null==e?pv:function(){return this.querySelectorAll(e)}}function hv(e){return function(){return this.matches(e)}}function gv(e){return function(t){return t.matches(e)}}var mv=Array.prototype.find;function yv(){return this.firstElementChild}var bv=Array.prototype.filter;function wv(){return Array.from(this.children)}function xv(e){return new Array(e.length)}function _v(e,t){this.ownerDocument=e.ownerDocument,this.namespaceURI=e.namespaceURI,this._next=null,this._parent=e,this.__data__=t}function Sv(e,t,n,o,r,i){for(var l,a=0,s=t.length,u=i.length;a<u;++a)(l=t[a])?(l.__data__=i[a],o[a]=l):n[a]=new _v(e,i[a]);for(;a<s;++a)(l=t[a])&&(r[a]=l)}function Ev(e,t,n,o,r,i,l){var a,s,u,c=new Map,d=t.length,f=i.length,p=new Array(d);for(a=0;a<d;++a)(s=t[a])&&(p[a]=u=l.call(s,s.__data__,a,t)+"",c.has(u)?r[a]=s:c.set(u,s));for(a=0;a<f;++a)u=l.call(e,i[a],a,i)+"",(s=c.get(u))?(o[a]=s,s.__data__=i[a],c.delete(u)):n[a]=new _v(e,i[a]);for(a=0;a<d;++a)(s=t[a])&&c.get(p[a])===s&&(r[a]=s)}function kv(e){return e.__data__}function Cv(e){return"object"==typeof e&&"length"in e?e:Array.from(e)}function Ov(e,t){return e<t?-1:e>t?1:e>=t?0:NaN}function Nv(e){return function(){this.removeAttribute(e)}}function Mv(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Pv(e,t){return function(){this.setAttribute(e,t)}}function Tv(e,t){return function(){this.setAttributeNS(e.space,e.local,t)}}function Av(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttribute(e):this.setAttribute(e,n)}}function Dv(e,t){return function(){var n=t.apply(this,arguments);null==n?this.removeAttributeNS(e.space,e.local):this.setAttributeNS(e.space,e.local,n)}}function Iv(e){return e.ownerDocument&&e.ownerDocument.defaultView||e.document&&e||e.defaultView}function $v(e){return function(){this.style.removeProperty(e)}}function zv(e,t,n){return function(){this.style.setProperty(e,t,n)}}function jv(e,t,n){return function(){var o=t.apply(this,arguments);null==o?this.style.removeProperty(e):this.style.setProperty(e,o,n)}}function Bv(e,t){return e.style.getPropertyValue(t)||Iv(e).getComputedStyle(e,null).getPropertyValue(t)}function Lv(e){return function(){delete this[e]}}function Rv(e,t){return function(){this[e]=t}}function Fv(e,t){return function(){var n=t.apply(this,arguments);null==n?delete this[e]:this[e]=n}}function Vv(e){return e.trim().split(/^|\s+/)}function Hv(e){return e.classList||new Uv(e)}function Uv(e){this._node=e,this._names=Vv(e.getAttribute("class")||"")}function Yv(e,t){for(var n=Hv(e),o=-1,r=t.length;++o<r;)n.add(t[o])}function Xv(e,t){for(var n=Hv(e),o=-1,r=t.length;++o<r;)n.remove(t[o])}function Gv(e){return function(){Yv(this,e)}}function qv(e){return function(){Xv(this,e)}}function Wv(e,t){return function(){(t.apply(this,arguments)?Yv:Xv)(this,e)}}function Zv(){this.textContent=""}function Kv(e){return function(){this.textContent=e}}function Jv(e){return function(){var t=e.apply(this,arguments);this.textContent=null==t?"":t}}function Qv(){this.innerHTML=""}function eh(e){return function(){this.innerHTML=e}}function th(e){return function(){var t=e.apply(this,arguments);this.innerHTML=null==t?"":t}}function nh(){this.nextSibling&&this.parentNode.appendChild(this)}function oh(){this.previousSibling&&this.parentNode.insertBefore(this,this.parentNode.firstChild)}function rh(){return null}function ih(){var e=this.parentNode;e&&e.removeChild(this)}function lh(){var e=this.cloneNode(!1),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function ah(){var e=this.cloneNode(!0),t=this.parentNode;return t?t.insertBefore(e,this.nextSibling):e}function sh(e){return function(){var t=this.__on;if(t){for(var n,o=0,r=-1,i=t.length;o<i;++o)n=t[o],e.type&&n.type!==e.type||n.name!==e.name?t[++r]=n:this.removeEventListener(n.type,n.listener,n.options);++r?t.length=r:delete this.__on}}}function uh(e,t,n){return function(){var o,r=this.__on,i=function(e){return function(t){e.call(this,t,this.__data__)}}(t);if(r)for(var l=0,a=r.length;l<a;++l)if((o=r[l]).type===e.type&&o.name===e.name)return this.removeEventListener(o.type,o.listener,o.options),this.addEventListener(o.type,o.listener=i,o.options=n),void(o.value=t);this.addEventListener(e.type,i,n),o={type:e.type,name:e.name,value:t,listener:i,options:n},r?r.push(o):this.__on=[o]}}function ch(e,t,n){var o=Iv(e),r=o.CustomEvent;"function"==typeof r?r=new r(t,n):(r=o.document.createEvent("Event"),n?(r.initEvent(t,n.bubbles,n.cancelable),r.detail=n.detail):r.initEvent(t,!1,!1)),e.dispatchEvent(r)}function dh(e,t){return function(){return ch(this,e,t)}}function fh(e,t){return function(){return ch(this,e,t.apply(this,arguments))}}_v.prototype={constructor:_v,appendChild:function(e){return this._parent.insertBefore(e,this._next)},insertBefore:function(e,t){return this._parent.insertBefore(e,t)},querySelector:function(e){return this._parent.querySelector(e)},querySelectorAll:function(e){return this._parent.querySelectorAll(e)}},Uv.prototype={add:function(e){this._names.indexOf(e)<0&&(this._names.push(e),this._node.setAttribute("class",this._names.join(" ")))},remove:function(e){var t=this._names.indexOf(e);t>=0&&(this._names.splice(t,1),this._node.setAttribute("class",this._names.join(" ")))},contains:function(e){return this._names.indexOf(e)>=0}};var ph=[null];function vh(e,t){this._groups=e,this._parents=t}function hh(){return new vh([[document.documentElement]],ph)}function gh(e){return"string"==typeof e?new vh([[document.querySelector(e)]],[document.documentElement]):new vh([[e]],ph)}function mh(e,t){if(e=function(e){let t;for(;t=e.sourceEvent;)e=t;return e}(e),void 0===t&&(t=e.currentTarget),t){var n=t.ownerSVGElement||t;if(n.createSVGPoint){var o=n.createSVGPoint();return o.x=e.clientX,o.y=e.clientY,[(o=o.matrixTransform(t.getScreenCTM().inverse())).x,o.y]}if(t.getBoundingClientRect){var r=t.getBoundingClientRect();return[e.clientX-r.left-t.clientLeft,e.clientY-r.top-t.clientTop]}}return[e.pageX,e.pageY]}vh.prototype=hh.prototype={constructor:vh,select:function(e){"function"!=typeof e&&(e=fv(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,l,a=t[r],s=a.length,u=o[r]=new Array(s),c=0;c<s;++c)(i=a[c])&&(l=e.call(i,i.__data__,c,a))&&("__data__"in i&&(l.__data__=i.__data__),u[c]=l);return new vh(o,this._parents)},selectAll:function(e){e="function"==typeof e?function(e){return function(){return function(e){return null==e?[]:Array.isArray(e)?e:Array.from(e)}(e.apply(this,arguments))}}(e):vv(e);for(var t=this._groups,n=t.length,o=[],r=[],i=0;i<n;++i)for(var l,a=t[i],s=a.length,u=0;u<s;++u)(l=a[u])&&(o.push(e.call(l,l.__data__,u,a)),r.push(l));return new vh(o,r)},selectChild:function(e){return this.select(null==e?yv:function(e){return function(){return mv.call(this.children,e)}}("function"==typeof e?e:gv(e)))},selectChildren:function(e){return this.selectAll(null==e?wv:function(e){return function(){return bv.call(this.children,e)}}("function"==typeof e?e:gv(e)))},filter:function(e){"function"!=typeof e&&(e=hv(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,l=t[r],a=l.length,s=o[r]=[],u=0;u<a;++u)(i=l[u])&&e.call(i,i.__data__,u,l)&&s.push(i);return new vh(o,this._parents)},data:function(e,t){if(!arguments.length)return Array.from(this,kv);var n=t?Ev:Sv,o=this._parents,r=this._groups;"function"!=typeof e&&(e=function(e){return function(){return e}}(e));for(var i=r.length,l=new Array(i),a=new Array(i),s=new Array(i),u=0;u<i;++u){var c=o[u],d=r[u],f=d.length,p=Cv(e.call(c,c&&c.__data__,u,o)),v=p.length,h=a[u]=new Array(v),g=l[u]=new Array(v);n(c,d,h,g,s[u]=new Array(f),p,t);for(var m,y,b=0,w=0;b<v;++b)if(m=h[b]){for(b>=w&&(w=b+1);!(y=g[w])&&++w<v;);m._next=y||null}}return(l=new vh(l,o))._enter=a,l._exit=s,l},enter:function(){return new vh(this._enter||this._groups.map(xv),this._parents)},exit:function(){return new vh(this._exit||this._groups.map(xv),this._parents)},join:function(e,t,n){var o=this.enter(),r=this,i=this.exit();return"function"==typeof e?(o=e(o))&&(o=o.selection()):o=o.append(e+""),null!=t&&(r=t(r))&&(r=r.selection()),null==n?i.remove():n(i),o&&r?o.merge(r).order():r},merge:function(e){for(var t=e.selection?e.selection():e,n=this._groups,o=t._groups,r=n.length,i=o.length,l=Math.min(r,i),a=new Array(r),s=0;s<l;++s)for(var u,c=n[s],d=o[s],f=c.length,p=a[s]=new Array(f),v=0;v<f;++v)(u=c[v]||d[v])&&(p[v]=u);for(;s<r;++s)a[s]=n[s];return new vh(a,this._parents)},selection:function(){return this},order:function(){for(var e=this._groups,t=-1,n=e.length;++t<n;)for(var o,r=e[t],i=r.length-1,l=r[i];--i>=0;)(o=r[i])&&(l&&4^o.compareDocumentPosition(l)&&l.parentNode.insertBefore(o,l),l=o);return this},sort:function(e){function t(t,n){return t&&n?e(t.__data__,n.__data__):!t-!n}e||(e=Ov);for(var n=this._groups,o=n.length,r=new Array(o),i=0;i<o;++i){for(var l,a=n[i],s=a.length,u=r[i]=new Array(s),c=0;c<s;++c)(l=a[c])&&(u[c]=l);u.sort(t)}return new vh(r,this._parents).order()},call:function(){var e=arguments[0];return arguments[0]=this,e.apply(null,arguments),this},nodes:function(){return Array.from(this)},node:function(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o=e[t],r=0,i=o.length;r<i;++r){var l=o[r];if(l)return l}return null},size:function(){let e=0;for(const t of this)++e;return e},empty:function(){return!this.node()},each:function(e){for(var t=this._groups,n=0,o=t.length;n<o;++n)for(var r,i=t[n],l=0,a=i.length;l<a;++l)(r=i[l])&&e.call(r,r.__data__,l,i);return this},attr:function(e,t){var n=av(e);if(arguments.length<2){var o=this.node();return n.local?o.getAttributeNS(n.space,n.local):o.getAttribute(n)}return this.each((null==t?n.local?Mv:Nv:"function"==typeof t?n.local?Dv:Av:n.local?Tv:Pv)(n,t))},style:function(e,t,n){return arguments.length>1?this.each((null==t?$v:"function"==typeof t?jv:zv)(e,t,null==n?"":n)):Bv(this.node(),e)},property:function(e,t){return arguments.length>1?this.each((null==t?Lv:"function"==typeof t?Fv:Rv)(e,t)):this.node()[e]},classed:function(e,t){var n=Vv(e+"");if(arguments.length<2){for(var o=Hv(this.node()),r=-1,i=n.length;++r<i;)if(!o.contains(n[r]))return!1;return!0}return this.each(("function"==typeof t?Wv:t?Gv:qv)(n,t))},text:function(e){return arguments.length?this.each(null==e?Zv:("function"==typeof e?Jv:Kv)(e)):this.node().textContent},html:function(e){return arguments.length?this.each(null==e?Qv:("function"==typeof e?th:eh)(e)):this.node().innerHTML},raise:function(){return this.each(nh)},lower:function(){return this.each(oh)},append:function(e){var t="function"==typeof e?e:cv(e);return this.select((function(){return this.appendChild(t.apply(this,arguments))}))},insert:function(e,t){var n="function"==typeof e?e:cv(e),o=null==t?rh:"function"==typeof t?t:fv(t);return this.select((function(){return this.insertBefore(n.apply(this,arguments),o.apply(this,arguments)||null)}))},remove:function(){return this.each(ih)},clone:function(e){return this.select(e?ah:lh)},datum:function(e){return arguments.length?this.property("__data__",e):this.node().__data__},on:function(e,t,n){var o,r,i=function(e){return e.trim().split(/^|\s+/).map((function(e){var t="",n=e.indexOf(".");return n>=0&&(t=e.slice(n+1),e=e.slice(0,n)),{type:e,name:t}}))}(e+""),l=i.length;if(!(arguments.length<2)){for(a=t?uh:sh,o=0;o<l;++o)this.each(a(i[o],t,n));return this}var a=this.node().__on;if(a)for(var s,u=0,c=a.length;u<c;++u)for(o=0,s=a[u];o<l;++o)if((r=i[o]).type===s.type&&r.name===s.name)return s.value},dispatch:function(e,t){return this.each(("function"==typeof t?fh:dh)(e,t))},[Symbol.iterator]:function*(){for(var e=this._groups,t=0,n=e.length;t<n;++t)for(var o,r=e[t],i=0,l=r.length;i<l;++i)(o=r[i])&&(yield o)}};const yh={capture:!0,passive:!1};function bh(e){e.preventDefault(),e.stopImmediatePropagation()}function wh(e,t,n){e.prototype=t.prototype=n,n.constructor=e}function xh(e,t){var n=Object.create(e.prototype);for(var o in t)n[o]=t[o];return n}function _h(){}var Sh=.7,Eh=1.4285714285714286,kh="\\s*([+-]?\\d+)\\s*",Ch="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)\\s*",Oh="\\s*([+-]?(?:\\d*\\.)?\\d+(?:[eE][+-]?\\d+)?)%\\s*",Nh=/^#([0-9a-f]{3,8})$/,Mh=new RegExp(`^rgb\\(${kh},${kh},${kh}\\)$`),Ph=new RegExp(`^rgb\\(${Oh},${Oh},${Oh}\\)$`),Th=new RegExp(`^rgba\\(${kh},${kh},${kh},${Ch}\\)$`),Ah=new RegExp(`^rgba\\(${Oh},${Oh},${Oh},${Ch}\\)$`),Dh=new RegExp(`^hsl\\(${Ch},${Oh},${Oh}\\)$`),Ih=new RegExp(`^hsla\\(${Ch},${Oh},${Oh},${Ch}\\)$`),$h={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};function zh(){return this.rgb().formatHex()}function jh(){return this.rgb().formatRgb()}function Bh(e){var t,n;return e=(e+"").trim().toLowerCase(),(t=Nh.exec(e))?(n=t[1].length,t=parseInt(t[1],16),6===n?Lh(t):3===n?new Vh(t>>8&15|t>>4&240,t>>4&15|240&t,(15&t)<<4|15&t,1):8===n?Rh(t>>24&255,t>>16&255,t>>8&255,(255&t)/255):4===n?Rh(t>>12&15|t>>8&240,t>>8&15|t>>4&240,t>>4&15|240&t,((15&t)<<4|15&t)/255):null):(t=Mh.exec(e))?new Vh(t[1],t[2],t[3],1):(t=Ph.exec(e))?new Vh(255*t[1]/100,255*t[2]/100,255*t[3]/100,1):(t=Th.exec(e))?Rh(t[1],t[2],t[3],t[4]):(t=Ah.exec(e))?Rh(255*t[1]/100,255*t[2]/100,255*t[3]/100,t[4]):(t=Dh.exec(e))?qh(t[1],t[2]/100,t[3]/100,1):(t=Ih.exec(e))?qh(t[1],t[2]/100,t[3]/100,t[4]):$h.hasOwnProperty(e)?Lh($h[e]):"transparent"===e?new Vh(NaN,NaN,NaN,0):null}function Lh(e){return new Vh(e>>16&255,e>>8&255,255&e,1)}function Rh(e,t,n,o){return o<=0&&(e=t=n=NaN),new Vh(e,t,n,o)}function Fh(e,t,n,o){return 1===arguments.length?function(e){return e instanceof _h||(e=Bh(e)),e?new Vh((e=e.rgb()).r,e.g,e.b,e.opacity):new Vh}(e):new Vh(e,t,n,null==o?1:o)}function Vh(e,t,n,o){this.r=+e,this.g=+t,this.b=+n,this.opacity=+o}function Hh(){return`#${Gh(this.r)}${Gh(this.g)}${Gh(this.b)}`}function Uh(){const e=Yh(this.opacity);return`${1===e?"rgb(":"rgba("}${Xh(this.r)}, ${Xh(this.g)}, ${Xh(this.b)}${1===e?")":`, ${e})`}`}function Yh(e){return isNaN(e)?1:Math.max(0,Math.min(1,e))}function Xh(e){return Math.max(0,Math.min(255,Math.round(e)||0))}function Gh(e){return((e=Xh(e))<16?"0":"")+e.toString(16)}function qh(e,t,n,o){return o<=0?e=t=n=NaN:n<=0||n>=1?e=t=NaN:t<=0&&(e=NaN),new Zh(e,t,n,o)}function Wh(e){if(e instanceof Zh)return new Zh(e.h,e.s,e.l,e.opacity);if(e instanceof _h||(e=Bh(e)),!e)return new Zh;if(e instanceof Zh)return e;var t=(e=e.rgb()).r/255,n=e.g/255,o=e.b/255,r=Math.min(t,n,o),i=Math.max(t,n,o),l=NaN,a=i-r,s=(i+r)/2;return a?(l=t===i?(n-o)/a+6*(n<o):n===i?(o-t)/a+2:(t-n)/a+4,a/=s<.5?i+r:2-i-r,l*=60):a=s>0&&s<1?0:l,new Zh(l,a,s,e.opacity)}function Zh(e,t,n,o){this.h=+e,this.s=+t,this.l=+n,this.opacity=+o}function Kh(e){return(e=(e||0)%360)<0?e+360:e}function Jh(e){return Math.max(0,Math.min(1,e||0))}function Qh(e,t,n){return 255*(e<60?t+(n-t)*e/60:e<180?n:e<240?t+(n-t)*(240-e)/60:t)}wh(_h,Bh,{copy(e){return Object.assign(new this.constructor,this,e)},displayable(){return this.rgb().displayable()},hex:zh,formatHex:zh,formatHex8:function(){return this.rgb().formatHex8()},formatHsl:function(){return Wh(this).formatHsl()},formatRgb:jh,toString:jh}),wh(Vh,Fh,xh(_h,{brighter(e){return e=null==e?Eh:Math.pow(Eh,e),new Vh(this.r*e,this.g*e,this.b*e,this.opacity)},darker(e){return e=null==e?Sh:Math.pow(Sh,e),new Vh(this.r*e,this.g*e,this.b*e,this.opacity)},rgb(){return this},clamp(){return new Vh(Xh(this.r),Xh(this.g),Xh(this.b),Yh(this.opacity))},displayable(){return-.5<=this.r&&this.r<255.5&&-.5<=this.g&&this.g<255.5&&-.5<=this.b&&this.b<255.5&&0<=this.opacity&&this.opacity<=1},hex:Hh,formatHex:Hh,formatHex8:function(){return`#${Gh(this.r)}${Gh(this.g)}${Gh(this.b)}${Gh(255*(isNaN(this.opacity)?1:this.opacity))}`},formatRgb:Uh,toString:Uh})),wh(Zh,(function(e,t,n,o){return 1===arguments.length?Wh(e):new Zh(e,t,n,null==o?1:o)}),xh(_h,{brighter(e){return e=null==e?Eh:Math.pow(Eh,e),new Zh(this.h,this.s,this.l*e,this.opacity)},darker(e){return e=null==e?Sh:Math.pow(Sh,e),new Zh(this.h,this.s,this.l*e,this.opacity)},rgb(){var e=this.h%360+360*(this.h<0),t=isNaN(e)||isNaN(this.s)?0:this.s,n=this.l,o=n+(n<.5?n:1-n)*t,r=2*n-o;return new Vh(Qh(e>=240?e-240:e+120,r,o),Qh(e,r,o),Qh(e<120?e+240:e-120,r,o),this.opacity)},clamp(){return new Zh(Kh(this.h),Jh(this.s),Jh(this.l),Yh(this.opacity))},displayable(){return(0<=this.s&&this.s<=1||isNaN(this.s))&&0<=this.l&&this.l<=1&&0<=this.opacity&&this.opacity<=1},formatHsl(){const e=Yh(this.opacity);return`${1===e?"hsl(":"hsla("}${Kh(this.h)}, ${100*Jh(this.s)}%, ${100*Jh(this.l)}%${1===e?")":`, ${e})`}`}}));const eg=e=>()=>e;function tg(e,t){var n=t-e;return n?function(e,t){return function(n){return e+n*t}}(e,n):eg(isNaN(e)?t:e)}const ng=function e(t){var n=function(e){return 1==(e=+e)?tg:function(t,n){return n-t?function(e,t,n){return e=Math.pow(e,n),t=Math.pow(t,n)-e,n=1/n,function(o){return Math.pow(e+o*t,n)}}(t,n,e):eg(isNaN(t)?n:t)}}(t);function o(e,t){var o=n((e=Fh(e)).r,(t=Fh(t)).r),r=n(e.g,t.g),i=n(e.b,t.b),l=tg(e.opacity,t.opacity);return function(t){return e.r=o(t),e.g=r(t),e.b=i(t),e.opacity=l(t),e+""}}return o.gamma=e,o}(1);function og(e,t){return e=+e,t=+t,function(n){return e*(1-n)+t*n}}var rg=/[-+]?(?:\d+\.?\d*|\.?\d+)(?:[eE][-+]?\d+)?/g,ig=new RegExp(rg.source,"g");function lg(e,t){var n,o,r,i=rg.lastIndex=ig.lastIndex=0,l=-1,a=[],s=[];for(e+="",t+="";(n=rg.exec(e))&&(o=ig.exec(t));)(r=o.index)>i&&(r=t.slice(i,r),a[l]?a[l]+=r:a[++l]=r),(n=n[0])===(o=o[0])?a[l]?a[l]+=o:a[++l]=o:(a[++l]=null,s.push({i:l,x:og(n,o)})),i=ig.lastIndex;return i<t.length&&(r=t.slice(i),a[l]?a[l]+=r:a[++l]=r),a.length<2?s[0]?function(e){return function(t){return e(t)+""}}(s[0].x):function(e){return function(){return e}}(t):(t=s.length,function(e){for(var n,o=0;o<t;++o)a[(n=s[o]).i]=n.x(e);return a.join("")})}var ag,sg=180/Math.PI,ug={translateX:0,translateY:0,rotate:0,skewX:0,scaleX:1,scaleY:1};function cg(e,t,n,o,r,i){var l,a,s;return(l=Math.sqrt(e*e+t*t))&&(e/=l,t/=l),(s=e*n+t*o)&&(n-=e*s,o-=t*s),(a=Math.sqrt(n*n+o*o))&&(n/=a,o/=a,s/=a),e*o<t*n&&(e=-e,t=-t,s=-s,l=-l),{translateX:r,translateY:i,rotate:Math.atan2(t,e)*sg,skewX:Math.atan(s)*sg,scaleX:l,scaleY:a}}function dg(e,t,n,o){function r(e){return e.length?e.pop()+" ":""}return function(i,l){var a=[],s=[];return i=e(i),l=e(l),function(e,o,r,i,l,a){if(e!==r||o!==i){var s=l.push("translate(",null,t,null,n);a.push({i:s-4,x:og(e,r)},{i:s-2,x:og(o,i)})}else(r||i)&&l.push("translate("+r+t+i+n)}(i.translateX,i.translateY,l.translateX,l.translateY,a,s),function(e,t,n,i){e!==t?(e-t>180?t+=360:t-e>180&&(e+=360),i.push({i:n.push(r(n)+"rotate(",null,o)-2,x:og(e,t)})):t&&n.push(r(n)+"rotate("+t+o)}(i.rotate,l.rotate,a,s),function(e,t,n,i){e!==t?i.push({i:n.push(r(n)+"skewX(",null,o)-2,x:og(e,t)}):t&&n.push(r(n)+"skewX("+t+o)}(i.skewX,l.skewX,a,s),function(e,t,n,o,i,l){if(e!==n||t!==o){var a=i.push(r(i)+"scale(",null,",",null,")");l.push({i:a-4,x:og(e,n)},{i:a-2,x:og(t,o)})}else 1===n&&1===o||i.push(r(i)+"scale("+n+","+o+")")}(i.scaleX,i.scaleY,l.scaleX,l.scaleY,a,s),i=l=null,function(e){for(var t,n=-1,o=s.length;++n<o;)a[(t=s[n]).i]=t.x(e);return a.join("")}}}var fg=dg((function(e){const t=new("function"==typeof DOMMatrix?DOMMatrix:WebKitCSSMatrix)(e+"");return t.isIdentity?ug:cg(t.a,t.b,t.c,t.d,t.e,t.f)}),"px, ","px)","deg)"),pg=dg((function(e){return null==e?ug:(ag||(ag=document.createElementNS("http://www.w3.org/2000/svg","g")),ag.setAttribute("transform",e),(e=ag.transform.baseVal.consolidate())?cg((e=e.matrix).a,e.b,e.c,e.d,e.e,e.f):ug)}),", ",")",")");function vg(e){return((e=Math.exp(e))+1/e)/2}const hg=function e(t,n,o){function r(e,r){var i,l,a=e[0],s=e[1],u=e[2],c=r[0],d=r[1],f=r[2],p=c-a,v=d-s,h=p*p+v*v;if(h<1e-12)l=Math.log(f/u)/t,i=function(e){return[a+e*p,s+e*v,u*Math.exp(t*e*l)]};else{var g=Math.sqrt(h),m=(f*f-u*u+o*h)/(2*u*n*g),y=(f*f-u*u-o*h)/(2*f*n*g),b=Math.log(Math.sqrt(m*m+1)-m),w=Math.log(Math.sqrt(y*y+1)-y);l=(w-b)/t,i=function(e){var o=e*l,r=vg(b),i=u/(n*g)*(r*function(e){return((e=Math.exp(2*e))-1)/(e+1)}(t*o+b)-function(e){return((e=Math.exp(e))-1/e)/2}(b));return[a+i*p,s+i*v,u*r/vg(t*o+b)]}}return i.duration=1e3*l*t/Math.SQRT2,i}return r.rho=function(t){var n=Math.max(.001,+t),o=n*n;return e(n,o,o*o)},r}(Math.SQRT2,2,4);var gg,mg,yg=0,bg=0,wg=0,xg=1e3,_g=0,Sg=0,Eg=0,kg="object"==typeof performance&&performance.now?performance:Date,Cg="object"==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame.bind(window):function(e){setTimeout(e,17)};function Og(){return Sg||(Cg(Ng),Sg=kg.now()+Eg)}function Ng(){Sg=0}function Mg(){this._call=this._time=this._next=null}function Pg(e,t,n){var o=new Mg;return o.restart(e,t,n),o}function Tg(){Sg=(_g=kg.now())+Eg,yg=bg=0;try{!function(){Og(),++yg;for(var e,t=gg;t;)(e=Sg-t._time)>=0&&t._call.call(void 0,e),t=t._next;--yg}()}finally{yg=0,function(){for(var e,t,n=gg,o=1/0;n;)n._call?(o>n._time&&(o=n._time),e=n,n=n._next):(t=n._next,n._next=null,n=e?e._next=t:gg=t);mg=e,Dg(o)}(),Sg=0}}function Ag(){var e=kg.now(),t=e-_g;t>xg&&(Eg-=t,_g=e)}function Dg(e){yg||(bg&&(bg=clearTimeout(bg)),e-Sg>24?(e<1/0&&(bg=setTimeout(Tg,e-kg.now()-Eg)),wg&&(wg=clearInterval(wg))):(wg||(_g=kg.now(),wg=setInterval(Ag,xg)),yg=1,Cg(Tg)))}function Ig(e,t,n){var o=new Mg;return t=null==t?0:+t,o.restart((n=>{o.stop(),e(n+t)}),t,n),o}Mg.prototype=Pg.prototype={constructor:Mg,restart:function(e,t,n){if("function"!=typeof e)throw new TypeError("callback is not a function");n=(null==n?Og():+n)+(null==t?0:+t),this._next||mg===this||(mg?mg._next=this:gg=this,mg=this),this._call=e,this._time=n,Dg()},stop:function(){this._call&&(this._call=null,this._time=1/0,Dg())}};var $g=tv("start","end","cancel","interrupt"),zg=[],jg=0,Bg=2,Lg=3,Rg=5,Fg=6;function Vg(e,t,n,o,r,i){var l=e.__transition;if(l){if(n in l)return}else e.__transition={};!function(e,t,n){var o,r=e.__transition;function i(s){var u,c,d,f;if(1!==n.state)return a();for(u in r)if((f=r[u]).name===n.name){if(f.state===Lg)return Ig(i);4===f.state?(f.state=Fg,f.timer.stop(),f.on.call("interrupt",e,e.__data__,f.index,f.group),delete r[u]):+u<t&&(f.state=Fg,f.timer.stop(),f.on.call("cancel",e,e.__data__,f.index,f.group),delete r[u])}if(Ig((function(){n.state===Lg&&(n.state=4,n.timer.restart(l,n.delay,n.time),l(s))})),n.state=Bg,n.on.call("start",e,e.__data__,n.index,n.group),n.state===Bg){for(n.state=Lg,o=new Array(d=n.tween.length),u=0,c=-1;u<d;++u)(f=n.tween[u].value.call(e,e.__data__,n.index,n.group))&&(o[++c]=f);o.length=c+1}}function l(t){for(var r=t<n.duration?n.ease.call(null,t/n.duration):(n.timer.restart(a),n.state=Rg,1),i=-1,l=o.length;++i<l;)o[i].call(e,r);n.state===Rg&&(n.on.call("end",e,e.__data__,n.index,n.group),a())}function a(){for(var o in n.state=Fg,n.timer.stop(),delete r[t],r)return;delete e.__transition}r[t]=n,n.timer=Pg((function(e){n.state=1,n.timer.restart(i,n.delay,n.time),n.delay<=e&&i(e-n.delay)}),0,n.time)}(e,n,{name:t,index:o,group:r,on:$g,tween:zg,time:i.time,delay:i.delay,duration:i.duration,ease:i.ease,timer:null,state:jg})}function Hg(e,t){var n=Yg(e,t);if(n.state>jg)throw new Error("too late; already scheduled");return n}function Ug(e,t){var n=Yg(e,t);if(n.state>Lg)throw new Error("too late; already running");return n}function Yg(e,t){var n=e.__transition;if(!n||!(n=n[t]))throw new Error("transition not found");return n}function Xg(e,t){var n,o,r,i=e.__transition,l=!0;if(i){for(r in t=null==t?null:t+"",i)(n=i[r]).name===t?(o=n.state>Bg&&n.state<Rg,n.state=Fg,n.timer.stop(),n.on.call(o?"interrupt":"cancel",e,e.__data__,n.index,n.group),delete i[r]):l=!1;l&&delete e.__transition}}function Gg(e,t){var n,o;return function(){var r=Ug(this,e),i=r.tween;if(i!==n)for(var l=0,a=(o=n=i).length;l<a;++l)if(o[l].name===t){(o=o.slice()).splice(l,1);break}r.tween=o}}function qg(e,t,n){var o,r;if("function"!=typeof n)throw new Error;return function(){var i=Ug(this,e),l=i.tween;if(l!==o){r=(o=l).slice();for(var a={name:t,value:n},s=0,u=r.length;s<u;++s)if(r[s].name===t){r[s]=a;break}s===u&&r.push(a)}i.tween=r}}function Wg(e,t,n){var o=e._id;return e.each((function(){var e=Ug(this,o);(e.value||(e.value={}))[t]=n.apply(this,arguments)})),function(e){return Yg(e,o).value[t]}}function Zg(e,t){var n;return("number"==typeof t?og:t instanceof Bh?ng:(n=Bh(t))?(t=n,ng):lg)(e,t)}function Kg(e){return function(){this.removeAttribute(e)}}function Jg(e){return function(){this.removeAttributeNS(e.space,e.local)}}function Qg(e,t,n){var o,r,i=n+"";return function(){var l=this.getAttribute(e);return l===i?null:l===o?r:r=t(o=l,n)}}function em(e,t,n){var o,r,i=n+"";return function(){var l=this.getAttributeNS(e.space,e.local);return l===i?null:l===o?r:r=t(o=l,n)}}function tm(e,t,n){var o,r,i;return function(){var l,a,s=n(this);if(null!=s)return(l=this.getAttribute(e))===(a=s+"")?null:l===o&&a===r?i:(r=a,i=t(o=l,s));this.removeAttribute(e)}}function nm(e,t,n){var o,r,i;return function(){var l,a,s=n(this);if(null!=s)return(l=this.getAttributeNS(e.space,e.local))===(a=s+"")?null:l===o&&a===r?i:(r=a,i=t(o=l,s));this.removeAttributeNS(e.space,e.local)}}function om(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&function(e,t){return function(n){this.setAttributeNS(e.space,e.local,t.call(this,n))}}(e,r)),n}return r._value=t,r}function rm(e,t){var n,o;function r(){var r=t.apply(this,arguments);return r!==o&&(n=(o=r)&&function(e,t){return function(n){this.setAttribute(e,t.call(this,n))}}(e,r)),n}return r._value=t,r}function im(e,t){return function(){Hg(this,e).delay=+t.apply(this,arguments)}}function lm(e,t){return t=+t,function(){Hg(this,e).delay=t}}function am(e,t){return function(){Ug(this,e).duration=+t.apply(this,arguments)}}function sm(e,t){return t=+t,function(){Ug(this,e).duration=t}}var um=hh.prototype.constructor;function cm(e){return function(){this.style.removeProperty(e)}}var dm=0;function fm(e,t,n,o){this._groups=e,this._parents=t,this._name=n,this._id=o}function pm(){return++dm}var vm=hh.prototype;fm.prototype={constructor:fm,select:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=fv(e));for(var o=this._groups,r=o.length,i=new Array(r),l=0;l<r;++l)for(var a,s,u=o[l],c=u.length,d=i[l]=new Array(c),f=0;f<c;++f)(a=u[f])&&(s=e.call(a,a.__data__,f,u))&&("__data__"in a&&(s.__data__=a.__data__),d[f]=s,Vg(d[f],t,n,f,d,Yg(a,n)));return new fm(i,this._parents,t,n)},selectAll:function(e){var t=this._name,n=this._id;"function"!=typeof e&&(e=vv(e));for(var o=this._groups,r=o.length,i=[],l=[],a=0;a<r;++a)for(var s,u=o[a],c=u.length,d=0;d<c;++d)if(s=u[d]){for(var f,p=e.call(s,s.__data__,d,u),v=Yg(s,n),h=0,g=p.length;h<g;++h)(f=p[h])&&Vg(f,t,n,h,p,v);i.push(p),l.push(s)}return new fm(i,l,t,n)},selectChild:vm.selectChild,selectChildren:vm.selectChildren,filter:function(e){"function"!=typeof e&&(e=hv(e));for(var t=this._groups,n=t.length,o=new Array(n),r=0;r<n;++r)for(var i,l=t[r],a=l.length,s=o[r]=[],u=0;u<a;++u)(i=l[u])&&e.call(i,i.__data__,u,l)&&s.push(i);return new fm(o,this._parents,this._name,this._id)},merge:function(e){if(e._id!==this._id)throw new Error;for(var t=this._groups,n=e._groups,o=t.length,r=n.length,i=Math.min(o,r),l=new Array(o),a=0;a<i;++a)for(var s,u=t[a],c=n[a],d=u.length,f=l[a]=new Array(d),p=0;p<d;++p)(s=u[p]||c[p])&&(f[p]=s);for(;a<o;++a)l[a]=t[a];return new fm(l,this._parents,this._name,this._id)},selection:function(){return new um(this._groups,this._parents)},transition:function(){for(var e=this._name,t=this._id,n=pm(),o=this._groups,r=o.length,i=0;i<r;++i)for(var l,a=o[i],s=a.length,u=0;u<s;++u)if(l=a[u]){var c=Yg(l,t);Vg(l,e,n,u,a,{time:c.time+c.delay+c.duration,delay:0,duration:c.duration,ease:c.ease})}return new fm(o,this._parents,e,n)},call:vm.call,nodes:vm.nodes,node:vm.node,size:vm.size,empty:vm.empty,each:vm.each,on:function(e,t){var n=this._id;return arguments.length<2?Yg(this.node(),n).on.on(e):this.each(function(e,t,n){var o,r,i=function(e){return(e+"").trim().split(/^|\s+/).every((function(e){var t=e.indexOf(".");return t>=0&&(e=e.slice(0,t)),!e||"start"===e}))}(t)?Hg:Ug;return function(){var l=i(this,e),a=l.on;a!==o&&(r=(o=a).copy()).on(t,n),l.on=r}}(n,e,t))},attr:function(e,t){var n=av(e),o="transform"===n?pg:Zg;return this.attrTween(e,"function"==typeof t?(n.local?nm:tm)(n,o,Wg(this,"attr."+e,t)):null==t?(n.local?Jg:Kg)(n):(n.local?em:Qg)(n,o,t))},attrTween:function(e,t){var n="attr."+e;if(arguments.length<2)return(n=this.tween(n))&&n._value;if(null==t)return this.tween(n,null);if("function"!=typeof t)throw new Error;var o=av(e);return this.tween(n,(o.local?om:rm)(o,t))},style:function(e,t,n){var o="transform"==(e+="")?fg:Zg;return null==t?this.styleTween(e,function(e,t){var n,o,r;return function(){var i=Bv(this,e),l=(this.style.removeProperty(e),Bv(this,e));return i===l?null:i===n&&l===o?r:r=t(n=i,o=l)}}(e,o)).on("end.style."+e,cm(e)):"function"==typeof t?this.styleTween(e,function(e,t,n){var o,r,i;return function(){var l=Bv(this,e),a=n(this),s=a+"";return null==a&&(this.style.removeProperty(e),s=a=Bv(this,e)),l===s?null:l===o&&s===r?i:(r=s,i=t(o=l,a))}}(e,o,Wg(this,"style."+e,t))).each(function(e,t){var n,o,r,i,l="style."+t,a="end."+l;return function(){var s=Ug(this,e),u=s.on,c=null==s.value[l]?i||(i=cm(t)):void 0;u===n&&r===c||(o=(n=u).copy()).on(a,r=c),s.on=o}}(this._id,e)):this.styleTween(e,function(e,t,n){var o,r,i=n+"";return function(){var l=Bv(this,e);return l===i?null:l===o?r:r=t(o=l,n)}}(e,o,t),n).on("end.style."+e,null)},styleTween:function(e,t,n){var o="style."+(e+="");if(arguments.length<2)return(o=this.tween(o))&&o._value;if(null==t)return this.tween(o,null);if("function"!=typeof t)throw new Error;return this.tween(o,function(e,t,n){var o,r;function i(){var i=t.apply(this,arguments);return i!==r&&(o=(r=i)&&function(e,t,n){return function(o){this.style.setProperty(e,t.call(this,o),n)}}(e,i,n)),o}return i._value=t,i}(e,t,null==n?"":n))},text:function(e){return this.tween("text","function"==typeof e?function(e){return function(){var t=e(this);this.textContent=null==t?"":t}}(Wg(this,"text",e)):function(e){return function(){this.textContent=e}}(null==e?"":e+""))},textTween:function(e){var t="text";if(arguments.length<1)return(t=this.tween(t))&&t._value;if(null==e)return this.tween(t,null);if("function"!=typeof e)throw new Error;return this.tween(t,function(e){var t,n;function o(){var o=e.apply(this,arguments);return o!==n&&(t=(n=o)&&function(e){return function(t){this.textContent=e.call(this,t)}}(o)),t}return o._value=e,o}(e))},remove:function(){return this.on("end.remove",(e=this._id,function(){var t=this.parentNode;for(var n in this.__transition)if(+n!==e)return;t&&t.removeChild(this)}));var e},tween:function(e,t){var n=this._id;if(e+="",arguments.length<2){for(var o,r=Yg(this.node(),n).tween,i=0,l=r.length;i<l;++i)if((o=r[i]).name===e)return o.value;return null}return this.each((null==t?Gg:qg)(n,e,t))},delay:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?im:lm)(t,e)):Yg(this.node(),t).delay},duration:function(e){var t=this._id;return arguments.length?this.each(("function"==typeof e?am:sm)(t,e)):Yg(this.node(),t).duration},ease:function(e){var t=this._id;return arguments.length?this.each(function(e,t){if("function"!=typeof t)throw new Error;return function(){Ug(this,e).ease=t}}(t,e)):Yg(this.node(),t).ease},easeVarying:function(e){if("function"!=typeof e)throw new Error;return this.each(function(e,t){return function(){var n=t.apply(this,arguments);if("function"!=typeof n)throw new Error;Ug(this,e).ease=n}}(this._id,e))},end:function(){var e,t,n=this,o=n._id,r=n.size();return new Promise((function(i,l){var a={value:l},s={value:function(){0==--r&&i()}};n.each((function(){var n=Ug(this,o),r=n.on;r!==e&&((t=(e=r).copy())._.cancel.push(a),t._.interrupt.push(a),t._.end.push(s)),n.on=t})),0===r&&i()}))},[Symbol.iterator]:vm[Symbol.iterator]};var hm={time:null,delay:0,duration:250,ease:function(e){return((e*=2)<=1?e*e*e:(e-=2)*e*e+2)/2}};function gm(e,t){for(var n;!(n=e.__transition)||!(n=n[t]);)if(!(e=e.parentNode))throw new Error(`transition ${t} not found`);return n}hh.prototype.interrupt=function(e){return this.each((function(){Xg(this,e)}))},hh.prototype.transition=function(e){var t,n;e instanceof fm?(t=e._id,e=e._name):(t=pm(),(n=hm).time=Og(),e=null==e?null:e+"");for(var o=this._groups,r=o.length,i=0;i<r;++i)for(var l,a=o[i],s=a.length,u=0;u<s;++u)(l=a[u])&&Vg(l,e,t,u,a,n||gm(l,t));return new fm(o,this._parents,e,t)};const mm=e=>()=>e;function ym(e,{sourceEvent:t,target:n,transform:o,dispatch:r}){Object.defineProperties(this,{type:{value:e,enumerable:!0,configurable:!0},sourceEvent:{value:t,enumerable:!0,configurable:!0},target:{value:n,enumerable:!0,configurable:!0},transform:{value:o,enumerable:!0,configurable:!0},_:{value:r}})}function bm(e,t,n){this.k=e,this.x=t,this.y=n}bm.prototype={constructor:bm,scale:function(e){return 1===e?this:new bm(this.k*e,this.x,this.y)},translate:function(e,t){return 0===e&0===t?this:new bm(this.k,this.x+this.k*e,this.y+this.k*t)},apply:function(e){return[e[0]*this.k+this.x,e[1]*this.k+this.y]},applyX:function(e){return e*this.k+this.x},applyY:function(e){return e*this.k+this.y},invert:function(e){return[(e[0]-this.x)/this.k,(e[1]-this.y)/this.k]},invertX:function(e){return(e-this.x)/this.k},invertY:function(e){return(e-this.y)/this.k},rescaleX:function(e){return e.copy().domain(e.range().map(this.invertX,this).map(e.invert,e))},rescaleY:function(e){return e.copy().domain(e.range().map(this.invertY,this).map(e.invert,e))},toString:function(){return"translate("+this.x+","+this.y+") scale("+this.k+")"}};var wm=new bm(1,0,0);function xm(e){e.stopImmediatePropagation()}function _m(e){e.preventDefault(),e.stopImmediatePropagation()}function Sm(e){return!(e.ctrlKey&&"wheel"!==e.type||e.button)}function Em(){var e=this;return e instanceof SVGElement?(e=e.ownerSVGElement||e).hasAttribute("viewBox")?[[(e=e.viewBox.baseVal).x,e.y],[e.x+e.width,e.y+e.height]]:[[0,0],[e.width.baseVal.value,e.height.baseVal.value]]:[[0,0],[e.clientWidth,e.clientHeight]]}function km(){return this.__zoom||wm}function Cm(e){return-e.deltaY*(1===e.deltaMode?.05:e.deltaMode?1:.002)*(e.ctrlKey?10:1)}function Om(){return navigator.maxTouchPoints||"ontouchstart"in this}function Nm(e,t,n){var o=e.invertX(t[0][0])-n[0][0],r=e.invertX(t[1][0])-n[1][0],i=e.invertY(t[0][1])-n[0][1],l=e.invertY(t[1][1])-n[1][1];return e.translate(r>o?(o+r)/2:Math.min(0,o)||Math.max(0,r),l>i?(i+l)/2:Math.min(0,i)||Math.max(0,l))}bm.prototype;const Mm=Symbol("MiniMapSlots"),Pm=["id","x","y","rx","ry","width","height","fill","stroke","stroke-width","shape-rendering"],Tm=Wn({name:"MiniMapNode",compatConfig:{MODE:3},props:{id:{},type:{},selected:{type:Boolean},dragging:{type:Boolean},position:{},dimensions:{},borderRadius:{},color:{},shapeRendering:{},strokeColor:{},strokeWidth:{}},emits:["click","dblclick","mouseenter","mousemove","mouseleave"],setup(e,{emit:t}){const n=e,o=qo(Mm),r=Po(),i=Wt((()=>r.style??{}));function l(e){t("click",e)}function a(e){t("dblclick",e)}function s(e){t("mouseenter",e)}function u(e){t("mousemove",e)}function c(e){t("mouseleave",e)}return(e,t)=>Ft(o)[`node-${n.type}`]?(jr(),Vr(bo(Ft(o)[`node-${n.type}`]),function(e){if(!e)return null;let{class:t,style:n}=e;return t&&!P(t)&&(e.class=re(t)),n&&(e.style=ee(n)),e}(ti({key:0},n)),null,16)):(jr(),Fr("rect",{key:1,id:e.id,class:re(["vue-flow__minimap-node",{selected:e.selected,dragging:e.dragging}]),x:e.position.x,y:e.position.y,rx:e.borderRadius,ry:e.borderRadius,width:e.dimensions.width,height:e.dimensions.height,fill:e.color||i.value.background||i.value.backgroundColor,stroke:e.strokeColor,"stroke-width":e.strokeWidth,"shape-rendering":e.shapeRendering,onClick:l,onDblclick:a,onMouseenter:s,onMousemove:u,onMouseleave:c},null,42,Pm))}}),Am=["width","height","viewBox","aria-labelledby"],Dm=["id"],Im=["d","fill","stroke","stroke-width"],$m=Wn({name:"MiniMap",compatConfig:{MODE:3},props:{nodeColor:{type:[String,Function],default:"#e2e2e2"},nodeStrokeColor:{type:[String,Function],default:"transparent"},nodeClassName:{type:[String,Function]},nodeBorderRadius:{default:5},nodeStrokeWidth:{default:2},maskColor:{default:"rgb(240, 240, 240, 0.6)"},maskStrokeColor:{default:"none"},maskStrokeWidth:{default:1},position:{default:"bottom-right"},pannable:{type:Boolean,default:!1},zoomable:{type:Boolean,default:!1},width:{},height:{},ariaLabel:{default:"Vue Flow mini map"},inversePan:{type:Boolean,default:!1},zoomStep:{default:10},offsetScale:{default:5},maskBorderRadius:{default:0}},emits:["click","nodeClick","nodeDblclick","nodeMouseenter","nodeMousemove","nodeMouseleave"],setup(e,{emit:t}){const n=Mo(),o=Po(),{id:r,edges:i,viewport:l,translateExtent:a,dimensions:s,emits:u,d3Selection:c,d3Zoom:d,getNodesInitialized:f}=Tf(),p=Bt();Go(Mm,n);const v=Wt((()=>{var t;return e.width??(null==(t=o.style)?void 0:t.width)??200})),h=Wt((()=>{var t;return e.height??(null==(t=o.style)?void 0:t.height)??150})),g="undefined"==typeof window||window.chrome?"crispEdges":"geometricPrecision",m=yi((()=>"string"==typeof e.nodeColor?()=>e.nodeColor:e.nodeColor)),y=yi((()=>"string"==typeof e.nodeStrokeColor?()=>e.nodeStrokeColor:e.nodeStrokeColor)),b=yi((()=>"string"==typeof e.nodeClassName?()=>e.nodeClassName:"function"==typeof e.nodeClassName?e.nodeClassName:()=>"")),w=yi((()=>Hc(f.value))),x=yi((()=>({x:-l.value.x/l.value.zoom,y:-l.value.y/l.value.zoom,width:s.value.width/l.value.zoom,height:s.value.height/l.value.zoom}))),_=yi((()=>{return f.value&&f.value.length?(e=w.value,t=x.value,Vc(Rc(Fc(e),Fc(t)))):x.value;var e,t})),S=yi((()=>{const e=_.value.width/v.value,t=_.value.height/h.value;return Math.max(e,t)})),E=yi((()=>{const t=S.value*v.value,n=S.value*h.value,o=e.offsetScale*S.value;return{offset:o,x:_.value.x-(t-_.value.width)/2-o,y:_.value.y-(n-_.value.height)/2-o,width:t+2*o,height:n+2*o}})),k=yi((()=>E.value.x&&E.value.y?`\n    M${E.value.x-E.value.offset},${E.value.y-E.value.offset}\n    h${E.value.width+2*E.value.offset}\n    v${E.value.height+2*E.value.offset}\n    h${-E.value.width-2*E.value.offset}z\n    M${x.value.x+e.maskBorderRadius},${x.value.y}\n    h${x.value.width-2*e.maskBorderRadius}\n    a${e.maskBorderRadius},${e.maskBorderRadius} 0 0 1 ${e.maskBorderRadius},${e.maskBorderRadius}\n    v${x.value.height-2*e.maskBorderRadius}\n    a${e.maskBorderRadius},${e.maskBorderRadius} 0 0 1 -${e.maskBorderRadius},${e.maskBorderRadius}\n    h${-(x.value.width-2*e.maskBorderRadius)}\n    a${e.maskBorderRadius},${e.maskBorderRadius} 0 0 1 -${e.maskBorderRadius},-${e.maskBorderRadius}\n    v${-(x.value.height-2*e.maskBorderRadius)}\n    a${e.maskBorderRadius},${e.maskBorderRadius} 0 0 1 ${e.maskBorderRadius},-${e.maskBorderRadius}z`:""));function C(e){const[n,o]=mh(e);t("click",{event:e,position:{x:n,y:o}})}return yr((t=>{if(p.value){const n=gh(p.value),o=t=>{if("wheel"!==t.sourceEvent.type||!c.value||!d.value)return;const n=-t.sourceEvent.deltaY*(1===t.sourceEvent.deltaMode?.05:t.sourceEvent.deltaMode?1:.002)*e.zoomStep,o=l.value.zoom*2**n;d.value.scaleTo(c.value,o)},r=t=>{if("mousemove"!==t.sourceEvent.type||!c.value||!d.value)return;const n=S.value*Math.max(1,l.value.zoom)*(e.inversePan?-1:1),o={x:l.value.x-t.sourceEvent.movementX*n,y:l.value.y-t.sourceEvent.movementY*n},r=[[0,0],[s.value.width,s.value.height]],i=wm.translate(o.x,o.y).scale(l.value.zoom),u=d.value.constrain()(i,r,a.value);d.value.transform(c.value,u)},i=function(){var e,t,n,o=Sm,r=Em,i=Nm,l=Cm,a=Om,s=[0,1/0],u=[[-1/0,-1/0],[1/0,1/0]],c=250,d=hg,f=tv("start","zoom","end"),p=500,v=150,h=0,g=10;function m(e){e.property("__zoom",km).on("wheel.zoom",E,{passive:!1}).on("mousedown.zoom",k).on("dblclick.zoom",C).filter(a).on("touchstart.zoom",O).on("touchmove.zoom",N).on("touchend.zoom touchcancel.zoom",M).style("-webkit-tap-highlight-color","rgba(0,0,0,0)")}function y(e,t){return(t=Math.max(s[0],Math.min(s[1],t)))===e.k?e:new bm(t,e.x,e.y)}function b(e,t,n){var o=t[0]-n[0]*e.k,r=t[1]-n[1]*e.k;return o===e.x&&r===e.y?e:new bm(e.k,o,r)}function w(e){return[(+e[0][0]+ +e[1][0])/2,(+e[0][1]+ +e[1][1])/2]}function x(e,t,n,o){e.on("start.zoom",(function(){_(this,arguments).event(o).start()})).on("interrupt.zoom end.zoom",(function(){_(this,arguments).event(o).end()})).tween("zoom",(function(){var e=this,i=arguments,l=_(e,i).event(o),a=r.apply(e,i),s=null==n?w(a):"function"==typeof n?n.apply(e,i):n,u=Math.max(a[1][0]-a[0][0],a[1][1]-a[0][1]),c=e.__zoom,f="function"==typeof t?t.apply(e,i):t,p=d(c.invert(s).concat(u/c.k),f.invert(s).concat(u/f.k));return function(e){if(1===e)e=f;else{var t=p(e),n=u/t[2];e=new bm(n,s[0]-t[0]*n,s[1]-t[1]*n)}l.zoom(null,e)}}))}function _(e,t,n){return!n&&e.__zooming||new S(e,t)}function S(e,t){this.that=e,this.args=t,this.active=0,this.sourceEvent=null,this.extent=r.apply(e,t),this.taps=0}function E(e,...t){if(o.apply(this,arguments)){var n=_(this,t).event(e),r=this.__zoom,a=Math.max(s[0],Math.min(s[1],r.k*Math.pow(2,l.apply(this,arguments)))),c=mh(e);if(n.wheel)n.mouse[0][0]===c[0]&&n.mouse[0][1]===c[1]||(n.mouse[1]=r.invert(n.mouse[0]=c)),clearTimeout(n.wheel);else{if(r.k===a)return;n.mouse=[c,r.invert(c)],Xg(this),n.start()}_m(e),n.wheel=setTimeout((function(){n.wheel=null,n.end()}),v),n.zoom("mouse",i(b(y(r,a),n.mouse[0],n.mouse[1]),n.extent,u))}}function k(e,...t){if(!n&&o.apply(this,arguments)){var r,l,a,s=e.currentTarget,c=_(this,t,!0).event(e),d=gh(e.view).on("mousemove.zoom",(function(e){if(_m(e),!c.moved){var t=e.clientX-p,n=e.clientY-v;c.moved=t*t+n*n>h}c.event(e).zoom("mouse",i(b(c.that.__zoom,c.mouse[0]=mh(e,s),c.mouse[1]),c.extent,u))}),!0).on("mouseup.zoom",(function(e){var t,n,o,r;d.on("mousemove.zoom mouseup.zoom",null),t=e.view,n=c.moved,o=t.document.documentElement,r=gh(t).on("dragstart.drag",null),n&&(r.on("click.drag",bh,yh),setTimeout((function(){r.on("click.drag",null)}),0)),"onselectstart"in o?r.on("selectstart.drag",null):(o.style.MozUserSelect=o.__noselect,delete o.__noselect),_m(e),c.event(e).end()}),!0),f=mh(e,s),p=e.clientX,v=e.clientY;l=(r=e.view).document.documentElement,a=gh(r).on("dragstart.drag",bh,yh),"onselectstart"in l?a.on("selectstart.drag",bh,yh):(l.__noselect=l.style.MozUserSelect,l.style.MozUserSelect="none"),xm(e),c.mouse=[f,this.__zoom.invert(f)],Xg(this),c.start()}}function C(e,...t){if(o.apply(this,arguments)){var n=this.__zoom,l=mh(e.changedTouches?e.changedTouches[0]:e,this),a=n.invert(l),s=n.k*(e.shiftKey?.5:2),d=i(b(y(n,s),l,a),r.apply(this,t),u);_m(e),c>0?gh(this).transition().duration(c).call(x,d,l,e):gh(this).call(m.transform,d,l,e)}}function O(n,...r){if(o.apply(this,arguments)){var i,l,a,s,u=n.touches,c=u.length,d=_(this,r,n.changedTouches.length===c).event(n);for(xm(n),l=0;l<c;++l)s=[s=mh(a=u[l],this),this.__zoom.invert(s),a.identifier],d.touch0?d.touch1||d.touch0[2]===s[2]||(d.touch1=s,d.taps=0):(d.touch0=s,i=!0,d.taps=1+!!e);e&&(e=clearTimeout(e)),i&&(d.taps<2&&(t=s[0],e=setTimeout((function(){e=null}),p)),Xg(this),d.start())}}function N(e,...t){if(this.__zooming){var n,o,r,l,a=_(this,t).event(e),s=e.changedTouches,c=s.length;for(_m(e),n=0;n<c;++n)r=mh(o=s[n],this),a.touch0&&a.touch0[2]===o.identifier?a.touch0[0]=r:a.touch1&&a.touch1[2]===o.identifier&&(a.touch1[0]=r);if(o=a.that.__zoom,a.touch1){var d=a.touch0[0],f=a.touch0[1],p=a.touch1[0],v=a.touch1[1],h=(h=p[0]-d[0])*h+(h=p[1]-d[1])*h,g=(g=v[0]-f[0])*g+(g=v[1]-f[1])*g;o=y(o,Math.sqrt(h/g)),r=[(d[0]+p[0])/2,(d[1]+p[1])/2],l=[(f[0]+v[0])/2,(f[1]+v[1])/2]}else{if(!a.touch0)return;r=a.touch0[0],l=a.touch0[1]}a.zoom("touch",i(b(o,r,l),a.extent,u))}}function M(e,...o){if(this.__zooming){var r,i,l=_(this,o).event(e),a=e.changedTouches,s=a.length;for(xm(e),n&&clearTimeout(n),n=setTimeout((function(){n=null}),p),r=0;r<s;++r)i=a[r],l.touch0&&l.touch0[2]===i.identifier?delete l.touch0:l.touch1&&l.touch1[2]===i.identifier&&delete l.touch1;if(l.touch1&&!l.touch0&&(l.touch0=l.touch1,delete l.touch1),l.touch0)l.touch0[1]=this.__zoom.invert(l.touch0[0]);else if(l.end(),2===l.taps&&(i=mh(i,this),Math.hypot(t[0]-i[0],t[1]-i[1])<g)){var u=gh(this).on("dblclick.zoom");u&&u.apply(this,arguments)}}}return m.transform=function(e,t,n,o){var r=e.selection?e.selection():e;r.property("__zoom",km),e!==r?x(e,t,n,o):r.interrupt().each((function(){_(this,arguments).event(o).start().zoom(null,"function"==typeof t?t.apply(this,arguments):t).end()}))},m.scaleBy=function(e,t,n,o){m.scaleTo(e,(function(){return this.__zoom.k*("function"==typeof t?t.apply(this,arguments):t)}),n,o)},m.scaleTo=function(e,t,n,o){m.transform(e,(function(){var e=r.apply(this,arguments),o=this.__zoom,l=null==n?w(e):"function"==typeof n?n.apply(this,arguments):n,a=o.invert(l),s="function"==typeof t?t.apply(this,arguments):t;return i(b(y(o,s),l,a),e,u)}),n,o)},m.translateBy=function(e,t,n,o){m.transform(e,(function(){return i(this.__zoom.translate("function"==typeof t?t.apply(this,arguments):t,"function"==typeof n?n.apply(this,arguments):n),r.apply(this,arguments),u)}),null,o)},m.translateTo=function(e,t,n,o,l){m.transform(e,(function(){var e=r.apply(this,arguments),l=this.__zoom,a=null==o?w(e):"function"==typeof o?o.apply(this,arguments):o;return i(wm.translate(a[0],a[1]).scale(l.k).translate("function"==typeof t?-t.apply(this,arguments):-t,"function"==typeof n?-n.apply(this,arguments):-n),e,u)}),o,l)},S.prototype={event:function(e){return e&&(this.sourceEvent=e),this},start:function(){return 1==++this.active&&(this.that.__zooming=this,this.emit("start")),this},zoom:function(e,t){return this.mouse&&"mouse"!==e&&(this.mouse[1]=t.invert(this.mouse[0])),this.touch0&&"touch"!==e&&(this.touch0[1]=t.invert(this.touch0[0])),this.touch1&&"touch"!==e&&(this.touch1[1]=t.invert(this.touch1[0])),this.that.__zoom=t,this.emit("zoom"),this},end:function(){return 0==--this.active&&(delete this.that.__zooming,this.emit("end")),this},emit:function(e){var t=gh(this.that).datum();f.call(e,this.that,new ym(e,{sourceEvent:this.sourceEvent,target:m,type:e,transform:this.that.__zoom,dispatch:f}),t)}},m.wheelDelta=function(e){return arguments.length?(l="function"==typeof e?e:mm(+e),m):l},m.filter=function(e){return arguments.length?(o="function"==typeof e?e:mm(!!e),m):o},m.touchable=function(e){return arguments.length?(a="function"==typeof e?e:mm(!!e),m):a},m.extent=function(e){return arguments.length?(r="function"==typeof e?e:mm([[+e[0][0],+e[0][1]],[+e[1][0],+e[1][1]]]),m):r},m.scaleExtent=function(e){return arguments.length?(s[0]=+e[0],s[1]=+e[1],m):[s[0],s[1]]},m.translateExtent=function(e){return arguments.length?(u[0][0]=+e[0][0],u[1][0]=+e[1][0],u[0][1]=+e[0][1],u[1][1]=+e[1][1],m):[[u[0][0],u[0][1]],[u[1][0],u[1][1]]]},m.constrain=function(e){return arguments.length?(i=e,m):i},m.duration=function(e){return arguments.length?(c=+e,m):c},m.interpolate=function(e){return arguments.length?(d=e,m):d},m.on=function(){var e=f.on.apply(f,arguments);return e===f?m:e},m.clickDistance=function(e){return arguments.length?(h=(e=+e)*e,m):Math.sqrt(h)},m.tapDistance=function(e){return arguments.length?(g=+e,m):g},m}().on("zoom",e.pannable?r:()=>{}).on("zoom.wheel",e.zoomable?o:()=>{});n.call(i),t((()=>{n.on("zoom",null)}))}}),{flush:"post"}),(e,n)=>(jr(),Vr(Ft(sp),{position:e.position,class:re(["vue-flow__minimap",{pannable:e.pannable,zoomable:e.zoomable}])},{default:Sn((()=>[(jr(),Fr("svg",{ref_key:"el",ref:p,width:v.value,height:h.value,viewBox:[E.value.x,E.value.y,E.value.width,E.value.height].join(" "),role:"img","aria-labelledby":`vue-flow__minimap-${Ft(r)}`,onClick:C},[e.ariaLabel?(jr(),Fr("title",{key:0,id:`vue-flow__minimap-${Ft(r)}`},ue(e.ariaLabel),9,Dm)):Kr("",!0),(jr(!0),Fr(Tr,null,_o(Ft(f),(n=>(jr(),Vr(Tm,{id:n.id,key:n.id,position:n.computedPosition,dimensions:n.dimensions,selected:n.selected,dragging:n.dragging,style:ee(n.style),class:re(b.value(n)),color:m.value(n),"border-radius":e.nodeBorderRadius,"stroke-color":y.value(n),"stroke-width":e.nodeStrokeWidth,"shape-rendering":Ft(g),type:n.type,onClick:e=>function(e,n){const o={event:e,node:n,connectedEdges:Yc([n],i.value)};u.miniMapNodeClick(o),t("nodeClick",o)}(e,n),onDblclick:e=>function(e,n){const o={event:e,node:n,connectedEdges:Yc([n],i.value)};u.miniMapNodeDoubleClick(o),t("nodeDblclick",o)}(e,n),onMouseenter:e=>function(e,n){const o={event:e,node:n,connectedEdges:Yc([n],i.value)};u.miniMapNodeMouseEnter(o),t("nodeMouseenter",o)}(e,n),onMousemove:e=>function(e,n){const o={event:e,node:n,connectedEdges:Yc([n],i.value)};u.miniMapNodeMouseMove(o),t("nodeMousemove",o)}(e,n),onMouseleave:e=>function(e,n){const o={event:e,node:n,connectedEdges:Yc([n],i.value)};u.miniMapNodeMouseLeave(o),t("nodeMouseleave",o)}(e,n)},null,8,["id","position","dimensions","selected","dragging","style","class","color","border-radius","stroke-color","stroke-width","shape-rendering","type","onClick","onDblclick","onMouseenter","onMousemove","onMouseleave"])))),128)),Gr("path",{class:"vue-flow__minimap-mask",d:k.value,fill:e.maskColor,stroke:e.maskStrokeColor,"stroke-width":e.maskStrokeWidth,"fill-rule":"evenodd"},null,8,Im)],8,Am))])),_:1},8,["position","class"]))}});let zm=Symbol("headlessui.useid"),jm=0;function Bm(){return qo(zm,(()=>""+ ++jm))()}let Lm=Symbol("Context");var Rm=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(Rm||{});function Fm(){return qo(Lm,null)}function Vm(e){var t;if(null==e||null==e.value)return null;let n=null!=(t=e.value.$el)?t:e.value;return n instanceof Node?n:null}var Hm=Object.defineProperty,Um=(e,t,n)=>(((e,t,n)=>{t in e?Hm(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let Ym=new class{constructor(){Um(this,"current",this.detect()),Um(this,"currentId",0)}set(e){this.current!==e&&(this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}};function Xm(e,t,...n){if(e in t){let o=t[e];return"function"==typeof o?o(...n):o}let o=new Error(`Tried to handle "${e}" but there is no handler defined. Only defined handlers are: ${Object.keys(t).map((e=>`"${e}"`)).join(", ")}.`);throw Error.captureStackTrace&&Error.captureStackTrace(o,Xm),o}var Gm=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(Gm||{}),qm=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))(qm||{});function Wm({visible:e=!0,features:t=0,ourProps:n,theirProps:o,...r}){var i;let l=Jm(o,n),a=Object.assign(r,{props:l});return e||2&t&&l.static?Zm(a):1&t?Xm(null==(i=l.unmount)||i?0:1,{0:()=>null,1:()=>Zm({...r,props:{...l,hidden:!0,style:{display:"none"}}})}):Zm(a)}function Zm({props:e,attrs:t,slots:n,slot:o,name:r}){var i,l;let{as:a,...s}=Qm(e,["unmount","static"]),u=null==(i=n.default)?void 0:i.call(n,o),c={};if(o){let e=!1,t=[];for(let[n,r]of Object.entries(o))"boolean"==typeof r&&(e=!0),!0===r&&t.push(n);e&&(c["data-headlessui-state"]=t.join(" "))}if("template"===a){if(u=Km(null!=u?u:[]),Object.keys(s).length>0||Object.keys(t).length>0){let[e,...n]=null!=u?u:[];if(!function(e){return null!=e&&("string"==typeof e.type||"object"==typeof e.type||"function"==typeof e.type)}(e)||n.length>0)throw new Error(['Passing props on "template"!',"",`The current component <${r} /> is rendering a "template".`,"However we need to passthrough the following props:",Object.keys(s).concat(Object.keys(t)).map((e=>e.trim())).filter(((e,t,n)=>n.indexOf(e)===t)).sort(((e,t)=>e.localeCompare(t))).map((e=>`  - ${e}`)).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "template".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>`  - ${e}`)).join("\n")].join("\n"));let o=Jm(null!=(l=e.props)?l:{},s,c),i=Wr(e,o,!0);for(let e in o)e.startsWith("on")&&(i.props||(i.props={}),i.props[e]=o[e]);return i}return Array.isArray(u)&&1===u.length?u[0]:u}return bi(a,Object.assign({},s,c),{default:()=>u})}function Km(e){return e.flatMap((e=>e.type===Tr?Km(e.children):[e]))}function Jm(...e){if(0===e.length)return{};if(1===e.length)return e[0];let t={},n={};for(let o of e)for(let e in o)e.startsWith("on")&&"function"==typeof o[e]?(null!=n[e]||(n[e]=[]),n[e].push(o[e])):t[e]=o[e];if(t.disabled||t["aria-disabled"])return Object.assign(t,Object.fromEntries(Object.keys(n).map((e=>[e,void 0]))));for(let e in n)Object.assign(t,{[e](t,...o){let r=n[e];for(let e of r){if(t instanceof Event&&t.defaultPrevented)return;e(t,...o)}}});return t}function Qm(e,t=[]){let n=Object.assign({},e);for(let e of t)e in n&&delete n[e];return n}function ey(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}function ty(){let e=[],t={addEventListener:(e,n,o,r)=>(e.addEventListener(n,o,r),t.add((()=>e.removeEventListener(n,o,r)))),requestAnimationFrame(...e){let n=requestAnimationFrame(...e);t.add((()=>cancelAnimationFrame(n)))},nextFrame(...e){t.requestAnimationFrame((()=>{t.requestAnimationFrame(...e)}))},setTimeout(...e){let n=setTimeout(...e);t.add((()=>clearTimeout(n)))},microTask(...e){let n={current:!0};return ey((()=>{n.current&&e[0]()})),t.add((()=>{n.current=!1}))},style(e,t,n){let o=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:o})}))},group(e){let t=ty();return e(t),this.add((()=>t.dispose()))},add:t=>(e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function ny(e,...t){e&&t.length>0&&e.classList.add(...t)}function oy(e,...t){e&&t.length>0&&e.classList.remove(...t)}var ry=(e=>(e.Finished="finished",e.Cancelled="cancelled",e))(ry||{});function iy(e,t,n,o,r,i){let l=ty(),a=void 0!==i?function(e){let t={called:!1};return(...n)=>{if(!t.called)return t.called=!0,e(...n)}}(i):()=>{};return oy(e,...r),ny(e,...t,...n),l.nextFrame((()=>{oy(e,...n),ny(e,...o),l.add(function(e,t){let n=ty();if(!e)return n.dispose;let{transitionDuration:o,transitionDelay:r}=getComputedStyle(e),[i,l]=[o,r].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t}));return 0!==i?n.setTimeout((()=>t("finished")),i+l):t("finished"),n.add((()=>t("cancelled"))),n.dispose}(e,(n=>(oy(e,...o,...t),ny(e,...r),a(n)))))})),l.add((()=>oy(e,...t,...n,...o,...r))),l.add((()=>a("cancelled"))),l.dispose}function ly(e=""){return e.split(/\s+/).filter((e=>e.length>1))}let ay=Symbol("TransitionContext");var sy=(e=>(e.Visible="visible",e.Hidden="hidden",e))(sy||{});let uy=Symbol("NestingContext");function cy(e){return"children"in e?cy(e.children):e.value.filter((({state:e})=>"visible"===e)).length>0}function dy(e){let t=Bt([]),n=Bt(!1);function o(o,r=qm.Hidden){let i=t.value.findIndex((({id:e})=>e===o));-1!==i&&(Xm(r,{[qm.Unmount](){t.value.splice(i,1)},[qm.Hidden](){t.value[i].state="hidden"}}),!cy(t)&&n.value&&(null==e||e()))}return lo((()=>n.value=!0)),co((()=>n.value=!1)),{children:t,register:function(e){let n=t.value.find((({id:t})=>t===e));return n?"visible"!==n.state&&(n.state="visible"):t.value.push({id:e,state:"visible"}),()=>o(e,qm.Unmount)},unregister:o}}let fy=Gm.RenderStrategy,py=Wn({props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:o,expose:r}){let i=Bt(0);function l(){i.value|=Rm.Opening,t("beforeEnter")}function a(){i.value&=~Rm.Opening,t("afterEnter")}function s(){i.value|=Rm.Closing,t("beforeLeave")}function u(){i.value&=~Rm.Closing,t("afterLeave")}if(null===qo(ay,null)&&null!==Fm())return()=>bi(hy,{...e,onBeforeEnter:l,onAfterEnter:a,onBeforeLeave:s,onAfterLeave:u},o);let c=Bt(null),d=yi((()=>e.unmount?qm.Unmount:qm.Hidden));r({el:c,$el:c});let{show:f,appear:p}=function(){let e=qo(ay,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),{register:v,unregister:h}=function(){let e=qo(uy,null);if(null===e)throw new Error("A <TransitionChild /> is used but it is missing a parent <TransitionRoot />.");return e}(),g=Bt(f.value?"visible":"hidden"),m={value:!0},y=Bm(),b={value:!1},w=dy((()=>{!b.value&&"hidden"!==g.value&&(g.value="hidden",h(y),u())}));lo((()=>{let e=v(y);co(e)})),yr((()=>{if(d.value===qm.Hidden&&y){if(f.value&&"visible"!==g.value)return void(g.value="visible");Xm(g.value,{hidden:()=>h(y),visible:()=>v(y)})}}));let x=ly(e.enter),_=ly(e.enterFrom),S=ly(e.enterTo),E=ly(e.entered),k=ly(e.leave),C=ly(e.leaveFrom),O=ly(e.leaveTo);return lo((()=>{yr((()=>{if("visible"===g.value){let e=Vm(c);if(e instanceof Comment&&""===e.data)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}}))})),lo((()=>{br([f],((e,t,n)=>{(function(e){let t=m.value&&!p.value,n=Vm(c);!n||!(n instanceof HTMLElement)||t||(b.value=!0,f.value&&l(),f.value||s(),e(f.value?iy(n,x,_,S,E,(e=>{b.value=!1,e===ry.Finished&&a()})):iy(n,k,C,O,E,(e=>{b.value=!1,e===ry.Finished&&(cy(w)||(g.value="hidden",h(y),u()))}))))})(n),m.value=!1}),{immediate:!0})})),Go(uy,w),function(e){Go(Lm,e)}(yi((()=>Xm(g.value,{visible:Rm.Open,hidden:Rm.Closed})|i.value))),()=>{let{appear:t,show:r,enter:i,enterFrom:l,enterTo:a,entered:s,leave:u,leaveFrom:d,leaveTo:v,...h}=e,m={ref:c};return Wm({theirProps:{...h,...p.value&&f.value&&Ym.isServer?{class:re([n.class,h.class,...x,..._])}:{}},ourProps:m,slot:{},slots:o,attrs:n,features:fy,visible:"visible"===g.value,name:"TransitionChild"})}}}),vy=py,hy=Wn({inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},show:{type:[Boolean],default:null},unmount:{type:[Boolean],default:!0},appear:{type:[Boolean],default:!1},enter:{type:[String],default:""},enterFrom:{type:[String],default:""},enterTo:{type:[String],default:""},entered:{type:[String],default:""},leave:{type:[String],default:""},leaveFrom:{type:[String],default:""},leaveTo:{type:[String],default:""}},emits:{beforeEnter:()=>!0,afterEnter:()=>!0,beforeLeave:()=>!0,afterLeave:()=>!0},setup(e,{emit:t,attrs:n,slots:o}){let r=Fm(),i=yi((()=>null===e.show&&null!==r?(r.value&Rm.Open)===Rm.Open:e.show));yr((()=>{if(![!0,!1].includes(i.value))throw new Error('A <Transition /> is used but it is missing a `:show="true | false"` prop.')}));let l=Bt(i.value?"visible":"hidden"),a=dy((()=>{l.value="hidden"})),s=Bt(!0),u={show:i,appear:yi((()=>e.appear||!s.value))};return lo((()=>{yr((()=>{s.value=!1,i.value?l.value="visible":cy(a)||(l.value="hidden")}))})),Go(uy,a),Go(ay,u),()=>{let r=Qm(e,["show","appear","unmount","onBeforeEnter","onBeforeLeave","onAfterEnter","onAfterLeave"]),i={unmount:e.unmount};return Wm({ourProps:{...i,as:"template"},theirProps:{},slot:{},slots:{...o,default:()=>[bi(vy,{onBeforeEnter:()=>t("beforeEnter"),onAfterEnter:()=>t("afterEnter"),onBeforeLeave:()=>t("beforeLeave"),onAfterLeave:()=>t("afterLeave"),...n,...i,...r},o.default)]},attrs:{},features:fy,visible:"visible"===l.value,name:"Transition"})}}});function gy(e,t,n,o){Ym.isServer||yr((r=>{(e=null!=e?e:window).addEventListener(t,n,o),r((()=>e.removeEventListener(t,n,o)))}))}function my(e,t,n){Ym.isServer||yr((o=>{window.addEventListener(e,t,n),o((()=>window.removeEventListener(e,t,n)))}))}var yy,by=((yy=by||{})[yy.Forwards=0]="Forwards",yy[yy.Backwards=1]="Backwards",yy),wy=(e=>(e[e.None=1]="None",e[e.Focusable=2]="Focusable",e[e.Hidden=4]="Hidden",e))(wy||{});let xy=Wn({name:"Hidden",props:{as:{type:[Object,String],default:"div"},features:{type:Number,default:1}},setup:(e,{slots:t,attrs:n})=>()=>{var o;let{features:r,...i}=e;return Wm({ourProps:{"aria-hidden":!(2&~r)||(null!=(o=i["aria-hidden"])?o:void 0),hidden:!(4&~r)||void 0,style:{position:"fixed",top:1,left:1,width:1,height:0,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0",...!(4&~r)&&!!(2&~r)&&{display:"none"}}},theirProps:i,slot:{},attrs:n,slots:t,name:"Hidden"})}}),_y=[];function Sy(e){if(Ym.isServer)return null;if(e instanceof Node)return e.ownerDocument;if(null!=e&&e.hasOwnProperty("value")){let t=Vm(e);if(t)return t.ownerDocument}return document}!function(){function e(){"loading"!==document.readyState&&((()=>{function e(e){e.target instanceof HTMLElement&&e.target!==document.body&&_y[0]!==e.target&&(_y.unshift(e.target),_y=_y.filter((e=>null!=e&&e.isConnected)),_y.splice(10))}window.addEventListener("click",e,{capture:!0}),window.addEventListener("mousedown",e,{capture:!0}),window.addEventListener("focus",e,{capture:!0}),document.body.addEventListener("click",e,{capture:!0}),document.body.addEventListener("mousedown",e,{capture:!0}),document.body.addEventListener("focus",e,{capture:!0})})(),document.removeEventListener("DOMContentLoaded",e))}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("DOMContentLoaded",e),e())}();let Ey=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>`${e}:not([tabindex='-1'])`)).join(",");var ky=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(ky||{}),Cy=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(Cy||{}),Oy=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(Oy||{});var Ny=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(Ny||{});var My=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(My||{});function Py(e){null==e||e.focus({preventScroll:!0})}"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let Ty=["textarea","input"].join(",");function Ay(e,t,{sorted:n=!0,relativeTo:o=null,skipElements:r=[]}={}){var i;let l=null!=(i=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:null==e?void 0:e.ownerDocument)?i:document,a=Array.isArray(e)?n?function(e,t=e=>e){return e.slice().sort(((e,n)=>{let o=t(e),r=t(n);if(null===o||null===r)return 0;let i=o.compareDocumentPosition(r);return i&Node.DOCUMENT_POSITION_FOLLOWING?-1:i&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}(e):e:function(e=document.body){return null==e?[]:Array.from(e.querySelectorAll(Ey)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}(e);r.length>0&&a.length>1&&(a=a.filter((e=>!r.includes(e)))),o=null!=o?o:l.activeElement;let s,u=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,a.indexOf(o))-1;if(4&t)return Math.max(0,a.indexOf(o))+1;if(8&t)return a.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),d=32&t?{preventScroll:!0}:{},f=0,p=a.length;do{if(f>=p||f+p<=0)return 0;let e=c+f;if(16&t)e=(e+p)%p;else{if(e<0)return 3;if(e>=p)return 1}s=a[e],null==s||s.focus(d),f+=u}while(s!==l.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,Ty))&&n}(s)&&s.select(),2}function Dy(e){if(!e)return new Set;if("function"==typeof e)return new Set(e());let t=new Set;for(let n of e.value){let e=Vm(n);e instanceof HTMLElement&&t.add(e)}return t}var Iy=(e=>(e[e.None=1]="None",e[e.InitialFocus=2]="InitialFocus",e[e.TabLock=4]="TabLock",e[e.FocusLock=8]="FocusLock",e[e.RestoreFocus=16]="RestoreFocus",e[e.All=30]="All",e))(Iy||{});let $y=Object.assign(Wn({name:"FocusTrap",props:{as:{type:[Object,String],default:"div"},initialFocus:{type:Object,default:null},features:{type:Number,default:30},containers:{type:[Object,Function],default:Bt(new Set)}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:o}){let r=Bt(null);o({el:r,$el:r});let i=yi((()=>Sy(r))),l=Bt(!1);lo((()=>l.value=!0)),co((()=>l.value=!1)),function({ownerDocument:e},t){let n=function(e){let t=Bt(_y.slice());return br([e],(([e],[n])=>{!0===n&&!1===e?ey((()=>{t.value.splice(0)})):!1===n&&!0===e&&(t.value=_y.slice())}),{flush:"post"}),()=>{var e;return null!=(e=t.value.find((e=>null!=e&&e.isConnected)))?e:null}}(t);lo((()=>{yr((()=>{var o,r;t.value||(null==(o=e.value)?void 0:o.activeElement)===(null==(r=e.value)?void 0:r.body)&&Py(n())}),{flush:"post"})})),co((()=>{t.value&&Py(n())}))}({ownerDocument:i},yi((()=>l.value&&Boolean(16&e.features))));let a=function({ownerDocument:e,container:t,initialFocus:n},o){let r=Bt(null),i=Bt(!1);return lo((()=>i.value=!0)),co((()=>i.value=!1)),lo((()=>{br([t,n,o],((l,a)=>{if(l.every(((e,t)=>(null==a?void 0:a[t])===e))||!o.value)return;let s=Vm(t);s&&ey((()=>{var t,o;if(!i.value)return;let l=Vm(n),a=null==(t=e.value)?void 0:t.activeElement;if(l){if(l===a)return void(r.value=a)}else if(s.contains(a))return void(r.value=a);l?Py(l):Ay(s,ky.First|ky.NoScroll)===Cy.Error&&console.warn("There are no focusable elements inside the <FocusTrap />"),r.value=null==(o=e.value)?void 0:o.activeElement}))}),{immediate:!0,flush:"post"})})),r}({ownerDocument:i,container:r,initialFocus:yi((()=>e.initialFocus))},yi((()=>l.value&&Boolean(2&e.features))));!function({ownerDocument:e,container:t,containers:n,previousActiveElement:o},r){var i;gy(null==(i=e.value)?void 0:i.defaultView,"focus",(e=>{if(!r.value)return;let i=Dy(n);Vm(t)instanceof HTMLElement&&i.add(Vm(t));let l=o.value;if(!l)return;let a=e.target;a&&a instanceof HTMLElement?zy(i,a)?(o.value=a,Py(a)):(e.preventDefault(),e.stopPropagation(),Py(l)):Py(o.value)}),!0)}({ownerDocument:i,container:r,containers:e.containers,previousActiveElement:a},yi((()=>l.value&&Boolean(8&e.features))));let s=function(){let e=Bt(0);return my("keydown",(t=>{"Tab"===t.key&&(e.value=t.shiftKey?1:0)})),e}();function u(e){let t=Vm(r);t&&Xm(s.value,{[by.Forwards]:()=>{Ay(t,ky.First,{skipElements:[e.relatedTarget]})},[by.Backwards]:()=>{Ay(t,ky.Last,{skipElements:[e.relatedTarget]})}})}let c=Bt(!1);function d(e){"Tab"===e.key&&(c.value=!0,requestAnimationFrame((()=>{c.value=!1})))}function f(t){if(!l.value)return;let n=Dy(e.containers);Vm(r)instanceof HTMLElement&&n.add(Vm(r));let o=t.relatedTarget;o instanceof HTMLElement&&"true"!==o.dataset.headlessuiFocusGuard&&(zy(n,o)||(c.value?Ay(Vm(r),Xm(s.value,{[by.Forwards]:()=>ky.Next,[by.Backwards]:()=>ky.Previous})|ky.WrapAround,{relativeTo:t.target}):t.target instanceof HTMLElement&&Py(t.target)))}return()=>{let o={ref:r,onKeydown:d,onFocusout:f},{features:i,initialFocus:l,containers:a,...s}=e;return bi(Tr,[Boolean(4&i)&&bi(xy,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:wy.Focusable}),Wm({ourProps:o,theirProps:{...t,...s},slot:{},attrs:t,slots:n,name:"FocusTrap"}),Boolean(4&i)&&bi(xy,{as:"button",type:"button","data-headlessui-focus-guard":!0,onFocus:u,features:wy.Focusable})])}}}),{features:Iy});function zy(e,t){for(let n of e)if(n.contains(t))return!0;return!1}function jy(){let e;return{before({doc:t}){var n;let o=t.documentElement;e=(null!=(n=t.defaultView)?n:window).innerWidth-o.clientWidth},after({doc:t,d:n}){let o=t.documentElement,r=o.clientWidth-o.offsetWidth,i=e-r;n.style(o,"paddingRight",`${i}px`)}}}function By(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0}function Ly(e){let t={};for(let n of e)Object.assign(t,n(t));return t}let Ry=function(e,t){let n=new Map,o=new Set;return{getSnapshot:()=>n,subscribe:e=>(o.add(e),()=>o.delete(e)),dispatch(e,...r){let i=t[e].call(n,...r);i&&(n=i,o.forEach((e=>e())))}}}(0,{PUSH(e,t){var n;let o=null!=(n=this.get(e))?n:{doc:e,count:0,d:ty(),meta:new Set};return o.count++,o.meta.add(t),this.set(e,o),this},POP(e,t){let n=this.get(e);return n&&(n.count--,n.meta.delete(t)),this},SCROLL_PREVENT({doc:e,d:t,meta:n}){let o={doc:e,d:t,meta:Ly(n)},r=[By()?{before({doc:e,d:t,meta:n}){function o(e){return n.containers.flatMap((e=>e())).some((t=>t.contains(e)))}t.microTask((()=>{var n;if("auto"!==window.getComputedStyle(e.documentElement).scrollBehavior){let n=ty();n.style(e.documentElement,"scrollBehavior","auto"),t.add((()=>t.microTask((()=>n.dispose()))))}let r=null!=(n=window.scrollY)?n:window.pageYOffset,i=null;t.addEventListener(e,"click",(t=>{if(t.target instanceof HTMLElement)try{let n=t.target.closest("a");if(!n)return;let{hash:r}=new URL(n.href),l=e.querySelector(r);l&&!o(l)&&(i=l)}catch{}}),!0),t.addEventListener(e,"touchstart",(e=>{if(e.target instanceof HTMLElement)if(o(e.target)){let n=e.target;for(;n.parentElement&&o(n.parentElement);)n=n.parentElement;t.style(n,"overscrollBehavior","contain")}else t.style(e.target,"touchAction","none")})),t.addEventListener(e,"touchmove",(e=>{if(e.target instanceof HTMLElement){if("INPUT"===e.target.tagName)return;if(o(e.target)){let t=e.target;for(;t.parentElement&&""!==t.dataset.headlessuiPortal&&!(t.scrollHeight>t.clientHeight||t.scrollWidth>t.clientWidth);)t=t.parentElement;""===t.dataset.headlessuiPortal&&e.preventDefault()}else e.preventDefault()}}),{passive:!1}),t.add((()=>{var e;let t=null!=(e=window.scrollY)?e:window.pageYOffset;r!==t&&window.scrollTo(0,r),i&&i.isConnected&&(i.scrollIntoView({block:"nearest"}),i=null)}))}))}}:{},jy(),{before({doc:e,d:t}){t.style(e.documentElement,"overflow","hidden")}}];r.forEach((({before:e})=>null==e?void 0:e(o))),r.forEach((({after:e})=>null==e?void 0:e(o)))},SCROLL_ALLOW({d:e}){e.dispose()},TEARDOWN({doc:e}){this.delete(e)}});function Fy(e,t,n){let o=function(e){let t=function(e){return Lt(e,!0)}(e.getSnapshot());return co(e.subscribe((()=>{t.value=e.getSnapshot()}))),t}(Ry),r=yi((()=>{let t=e.value?o.value.get(e.value):void 0;return!!t&&t.count>0}));return br([e,t],(([e,t],[o],r)=>{if(!e||!t)return;Ry.dispatch("PUSH",e,n);let i=!1;r((()=>{i||(Ry.dispatch("POP",null!=o?o:e,n),i=!0)}))}),{immediate:!0}),r}Ry.subscribe((()=>{let e=Ry.getSnapshot(),t=new Map;for(let[n]of e)t.set(n,n.documentElement.style.overflow);for(let n of e.values()){let e="hidden"===t.get(n.doc),o=0!==n.count;(o&&!e||!o&&e)&&Ry.dispatch(n.count>0?"SCROLL_PREVENT":"SCROLL_ALLOW",n),0===n.count&&Ry.dispatch("TEARDOWN",n)}}));let Vy=new Map,Hy=new Map;function Uy(e,t=Bt(!0)){yr((n=>{var o;if(!t.value)return;let r=Vm(e);if(!r)return;n((function(){var e;if(!r)return;let t=null!=(e=Hy.get(r))?e:1;if(1===t?Hy.delete(r):Hy.set(r,t-1),1!==t)return;let n=Vy.get(r);n&&(null===n["aria-hidden"]?r.removeAttribute("aria-hidden"):r.setAttribute("aria-hidden",n["aria-hidden"]),r.inert=n.inert,Vy.delete(r))}));let i=null!=(o=Hy.get(r))?o:0;Hy.set(r,i+1),0===i&&(Vy.set(r,{"aria-hidden":r.getAttribute("aria-hidden"),inert:r.inert}),r.setAttribute("aria-hidden","true"),r.inert=!0)}))}function Yy(e,t,n){Ym.isServer||yr((o=>{document.addEventListener(e,t,n),o((()=>document.removeEventListener(e,t,n)))}))}let Xy=Symbol("ForcePortalRootContext"),Gy=Wn({name:"ForcePortalRoot",props:{as:{type:[Object,String],default:"template"},force:{type:Boolean,default:!1}},setup:(e,{slots:t,attrs:n})=>(Go(Xy,e.force),()=>{let{force:o,...r}=e;return Wm({theirProps:r,ourProps:{},slot:{},slots:t,attrs:n,name:"ForcePortalRoot"})})}),qy=Symbol("StackContext");var Wy=(e=>(e[e.Add=0]="Add",e[e.Remove=1]="Remove",e))(Wy||{});var Zy=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(Zy||{});let Ky=Symbol("DescriptionContext");Wn({name:"Description",props:{as:{type:[Object,String],default:"p"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var o;let r=null!=(o=e.id)?o:`headlessui-description-${Bm()}`,i=function(){let e=qo(Ky,null);if(null===e)throw new Error("Missing parent");return e}();return lo((()=>co(i.register(r)))),()=>{let{name:o="Description",slot:l=Bt({}),props:a={}}=i,{...s}=e,u={...Object.entries(a).reduce(((e,[t,n])=>Object.assign(e,{[t]:Ft(n)})),{}),id:r};return Wm({ourProps:u,theirProps:s,slot:l.value,attrs:t,slots:n,name:o})}}});const Jy=new WeakMap;function Qy(e,t){let n=t(function(e){var t;return null!=(t=Jy.get(e))?t:0}(e));return n<=0?Jy.delete(e):Jy.set(e,n),n}let eb=Wn({name:"Portal",props:{as:{type:[Object,String],default:"div"}},setup(e,{slots:t,attrs:n}){let o=Bt(null),r=yi((()=>Sy(o))),i=qo(Xy,!1),l=qo(nb,null),a=Bt(!0===i||null==l?function(e){let t=Sy(e);if(!t){if(null===e)return null;throw new Error(`[Headless UI]: Cannot find ownerDocument for contextElement: ${e}`)}let n=t.getElementById("headlessui-portal-root");if(n)return n;let o=t.createElement("div");return o.setAttribute("id","headlessui-portal-root"),t.body.appendChild(o)}(o.value):l.resolveTarget());a.value&&Qy(a.value,(e=>e+1));let s=Bt(!1);lo((()=>{s.value=!0})),yr((()=>{i||null!=l&&(a.value=l.resolveTarget())}));let u=qo(tb,null),c=!1,d=ai();return br(o,(()=>{if(c||!u)return;let e=Vm(o);e&&(co(u.register(e),d),c=!0)})),co((()=>{var e,t;let n=null==(e=r.value)?void 0:e.getElementById("headlessui-portal-root");!n||a.value!==n||Qy(a.value,(e=>e-1))||a.value.children.length>0||null==(t=a.value.parentElement)||t.removeChild(a.value)})),()=>{if(!s.value||null===a.value)return null;let r={ref:o,"data-headlessui-portal":""};return bi(Dn,{to:a.value},Wm({ourProps:r,theirProps:e,slot:{},attrs:n,slots:t,name:"Portal"}))}}}),tb=Symbol("PortalParentContext"),nb=Symbol("PortalGroupContext"),ob=Wn({name:"PortalGroup",props:{as:{type:[Object,String],default:"template"},target:{type:Object,default:null}},setup(e,{attrs:t,slots:n}){let o=Ct({resolveTarget:()=>e.target});return Go(nb,o),()=>{let{target:o,...r}=e;return Wm({theirProps:r,ourProps:{},slot:{},attrs:t,slots:n,name:"PortalGroup"})}}});var rb=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(rb||{});let ib=Symbol("DialogContext");function lb(e){let t=qo(ib,null);if(null===t){let t=new Error(`<${e} /> is missing a parent <Dialog /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(t,lb),t}return t}let ab="DC8F892D-2EBD-447C-A4C8-A03058436FF4",sb=Wn({name:"Dialog",inheritAttrs:!1,props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},open:{type:[Boolean,String],default:ab},initialFocus:{type:Object,default:null},id:{type:String,default:null},role:{type:String,default:"dialog"}},emits:{close:e=>!0},setup(e,{emit:t,attrs:n,slots:o,expose:r}){var i,l;let a=null!=(i=e.id)?i:`headlessui-dialog-${Bm()}`,s=Bt(!1);lo((()=>{s.value=!0}));let u=!1,c=yi((()=>"dialog"===e.role||"alertdialog"===e.role?e.role:(u||(u=!0,console.warn(`Invalid role [${c}] passed to <Dialog />. Only \`dialog\` and and \`alertdialog\` are supported. Using \`dialog\` instead.`)),"dialog"))),d=Bt(0),f=Fm(),p=yi((()=>e.open===ab&&null!==f?(f.value&Rm.Open)===Rm.Open:e.open)),v=Bt(null),h=yi((()=>Sy(v)));if(r({el:v,$el:v}),e.open===ab&&null===f)throw new Error("You forgot to provide an `open` prop to the `Dialog`.");if("boolean"!=typeof p.value)throw new Error(`You provided an \`open\` prop to the \`Dialog\`, but the value is not a boolean. Received: ${p.value===ab?void 0:e.open}`);let g=yi((()=>s.value&&p.value?0:1)),m=yi((()=>0===g.value)),y=yi((()=>d.value>1)),b=null!==qo(ib,null),[w,x]=function(){let e=qo(tb,null),t=Bt([]);function n(n){let o=t.value.indexOf(n);-1!==o&&t.value.splice(o,1),e&&e.unregister(n)}let o={register:function(o){return t.value.push(o),e&&e.register(o),()=>n(o)},unregister:n,portals:t};return[t,Wn({name:"PortalWrapper",setup:(e,{slots:t})=>(Go(tb,o),()=>{var e;return null==(e=t.default)?void 0:e.call(t)})})]}(),{resolveContainers:_,mainTreeNodeRef:S,MainTreeNode:E}=function({defaultContainers:e=[],portals:t,mainTreeNodeRef:n}={}){let o=Bt(null),r=Sy(o);function i(){var n,i,l;let a=[];for(let t of e)null!==t&&(t instanceof HTMLElement?a.push(t):"value"in t&&t.value instanceof HTMLElement&&a.push(t.value));if(null!=t&&t.value)for(let e of t.value)a.push(e);for(let e of null!=(n=null==r?void 0:r.querySelectorAll("html > *, body > *"))?n:[])e!==document.body&&e!==document.head&&e instanceof HTMLElement&&"headlessui-portal-root"!==e.id&&(e.contains(Vm(o))||e.contains(null==(l=null==(i=Vm(o))?void 0:i.getRootNode())?void 0:l.host)||a.some((t=>e.contains(t)))||a.push(e));return a}return{resolveContainers:i,contains:e=>i().some((t=>t.contains(e))),mainTreeNodeRef:o,MainTreeNode:()=>null!=n?null:bi(xy,{features:wy.Hidden,ref:o})}}({portals:w,defaultContainers:[yi((()=>{var e;return null!=(e=D.panelRef.value)?e:v.value}))]}),k=yi((()=>y.value?"parent":"leaf")),C=yi((()=>null!==f&&(f.value&Rm.Closing)===Rm.Closing)),O=yi((()=>!b&&!C.value&&m.value)),N=yi((()=>{var e,t,n;return null!=(n=Array.from(null!=(t=null==(e=h.value)?void 0:e.querySelectorAll("body > *"))?t:[]).find((e=>"headlessui-portal-root"!==e.id&&e.contains(Vm(S))&&e instanceof HTMLElement)))?n:null}));Uy(N,O);let M=yi((()=>!!y.value||m.value)),P=yi((()=>{var e,t,n;return null!=(n=Array.from(null!=(t=null==(e=h.value)?void 0:e.querySelectorAll("[data-headlessui-portal]"))?t:[]).find((e=>e.contains(Vm(S))&&e instanceof HTMLElement)))?n:null}));Uy(P,M),function({type:e,enabled:t,element:n,onUpdate:o}){let r=qo(qy,(()=>{}));function i(...e){null==o||o(...e),r(...e)}lo((()=>{br(t,((t,o)=>{t?i(0,e,n):!0===o&&i(1,e,n)}),{immediate:!0,flush:"sync"})})),co((()=>{t.value&&i(1,e,n)})),Go(qy,i)}({type:"Dialog",enabled:yi((()=>0===g.value)),element:v,onUpdate:(e,t)=>{if("Dialog"===t)return Xm(e,{[Wy.Add]:()=>d.value+=1,[Wy.Remove]:()=>d.value-=1})}});let T=function({slot:e=Bt({}),name:t="Description",props:n={}}={}){let o=Bt([]);return Go(Ky,{register:function(e){return o.value.push(e),()=>{let t=o.value.indexOf(e);-1!==t&&o.value.splice(t,1)}},slot:e,name:t,props:n}),yi((()=>o.value.length>0?o.value.join(" "):void 0))}({name:"DialogDescription",slot:yi((()=>({open:p.value})))}),A=Bt(null),D={titleId:A,panelRef:Bt(null),dialogState:g,setTitleId(e){A.value!==e&&(A.value=e)},close(){t("close",!1)}};Go(ib,D);let I=yi((()=>!(!m.value||y.value)));!function(e,t,n=yi((()=>!0))){function o(o,r){if(!n.value||o.defaultPrevented)return;let i=r(o);if(null===i||!i.getRootNode().contains(i))return;let l=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e);for(let e of l){if(null===e)continue;let t=e instanceof HTMLElement?e:Vm(e);if(null!=t&&t.contains(i)||o.composed&&o.composedPath().includes(t))return}return!function(e,t=0){var n;return e!==(null==(n=Sy(e))?void 0:n.body)&&Xm(t,{0:()=>e.matches(Ey),1(){let t=e;for(;null!==t;){if(t.matches(Ey))return!0;t=t.parentElement}return!1}})}(i,Ny.Loose)&&-1!==i.tabIndex&&o.preventDefault(),t(o,i)}let r=Bt(null);Yy("pointerdown",(e=>{var t,o;n.value&&(r.value=(null==(o=null==(t=e.composedPath)?void 0:t.call(e))?void 0:o[0])||e.target)}),!0),Yy("mousedown",(e=>{var t,o;n.value&&(r.value=(null==(o=null==(t=e.composedPath)?void 0:t.call(e))?void 0:o[0])||e.target)}),!0),Yy("click",(e=>{By()||/Android/gi.test(window.navigator.userAgent)||r.value&&(o(e,(()=>r.value)),r.value=null)}),!0),Yy("touchend",(e=>o(e,(()=>e.target instanceof HTMLElement?e.target:null))),!0),my("blur",(e=>o(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}(_,((e,t)=>{e.preventDefault(),D.close(),vn((()=>null==t?void 0:t.focus()))}),I);let $=yi((()=>!(y.value||0!==g.value)));gy(null==(l=h.value)?void 0:l.defaultView,"keydown",(e=>{$.value&&(e.defaultPrevented||e.key===Zy.Escape&&(e.preventDefault(),e.stopPropagation(),D.close()))}));let z=yi((()=>!(C.value||0!==g.value||b)));return Fy(h,z,(e=>{var t;return{containers:[...null!=(t=e.containers)?t:[],_]}})),yr((e=>{if(0!==g.value)return;let t=Vm(v);if(!t)return;let n=new ResizeObserver((e=>{for(let t of e){let e=t.target.getBoundingClientRect();0===e.x&&0===e.y&&0===e.width&&0===e.height&&D.close()}}));n.observe(t),e((()=>n.disconnect()))})),()=>{let{open:t,initialFocus:r,...i}=e,l={...n,ref:v,id:a,role:c.value,"aria-modal":0===g.value||void 0,"aria-labelledby":A.value,"aria-describedby":T.value},s={open:0===g.value};return bi(Gy,{force:!0},(()=>[bi(eb,(()=>bi(ob,{target:v.value},(()=>bi(Gy,{force:!1},(()=>bi($y,{initialFocus:r,containers:_,features:m.value?Xm(k.value,{parent:$y.features.RestoreFocus,leaf:$y.features.All&~$y.features.FocusLock}):$y.features.None},(()=>bi(x,{},(()=>Wm({ourProps:l,theirProps:{...i,...n},slot:s,attrs:n,slots:o,visible:0===g.value,features:Gm.RenderStrategy|Gm.Static,name:"Dialog"}))))))))))),bi(E)]))}}}),ub=(Wn({name:"DialogOverlay",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var o;let r=null!=(o=e.id)?o:`headlessui-dialog-overlay-${Bm()}`,i=lb("DialogOverlay");function l(e){e.target===e.currentTarget&&(e.preventDefault(),e.stopPropagation(),i.close())}return()=>{let{...o}=e;return Wm({ourProps:{id:r,"aria-hidden":!0,onClick:l},theirProps:o,slot:{open:0===i.dialogState.value},attrs:t,slots:n,name:"DialogOverlay"})}}}),Wn({name:"DialogBackdrop",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},inheritAttrs:!1,setup(e,{attrs:t,slots:n,expose:o}){var r;let i=null!=(r=e.id)?r:`headlessui-dialog-backdrop-${Bm()}`,l=lb("DialogBackdrop"),a=Bt(null);return o({el:a,$el:a}),lo((()=>{if(null===l.panelRef.value)throw new Error("A <DialogBackdrop /> component is being used, but a <DialogPanel /> component is missing.")})),()=>{let{...o}=e,r={id:i,ref:a,"aria-hidden":!0};return bi(Gy,{force:!0},(()=>bi(eb,(()=>Wm({ourProps:r,theirProps:{...t,...o},slot:{open:0===l.dialogState.value},attrs:t,slots:n,name:"DialogBackdrop"})))))}}}),Wn({name:"DialogPanel",props:{as:{type:[Object,String],default:"div"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n,expose:o}){var r;let i=null!=(r=e.id)?r:`headlessui-dialog-panel-${Bm()}`,l=lb("DialogPanel");function a(e){e.stopPropagation()}return o({el:l.panelRef,$el:l.panelRef}),()=>{let{...o}=e;return Wm({ourProps:{id:i,ref:l.panelRef,onClick:a},theirProps:o,slot:{open:0===l.dialogState.value},attrs:t,slots:n,name:"DialogPanel"})}}}));function cb(e){return cb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},cb(e)}function db(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function fb(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?db(Object(n),!0).forEach((function(t){pb(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):db(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function pb(e,t,n){return(t=function(e){var t=function(e){if("object"!=cb(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=cb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==cb(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Wn({name:"DialogTitle",props:{as:{type:[Object,String],default:"h2"},id:{type:String,default:null}},setup(e,{attrs:t,slots:n}){var o;let r=null!=(o=e.id)?o:`headlessui-dialog-title-${Bm()}`,i=lb("DialogTitle");return lo((()=>{i.setTitleId(r),co((()=>i.setTitleId(null)))})),()=>{let{...o}=e;return Wm({ourProps:{id:r},theirProps:o,slot:{open:0===i.dialogState.value},attrs:t,slots:n,name:"DialogTitle"})}}});var vb={class:"flex flex-col gap-y-2"},hb={class:"flex relative justify-between bg-gray-100 p-1 py-3 group"},gb={class:"flex w-full items-center gap-x-3"},mb=["src"],yb={class:"absolute right-[127px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},bb=["src"],wb={class:"p-2 mx-2 bg-blue-100 rounded mb-2"},xb={class:"text-blue-600 text-semibold font-medium-xs"},_b={class:"p-2 mx-2 bg-blue-100 rounded mb-2"},Sb={class:"text-blue-600 text-semibold font-medium-xs"},Eb={class:"p-2 mx-2 bg-blue-100 rounded mb-2"},kb={class:"text-blue-600 text-semibold font-medium-xs"},Cb={class:"grid gap-y-3"},Ob={class:"fixed inset-0 z-10 w-screen overflow-hidden"},Nb={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},Mb={class:"space-y-4 overflow-y-auto h-[calc(100%-120px)] px-4"},Pb={class:"mt-2"},Tb={key:0,class:"text-red-500 text-sm"},Ab={class:"w-full"},Db={class:"mt-2 w-full"},Ib={key:0,class:"text-red-500 text-sm"},$b={class:"w-full"},zb={class:"mt-2 w-full"},jb={key:0,class:"text-red-500 text-sm"};const Bb={__name:"start-node",setup:function(e){var t=Bt(!1),n=Bt(1),o=Bt("leads"),r=Bt("On exact match"),i=Bt(""),l=Bt({}),a=Vd();function s(){l.value={},i.value||(l.value.trigger="This field is required."),o.value||(l.value.rel_type="This field is required."),n.value||(l.value.reply_type="This field is required."),0===Object.keys(l.value).length&&(t.value=!1)}var u=function(e){var t=e.target,l=t.name,s=t.value,u=e.target.options?e.target.options[e.target.selectedIndex].text:null;"rel_type"===l?o.value=s:"reply_type"===l&&(n.value=s,r.value=u),a.node.data=fb(fb({},a.node.data),{},{output:[{reply_type_text:r.value,reply_type:n.value,rel_type:o.value,trigger:i.value}]})},c=Tf(),d=c.nodes,f=c.addNodes;function p(){var e=a.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(d.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});f(r)}function v(){t.value=!0}function h(e){return"".concat(image_path).concat(e)}return br((function(){return a.node.data}),(function(e){var t,l,a,s;n.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.reply_type)||"",r.value=(null===(l=e.output)||void 0===l||null===(l=l[0])||void 0===l?void 0:l.reply_type_text)||"",i.value=(null===(a=e.output)||void 0===a||null===(a=a[0])||void 0===a?void 0:a.trigger)||"",o.value=(null===(s=e.output)||void 0===s||null===(s=s[0])||void 0===s?void 0:s.rel_type)||""}),{deep:!0,immediate:!0}),function(e,a){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:v},[Gr("div",vb,[Gr("div",hb,[Gr("div",gb,[Gr("img",{src:h("assets/images/start.png"),class:"h-4 w-4",alt:"Start icon"},null,8,mb),a[4]||(a[4]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md text-gray-500 font-medium"},"Start Bot Flow")],-1)),Gr("div",yb,[Gr("button",{onClick:p},[Gr("img",{src:h("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,bb)])])])]),Gr("div",wb,[Gr("p",xb,"Matching Type: "+ue(r.value),1)]),Gr("div",_b,[Gr("p",Sb,"Relation Type: "+ue(o.value),1)]),Gr("div",Eb,[Gr("p",kb,"Trigger Message: "+ue(i.value),1)]),Gr("div",Cb,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:a[3]||(a[3]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return a[5]||(a[5]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",Ob,[Gr("div",Nb,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[a[11]||(a[11]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"}," Configure Start Bot Flow ")],-1)),Gr("div",Mb,[Gr("div",null,[a[6]||(a[6]=Gr("label",{for:"keywords",class:"block text-gray-700"},[Gr("span",{class:"text-base text-red-500 mr-2"},"*"),Zr("Write down the comma saprated keywords for which the bot will be triggered")],-1)),Gr("div",Pb,[En(Gr("input",{id:"trigger",name:"trigger",required:"",type:"text","onUpdate:modelValue":a[0]||(a[0]=function(e){return i.value=e}),onInput:u,class:"block trigger w-full rounded-md border-0 py-2.5 pl-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Hello"},null,544),[[il,i.value]]),l.value.trigger?(jr(),Fr("p",Tb,ue(l.value.trigger),1)):Kr("v-if",!0)])]),Gr("div",Ab,[a[8]||(a[8]=Gr("label",{class:"block text-sm text-gray-700"},[Zr(" Relation type "),Gr("span",{class:"text-base text-red-500"},"*")],-1)),Gr("div",Db,[En(Gr("select",{id:"rel_type",name:"rel_type",required:"","onUpdate:modelValue":a[1]||(a[1]=function(e){return o.value=e}),onInput:u,class:"rel_type block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},a[7]||(a[7]=[Gr("option",{value:"leads"},"Leads",-1),Gr("option",{value:"contacts"},"Contacts",-1)]),544),[[ll,o.value]]),l.value.rel_type?(jr(),Fr("p",Ib,ue(l.value.rel_type),1)):Kr("v-if",!0)])]),Gr("div",$b,[a[10]||(a[10]=Gr("label",{class:"block text-sm text-gray-700"},[Zr("Matching type "),Gr("span",{class:"text-base text-red-500"},"*")],-1)),Gr("div",zb,[En(Gr("select",{id:"reply_type",required:"",name:"reply_type","onUpdate:modelValue":a[2]||(a[2]=function(e){return n.value=e}),onInput:u,class:"reply_type block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},a[9]||(a[9]=[Gr("option",{value:"1"},"On exact match",-1),Gr("option",{value:"2"},"When messsage contains",-1),Gr("option",{value:"3"},"When lead or client send the first messsage",-1)]),544),[[ll,n.value]]),l.value.reply_type?(jr(),Fr("p",jb,ue(l.value.reply_type),1)):Kr("v-if",!0)])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:s,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])]),qr(Ft(Jd),{class:"bg-black",type:"source",position:Ft(cc).Right},null,8,["position"])])}}},Lb=Bb;function Rb(e){return Rb="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Rb(e)}function Fb(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Vb(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Fb(Object(n),!0).forEach((function(t){Hb(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Fb(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Hb(e,t,n){return(t=function(e){var t=function(e){if("object"!=Rb(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Rb(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Rb(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ub={class:"flex flex-col gap-y-2"},Yb={class:"group relative flex justify-between bg-gray-100 p-1 py-2"},Xb={class:"flex w-full items-center gap-x-3"},Gb=["src"],qb={class:"absolute right-[100px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 delayInReply-500 ease-in-out bg-gray-100 px-2 pt-1 rounded"},Wb=["src"],Zb=["src"],Kb={key:0,class:"p-2 mx-2 bg-blue-100 rounded mb-2"},Jb={class:"text-blue-600 text-semibold font-medium-xs"},Qb={class:"grid gap-y-3"},ew={class:"fixed inset-0 z-10 w-screen overflow-hidden"},tw={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},nw={class:"space-y-4 overflow-y-auto h-[calc(100%-120px)] px-4"},ow={class:"mt-2"},rw=["data-custom_node"],iw={key:0,class:"text-red-500 text-sm"};const lw={__name:"text-message",setup:function(e){var t=Bt(!1),n=Vd(),o=Bt(""),r=Bt({}),i=Bt(""),l=Tf().toObject;function a(){r.value={},o.value||(r.value.reply_text="This field is required."),0===Object.keys(r.value).length&&(t.value=!1)}function s(){var e=l().nodes,o=l().edges,r=[],a=new Set;o.forEach((function(e){a.add(e.source),a.add(e.target)}));var s=a.has(n.id);s&&o.forEach((function(t){if(t.source===n.id||t.target===n.id){var o=t.source===n.id?t.target:t.source,i=e.find((function(e){return e.id===o}));if(i){var l,a=(null===(l=i.data)||void 0===l||null===(l=l.output)||void 0===l||null===(l=l[0])||void 0===l?void 0:l.rel_type)||"";a&&r.push(a)}}})),e.forEach((function(e){if("start"===e.type){var t,n=(null===(t=e.data)||void 0===t||null===(t=t.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.rel_type)||"";n&&r.push(n)}}));var u=r.every((function(e){return e===r[0]}));i.value=u&&s?r[0]:"",t.value=!0,setTimeout((function(){$(".custom_node").trigger("change")}),300)}var u=function(){n.node.data=Vb(Vb({},n.node.data),{},{output:[{reply_text:o.value}]})},c=Tf(),d=c.removeNodes,f=c.nodes,p=c.addNodes;function v(){d(n.id)}function h(){var e=n.node,t=e.type,o=e.position,r=e.label,i=(e.data,{id:(f.value.length+1).toString(),type:t,position:{x:o.x-100,y:o.y-100},label:r,data:{}});p(i)}function g(e){return"".concat(image_path).concat(e)}return br((function(){return n.node.data}),(function(e){var t;o.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.reply_text)||""}),{deep:!0,immediate:!0}),function(e,n){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:s},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",Ub,[Gr("div",Yb,[Gr("div",Xb,[Gr("img",{src:g("assets/images/icon_TextMessage.png"),class:"h-4 w-4",alt:"Start icon"},null,8,Gb),n[2]||(n[2]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md text-gray-500 font-medium"},"Text Message")],-1))]),Gr("div",qb,[Gr("button",{onClick:v},[Gr("img",{src:g("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,Wb)]),Gr("button",{onClick:h},[Gr("img",{src:g("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,Zb)])])]),o.value.length>0?(jr(),Fr("div",Kb,[Gr("p",Jb,ue(o.value),1)])):Kr("v-if",!0),Gr("div",Qb,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:n[1]||(n[1]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return n[3]||(n[3]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",ew,[Gr("div",tw,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[n[5]||(n[5]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"}," Configure Message ")],-1)),Gr("div",nw,[Gr("div",null,[n[4]||(n[4]=Gr("label",{class:"block text-sm text-gray-500"},"Text Message",-1)),Gr("div",ow,[En(Gr("textarea",{id:"message","data-custom_node":Ft(i),name:"message",rows:"3","onUpdate:modelValue":n[0]||(n[0]=function(e){return o.value=e}),onInput:u,class:"mentionable custom_node block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,40,rw),[[il,o.value]]),r.value.reply_text?(jr(),Fr("p",iw,ue(r.value.reply_text),1)):Kr("v-if",!0)])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:a,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},aw=lw;function sw(e){return sw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},sw(e)}function uw(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function cw(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?uw(Object(n),!0).forEach((function(t){dw(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):uw(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function dw(e,t,n){return(t=function(e){var t=function(e){if("object"!=sw(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=sw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==sw(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var fw={class:"flex flex-col gap-y-2"},pw={class:"group relative flex justify-between bg-gray-100 p-1 py-2"},vw={class:"flex w-full items-center gap-x-3"},hw=["src"],gw={class:"absolute right-[100px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},mw=["src"],yw=["src"],bw={key:0,class:"p-1 rounded"},ww=["src"],xw={class:"grid gap-y-3"},_w={class:"fixed inset-0 z-10 w-screen overflow-hidden"},Sw={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},Ew={class:"space-y-4 overflow-y-auto h-[calc(100%-120px)] px-4"},kw={class:"mt-2"},Cw=["accept","size"];const Ow={__name:"image-node",setup:function(e){var t=Bt(!1),n=Bt(null),o=Bt(null),r=$('input[name="file_url"]').val(),i=Bt(null);function l(e){var t=e.target;t.files&&t.files.length>0&&(i.value=t.files[0])}var a=JSON.parse(allowed_extension),s=a.image.extension,u=a.image.size;function c(){var e=new FormData;if(i.value){e.append("image",i.value),e.append("id",$('input[name="id"]').val()),e.append("csrf_token_name",$('input[name="csrf_token_name"]').val());var n=f.node.id;e.append("node_id",n),$.ajax({url:"".concat(admin_url,"whatsbot/bot_flow/store_file"),type:"POST",data:e,contentType:!1,processData:!1,success:function(e){e=JSON.parse(e),o.value=e.file_path+e.filename,0==e.status&&""!=e.message&&alert_float("danger",e.message),f.node.data=cw(cw({},f.node.data),{},{output:[{imageUrl:e.filename,nodeId:f.node.id}]}),t.value=!1},error:function(e,t,n){console.error("File upload failed:",t,n)}})}else t.value=!1}function d(){t.value=!0}var f=Vd(),p=Tf(),v=p.removeNodes,h=p.nodes,g=p.addNodes;function m(){v(f.id)}function y(){var e=f.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(h.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});g(r)}function b(e){return"".concat(image_path).concat(e)}return br((function(){return f.node.data}),(function(e){var t;n.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.imageUrl)||null}),{deep:!0,immediate:!0}),function(e,o){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:d},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",fw,[Gr("div",pw,[Gr("div",vw,[Gr("img",{src:b("assets/images/icon_ImageMessage.png"),class:"h-4 w-4",alt:"Start icon"},null,8,hw),o[1]||(o[1]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md font-medium text-gray-500"},"Image")],-1))]),Gr("div",gw,[Gr("button",{onClick:m},[Gr("img",{src:b("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,mw)]),Gr("button",{onClick:y},[Gr("img",{src:b("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,yw)])])]),n.value?(jr(),Fr("div",bw,[Gr("img",{src:Ft(r)+"/"+n.value,alt:"Preview",class:"w-full max-h-48 rounded-md object-cover"},null,8,ww)])):Kr("v-if",!0),Gr("div",xw,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:o[0]||(o[0]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return o[2]||(o[2]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",_w,[Gr("div",Sw,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[o[4]||(o[4]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"}," Configure Image ")],-1)),Gr("div",Ew,[Gr("div",null,[o[3]||(o[3]=Gr("label",{class:"block text-sm text-gray-500"},"Image",-1)),Gr("div",kw,[Gr("input",{id:"file-input",type:"file",name:"image",accept:Ft(s),size:Ft(u),onInput:l,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,40,Cw)])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:c,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},Nw=Ow;function Mw(e){return Mw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Mw(e)}function Pw(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Tw(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Pw(Object(n),!0).forEach((function(t){Aw(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Pw(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Aw(e,t,n){return(t=function(e){var t=function(e){if("object"!=Mw(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Mw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Mw(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Dw={class:"flex flex-col gap-y-2"},Iw={class:"group relative flex justify-between bg-gray-100 p-1 py-2"},$w={class:"flex w-full items-center gap-x-3"},zw=["src"],jw={class:"absolute right-[100px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},Bw=["src"],Lw=["src"],Rw={key:0,class:"p-1 rounded"},Fw=["src"],Vw={class:"grid gap-y-3"},Hw={class:"fixed inset-0 z-10 w-screen overflow-hidden"},Uw={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},Yw={class:"space-y-4 overflow-y-auto h-[calc(100%-120px)] px-4"},Xw={class:"mt-2"},Gw=["accept","size"];const qw={__name:"audio-message",setup:function(e){function t(e){return"".concat(image_path).concat(e)}var n=Bt(!1),o=Bt(null),r=Bt(null),i=Bt(null),l=$('input[name="file_url"]').val();function a(e){var t=e.target;t.files&&t.files.length>0&&(o.value=t.files[0])}var s=JSON.parse(allowed_extension),u=s.audio.extension,c=s.audio.size;function d(){var e=new FormData;if(o.value){e.append("audio",o.value),e.append("id",$('input[name="id"]').val()),e.append("csrf_token_name",$('input[name="csrf_token_name"]').val());var t=p.node.id;e.append("node_id",t),$.ajax({url:"".concat(admin_url,"whatsbot/bot_flow/store_file"),type:"POST",data:e,contentType:!1,processData:!1,success:function(e){e=JSON.parse(e),i.value=e.file_path+e.filename,0==e.status&&""!=e.message&&alert_float("danger",e.message),p.node.data=Tw(Tw({},p.node.data),{},{output:[{audioUrl:e.filename}]}),n.value=!1},error:function(e,t,n){console.error("File upload failed:",t,n)}})}else n.value=!1}function f(){n.value=!0}var p=Vd(),v=Tf(),h=v.removeNodes,g=v.nodes,m=v.addNodes;function y(){h(p.id)}function b(){var e=p.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(g.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});m(r)}return br((function(){return p.node.data}),(function(e){var t;r.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.audioUrl)||null}),{deep:!0,immediate:!0}),function(e,o){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:f},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",Dw,[Gr("div",Iw,[Gr("div",$w,[Gr("img",{src:t("assets/images/icon_AudioMessage.png"),class:"h-4 w-4",alt:"Start icon"},null,8,zw),o[1]||(o[1]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md font-medium text-gray-500"},"Audio")],-1))]),Gr("div",jw,[Gr("button",{onClick:y},[Gr("img",{src:t("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,Bw)]),Gr("button",{onClick:b},[Gr("img",{src:t("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,Lw)])])]),Kr(" Audio Preview "),r.value?(jr(),Fr("div",Rw,[Gr("audio",{src:Ft(l)+"/"+r.value,controls:"",class:"w-full rounded"},null,8,Fw)])):Kr("v-if",!0),Gr("div",Vw,[qr(Ft(hy),{as:"template",show:n.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:o[0]||(o[0]=function(e){return n.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return o[2]||(o[2]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",Hw,[Gr("div",Uw,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[o[4]||(o[4]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"}," Configure Audio ")],-1)),Gr("div",Yw,[Gr("div",null,[o[3]||(o[3]=Gr("label",{class:"block text-sm text-gray-500"},"Audio",-1)),Gr("div",Xw,[Gr("input",{type:"file",name:"audio",accept:Ft(u),size:Ft(c),onChange:a,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,40,Gw)])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:d,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},Ww=qw;function Zw(e){return Zw="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Zw(e)}function Kw(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Jw(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Kw(Object(n),!0).forEach((function(t){Qw(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Kw(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Qw(e,t,n){return(t=function(e){var t=function(e){if("object"!=Zw(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Zw(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Zw(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var ex={class:"flex flex-col gap-y-2"},tx={class:"group relative flex justify-between bg-gray-100 p-1 py-2"},nx={class:"flex w-full items-center gap-x-3"},ox=["src"],rx={class:"absolute right-[100px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},ix=["src"],lx=["src"],ax={key:0,class:"p-1 rounded"},sx=["src"],ux={class:"grid gap-y-3"},cx={class:"fixed inset-0 z-10 w-screen overflow-hidden"},dx={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},fx={class:"space-y-4 overflow-y-auto h-[calc(100%-120px)] px-4"},px={class:"mt-2"},vx=["accept","size"];const hx={__name:"video-node",setup:function(e){var t=Bt(!1),n=Bt(null),o=Bt(null),r=$('input[name="file_url"]').val(),i=Bt(null);function l(e){var t=e.target;t.files&&t.files.length>0&&(i.value=t.files[0])}var a=JSON.parse(allowed_extension),s=a.video.extension.split(", ").map((function(e){return".mp4"===e?"video/mp4":".3gp"===e?"video/3gpp":""})).join(", "),u=a.video.size;function c(){var e=new FormData;if(i.value){e.append("video",i.value),e.append("id",$('input[name="id"]').val()),e.append("csrf_token_name",$('input[name="csrf_token_name"]').val());var n=f.node.id;e.append("node_id",n),$.ajax({url:"".concat(admin_url,"whatsbot/bot_flow/store_file"),type:"POST",data:e,contentType:!1,processData:!1,success:function(e){e=JSON.parse(e),o.value=e.file_path+e.filename,0==e.status&&""!=e.message&&alert_float("danger",e.message),f.node.data=Jw(Jw({},f.node.data),{},{output:[{videoUrl:e.filename}]}),t.value=!1},error:function(e,t,n){console.error("File upload failed:",t,n)}})}else t.value=!1}function d(){t.value=!0}var f=Vd(),p=Tf(),v=p.removeNodes,h=p.nodes,g=p.addNodes;function m(){v(f.id)}function y(){var e=f.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(h.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});g(r)}function b(e){return"".concat(image_path).concat(e)}return br((function(){return f.node.data}),(function(e){var t;n.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.videoUrl)||null}),{deep:!0,immediate:!0}),function(e,o){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:d},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",ex,[Gr("div",tx,[Gr("div",nx,[Gr("img",{src:b("assets/images/icon_VideoMessage.png"),class:"h-4 w-4",alt:"Start icon"},null,8,ox),o[1]||(o[1]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md font-medium text-gray-500"},"Video")],-1))]),Gr("div",rx,[Gr("button",{onClick:m},[Gr("img",{src:b("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,ix)]),Gr("button",{onClick:y},[Gr("img",{src:b("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,lx)])])]),n.value?(jr(),Fr("div",ax,[Gr("video",{src:Ft(r)+"/"+n.value,controls:"",class:"w-full rounded-md max-h-48 object-cover"},null,8,sx)])):Kr("v-if",!0),Gr("div",ux,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:o[0]||(o[0]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return o[2]||(o[2]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",cx,[Gr("div",dx,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[o[4]||(o[4]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"}," Configure Video ")],-1)),Gr("div",fx,[Gr("div",null,[o[3]||(o[3]=Gr("label",{class:"block text-sm text-gray-500"},"Video",-1)),Gr("div",px,[Gr("input",{type:"file",name:"video",accept:Ft(s),size:Ft(u),onInput:l,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,40,vx)])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:c,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},gx=hx;function mx(e){return mx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},mx(e)}function yx(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function bx(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?yx(Object(n),!0).forEach((function(t){wx(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yx(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function wx(e,t,n){return(t=function(e){var t=function(e){if("object"!=mx(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=mx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==mx(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var xx={class:"flex flex-col gap-y-2"},_x={class:"group relative flex justify-between bg-gray-100 p-1 py-2"},Sx={class:"flex w-full items-center gap-x-3"},Ex=["src"],kx={class:"absolute right-[100px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},Cx=["src"],Ox=["src"],Nx={key:0,class:"m-3 p-2 bg-blue-500 border border-gray-200 rounded-lg shadow-md flex items-center gap-2"},Mx=["src"],Px={class:"text-white truncate w-full"},Tx={class:"grid gap-y-3"},Ax={class:"fixed inset-0 z-10 w-screen overflow-hidden"},Dx={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},Ix={class:"space-y-4 overflow-y-auto h-[calc(100%-120px)] px-4"},$x={class:"mt-2"},zx=["accept","size"];const jx={__name:"document-node",setup:function(e){var t=Bt(!1),n=Bt(null),o=$('input[name="file_url"]').val(),r=Bt(null);function i(e){var t=e.target;t.files&&t.files.length>0&&(r.value=t.files[0])}var l=JSON.parse(allowed_extension),a=l.document.extension,s=l.document.size;function u(){var e=new FormData;if(r.value){e.append("document",r.value),e.append("id",$('input[name="id"]').val()),e.append("csrf_token_name",$('input[name="csrf_token_name"]').val());var o=d.node.id;e.append("node_id",o),$.ajax({url:"".concat(admin_url,"whatsbot/bot_flow/store_file"),type:"POST",data:e,contentType:!1,processData:!1,success:function(e){e=JSON.parse(e),n.value=e.file_path+e.filename,0==e.status&&""!=e.message&&alert_float("danger",e.message),d.node.data=bx(bx({},d.node.data),{},{output:[{documentName:e.filename}]}),t.value=!1},error:function(e,t,n){console.error("File upload failed:",t,n)}})}else t.value=!1}function c(){t.value=!0}var d=Vd(),f=Tf(),p=f.removeNodes,v=f.nodes,h=f.addNodes;function g(){p(d.id)}function m(){var e=d.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(v.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});h(r)}function y(e){return"".concat(image_path).concat(e)}return br((function(){return d.node.data}),(function(e){var t;n.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.documentName)||null}),{deep:!0,immediate:!0}),function(e,r){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:c},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",xx,[Gr("div",_x,[Gr("div",Sx,[Gr("img",{src:y("assets/images/icon_Document.png"),class:"h-4 w-4",alt:"Start icon"},null,8,Ex),r[1]||(r[1]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md font-medium text-gray-500"},"Document")],-1))]),Gr("div",kx,[Gr("button",{onClick:g},[Gr("img",{src:y("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,Cx)]),Gr("button",{onClick:m},[Gr("img",{src:y("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,Ox)])])]),n.value?(jr(),Fr("div",Nx,[Gr("img",{src:y("assets/images/icon_Document.png"),alt:"Document Icon",class:"h-6 w-6"},null,8,Mx),Gr("span",Px,ue(n.value+"/"+Ft(o)),1)])):Kr("v-if",!0),Gr("div",Tx,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:r[0]||(r[0]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return r[2]||(r[2]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",Ax,[Gr("div",Dx,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[r[4]||(r[4]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"}," Configure Document ")],-1)),Gr("div",Ix,[Gr("div",null,[r[3]||(r[3]=Gr("label",{class:"block text-sm text-gray-500"},"Document",-1)),Gr("div",$x,[Gr("input",{type:"file",name:"document",accept:Ft(a),size:Ft(s),onInput:i,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,40,zx)])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:u,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},Bx=jx;function Lx(e){return Lx="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Lx(e)}function Rx(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Fx(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Rx(Object(n),!0).forEach((function(t){Vx(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Rx(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Vx(e,t,n){return(t=function(e){var t=function(e){if("object"!=Lx(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=Lx(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==Lx(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Hx={class:"flex flex-col gap-y-2"},Ux={class:"flex relative justify-between bg-gray-100 p-1 py-3 group"},Yx={class:"flex w-full items-center gap-x-3"},Xx={class:"absolute right-[127px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},Gx=["src"],qx=["src"],Wx={class:"p-2 mx-2 bg-blue-100 rounded mb-2"},Zx={class:"text-blue-600 text-semibold font-medium-xs"},Kx={class:"p-2 mx-2 bg-blue-100 rounded mb-2"},Jx={class:"text-blue-600 text-semibold font-medium-xs"},Qx={class:"grid gap-y-3"},e_={class:"fixed inset-0 z-10 w-screen overflow-hidden"},t_={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},n_={class:"space-y-4 overflow-y-auto h-[calc(100%-120px)] px-4"},o_={class:"w-full"},r_={class:"mt-2 w-full"},i_=["value"],l_={key:0,class:"text-red-500 text-sm"},a_={class:"mt-2"};const s_={__name:"ai-response",setup:function(e){var t=Bt(!1),n=Bt(""),o=Bt(""),r=Bt(""),i=Bt({}),l=Vd(),a=JSON.parse(personal_assistant);function s(){i.value={},n.value||(i.value.personal_assistants="This field is required."),0===Object.keys(i.value).length&&(t.value=!1)}var u=function(e){var t=e.target,i=t.name,a=t.value,s=e.target.options?e.target.options[e.target.selectedIndex].text:null;" personal_assistants"===i&&(n.value=a,o.value=s),l.node.data=Fx(Fx({},l.node.data),{},{output:[{personal_assistants_text:o.value,personal_assistants:n.value,ai_footer:r.value}]})},c=Tf(),d=c.removeNodes,f=c.nodes,p=c.addNodes;function v(){d(l.id)}function h(){var e=l.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(f.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});p(r)}function g(){t.value=!0}function m(e){return"".concat(image_path).concat(e)}return br((function(){return l.node.data}),(function(e){var t,i,l;n.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.personal_assistants)||"",o.value=(null===(i=e.output)||void 0===i||null===(i=i[0])||void 0===i?void 0:i.personal_assistants_text)||"",r.value=(null===(l=e.output)||void 0===l||null===(l=l[0])||void 0===l?void 0:l.ai_footer)||""}),{deep:!0,immediate:!0}),function(e,l){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:g},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",Hx,[Gr("div",Ux,[Gr("div",Yx,[l[3]||(l[3]=Gr("i",{class:"h-6 w-6",alt:"aiicon"},"🧠",-1)),l[4]||(l[4]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md text-gray-500 font-medium"},"AI Personal Assistant")],-1)),Gr("div",Xx,[Gr("button",{onClick:v},[Gr("img",{src:m("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,Gx)]),Gr("button",{onClick:h},[Gr("img",{src:m("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,qx)])])])]),Gr("div",Wx,[Gr("p",Zx,"Personal Assistant: "+ue(o.value),1)]),Gr("div",Kx,[Gr("p",Jx,"Footer: "+ue(r.value),1)]),Gr("div",Qx,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:l[2]||(l[2]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return l[5]||(l[5]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",e_,[Gr("div",t_,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[l[8]||(l[8]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"}," Configure AI Response ")],-1)),Gr("div",n_,[Gr("div",o_,[l[6]||(l[6]=Gr("label",{class:"block text-sm text-gray-700"},[Zr("Select Personal Assistant "),Gr("span",{class:"text-base text-red-500"},"*")],-1)),Gr("div",r_,[En(Gr("select",{id:" personal_assistants",required:"",name:" personal_assistants","onUpdate:modelValue":l[0]||(l[0]=function(e){return n.value=e}),onInput:u,class:"reply_type block w-full rounded-md border-0 py-1.5 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},[(jr(!0),Fr(Tr,null,_o(Ft(a),(function(e){return jr(),Fr("option",{key:e.id,value:e.id},ue(e.name),9,i_)})),128))],544),[[ll,n.value]]),i.value.personal_assistants?(jr(),Fr("p",l_,ue(i.value.personal_assistants),1)):Kr("v-if",!0)])]),Gr("div",null,[l[7]||(l[7]=Gr("label",{for:"keywords",class:"block text-gray-700"},"Footer Text",-1)),Gr("div",a_,[En(Gr("input",{id:"footerText",name:"footer",type:"text","onUpdate:modelValue":l[1]||(l[1]=function(e){return r.value=e}),onInput:u,class:"block trigger w-full rounded-md border-0 py-2.5 pl-2 text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Footer"},null,544),[[il,r.value]])])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:s,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},u_=s_;function c_(e){return c_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},c_(e)}function d_(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function f_(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?d_(Object(n),!0).forEach((function(t){p_(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):d_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function p_(e,t,n){return(t=function(e){var t=function(e){if("object"!=c_(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=c_(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==c_(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var v_={class:"flex flex-col gap-y-2"},h_={class:"group relative flex justify-between bg-gray-100 p-1 py-3"},g_={class:"flex w-full items-center gap-x-3"},m_=["src"],y_={class:"absolute right-[100px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},b_=["src"],w_=["src"],x_={key:0,class:"p-2 mx-2 bg-blue-100 rounded mb-2"},__={class:"text-blue-600 text-semibold font-medium-xs"},S_={class:"p-2 mx-2 bg-blue-100 rounded mb-2"},E_={class:"flex flex-col justify-around gap-2 items-end"},k_={class:"relative"},C_={class:"relative"},O_={class:"bg-blue-500 text-semibold font-medium-xs text-wrap text-white rounded px-1 py-1 whitespace-normal h-[30px] w-[150px] overflow-hidden"},N_={class:"relative"},M_={class:"bg-blue-500 text-wrap text-semibold font-medium-xs text-white rounded px-1 py-1 whitespace-normal h-[30px] w-[150px] overflow-hidden"},P_={class:"relative"},T_={class:"bg-blue-500 text-wrap text-semibold font-medium-xs text-white rounded px-1 py-1 whitespace-normal h-[30px] w-[150px] overflow-hidden"},A_={key:1,class:"py-2 mt-2 bg-blue-100 rounded mx-2"},D_={class:"pl-2 text-sm text-blue-600 text-semibold font-medium-xs"},I_={class:"grid gap-y-3"},$_={class:"fixed inset-0 z-10 w-screen overflow-hidden"},z_={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},j_={class:"space-y-2 overflow-y-auto h-[calc(100%-120px)] px-4"},B_={class:"mt-2"},L_={class:"mt-2"},R_=["data-custom_node"],F_={key:0,class:"text-red-500 text-sm"},V_={class:"mt-2"},H_={key:0,class:"text-red-500 text-sm"},U_={class:"mt-2"},Y_={key:0,class:"text-red-500 text-sm"},X_={class:"mt-2"},G_={key:0,class:"text-red-500 text-sm"},q_={class:"mt-2"};const W_={__name:"buttons-message",setup:function(e){var t=Bt(!1),n=Bt(""),o=Bt(""),r=Bt("Enter Button Text"),i=Bt("Enter Button Text"),l=Bt("Enter Button Text"),a=Bt(""),s=Bt({}),u=Bt(""),c=Tf().toObject;function d(){s.value={},o.value||(s.value.reply_text="This field is required."),r.value||(s.value.button1="This field is required."),i.value||(s.value.button2="This field is required."),l.value||(s.value.button3="This field is required."),0===Object.keys(s.value).length&&(t.value=!1)}function f(){var e=c().nodes,n=c().edges,o=[],r=new Set;n.forEach((function(e){r.add(e.source),r.add(e.target)}));var i=r.has(p.id);i&&n.forEach((function(t){if(t.source===p.id||t.target===p.id){var n=t.source===p.id?t.target:t.source,r=e.find((function(e){return e.id===n}));if(r){var i,l=(null===(i=r.data)||void 0===i||null===(i=i.output)||void 0===i||null===(i=i[0])||void 0===i?void 0:i.rel_type)||"";l&&o.push(l)}}})),e.forEach((function(e){if("start"===e.type){var t,n=(null===(t=e.data)||void 0===t||null===(t=t.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.rel_type)||"";n&&o.push(n)}}));var l=o.every((function(e){return e===o[0]}));u.value=l&&i?o[0]:"",t.value=!0,setTimeout((function(){$(".custom_node").trigger("change")}),300)}var p=Vd(),v=function(){p.node.data=f_(f_({},p.node.data),{},{output:[{bot_header:n.value,reply_text:o.value,button1:r.value,button2:i.value,button3:l.value,bot_footer:a.value}]})};br((function(){return p.node.data}),(function(e){var t,s,u,c,d,f;n.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.bot_header)||"",o.value=(null===(s=e.output)||void 0===s||null===(s=s[0])||void 0===s?void 0:s.reply_text)||"",r.value=(null===(u=e.output)||void 0===u||null===(u=u[0])||void 0===u?void 0:u.button1)||"",i.value=(null===(c=e.output)||void 0===c||null===(c=c[0])||void 0===c?void 0:c.button2)||"",l.value=(null===(d=e.output)||void 0===d||null===(d=d[0])||void 0===d?void 0:d.button3)||"",a.value=(null===(f=e.output)||void 0===f||null===(f=f[0])||void 0===f?void 0:f.bot_footer)||""}),{deep:!0,immediate:!0});var h=Tf(),g=h.removeNodes,m=h.nodes,y=h.addNodes;function b(){g(p.id)}function w(){var e=p.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(m.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});y(r)}function x(e){return"".concat(image_path).concat(e)}return function(e,c){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:f},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",v_,[Gr("div",h_,[Gr("div",g_,[Gr("img",{src:x("assets/images/icon_Button.png"),class:"h-4 w-4",alt:"Start icon"},null,8,m_),c[8]||(c[8]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-xs text-gray-500"},"Messages with Buttons")],-1))]),Gr("div",y_,[Gr("button",{onClick:b},[Gr("img",{src:x("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,b_)]),Gr("button",{onClick:w},[Gr("img",{src:x("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,w_)])])]),Gr("div",null,[n.value.length>0?(jr(),Fr("div",x_,[Gr("p",__,ue(n.value),1)])):Kr("v-if",!0),Gr("div",S_,[En(Gr("input",{class:"text-sm text-semibold font-medium-xs w-full bg-blue-100 text-blue-600",placeholder:"Enter Button Message","onUpdate:modelValue":c[0]||(c[0]=function(e){return o.value=e})},null,512),[[il,o.value]])]),Gr("div",E_,[Gr("div",k_,[c[9]||(c[9]=Gr("div",{class:"text-wrap text-black rounded py-1 whitespace-normal w-[60px] overflow-hidden"}," Always ",-1)),qr(Ft(Jd),{id:"source-4",type:"source",position:Ft(cc).Right,class:"absolute -right-[5px] top-1/2 transform -translate-y-1/2 bg-black"},null,8,["position"])]),Gr("div",C_,[Gr("div",O_,ue(r.value),1),qr(Ft(Jd),{id:"source-1",type:"source",position:Ft(cc).Right,class:"absolute -right-[5px] top-1/2 transform -translate-y-1/2 bg-black"},null,8,["position"])]),Gr("div",N_,[Gr("div",M_,ue(i.value),1),qr(Ft(Jd),{id:"source-2",type:"source",position:Ft(cc).Right,class:"absolute -right-[5px] top-1/2 transform -translate-y-1/2 bg-black"},null,8,["position"])]),Gr("div",P_,[Gr("div",T_,ue(l.value),1),qr(Ft(Jd),{id:"source-3",type:"source",position:Ft(cc).Right,class:"absolute -right-[5px] top-1/2 transform -translate-y-1/2 bg-black"},null,8,["position"])])]),Kr(" Footer Text "),a.value.length>0?(jr(),Fr("div",A_,[Gr("p",D_,ue(a.value),1)])):Kr("v-if",!0)]),Gr("div",I_,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:c[7]||(c[7]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return c[10]||(c[10]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",$_,[Gr("div",z_,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[c[17]||(c[17]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"},"Configure Button Message")],-1)),Gr("div",j_,[Gr("div",null,[c[11]||(c[11]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Header Text",-1)),Gr("div",B_,[En(Gr("input",{id:"bot_header",name:"bot_header",type:"text","onUpdate:modelValue":c[1]||(c[1]=function(e){return n.value=e}),onInput:v,maxlength:"60",class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Header Text"},null,544),[[il,n.value]])])]),Gr("div",null,[c[12]||(c[12]=Gr("label",{class:"block text-sm text-gray-500"},"Button Message",-1)),Gr("div",L_,[En(Gr("textarea",{id:"reply_text",name:"reply_text",rows:"2","onUpdate:modelValue":c[2]||(c[2]=function(e){return o.value=e}),onInput:v,placeholder:"Enter Button Message","data-custom_node":Ft(u),class:"mentionable custom_node block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"},null,40,R_),[[il,o.value]]),s.value.reply_text?(jr(),Fr("p",F_,ue(s.value.reply_text),1)):Kr("v-if",!0)])]),Gr("div",null,[c[13]||(c[13]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Button1 text",-1)),Gr("div",V_,[En(Gr("input",{id:"button1",name:"button1",type:"text","onUpdate:modelValue":c[3]||(c[3]=function(e){return r.value=e}),onInput:v,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Enter Button Text",maxlength:"20"},null,544),[[il,r.value]]),s.value.button1?(jr(),Fr("p",H_,ue(s.value.button1),1)):Kr("v-if",!0)])]),Gr("div",null,[c[14]||(c[14]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Button2 text",-1)),Gr("div",U_,[En(Gr("input",{id:"button2",name:"button2",type:"text","onUpdate:modelValue":c[4]||(c[4]=function(e){return i.value=e}),onInput:v,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Enter Button Text",maxlength:"20"},null,544),[[il,i.value]]),s.value.button2?(jr(),Fr("p",Y_,ue(s.value.button2),1)):Kr("v-if",!0)])]),Gr("div",null,[c[15]||(c[15]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Button3 text",-1)),Gr("div",X_,[En(Gr("input",{id:"button3",name:"button3",type:"text","onUpdate:modelValue":c[5]||(c[5]=function(e){return l.value=e}),onInput:v,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Enter Button Text",maxlength:"20"},null,544),[[il,l.value]]),s.value.button3?(jr(),Fr("p",G_,ue(s.value.button3),1)):Kr("v-if",!0)])]),Gr("div",null,[c[16]||(c[16]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Footer Text",-1)),Gr("div",q_,[En(Gr("input",{id:"bot_footer",name:"bot_footer",type:"text","onUpdate:modelValue":c[6]||(c[6]=function(e){return a.value=e}),onInput:v,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Footer Text",maxlength:"60"},null,544),[[il,a.value]])])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:d,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},Z_=W_;function K_(e){return K_="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},K_(e)}function J_(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function Q_(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?J_(Object(n),!0).forEach((function(t){eS(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):J_(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function eS(e,t,n){return(t=function(e){var t=function(e){if("object"!=K_(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=K_(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==K_(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var tS={class:"flex flex-col gap-y-2"},nS={class:"group relative flex justify-between bg-gray-100 p-1 py-3"},oS={class:"flex w-full items-center gap-x-3"},rS=["src"],iS={class:"absolute right-[100px] top-[-13px] flex -translate-y-1/2 transform items-center gap-x-2 rounded bg-gray-100 px-2 pt-1 opacity-0 transition-opacity delay-500 duration-200 ease-in-out group-hover:opacity-100"},lS=["src"],aS=["src"],sS={key:0,class:"p-2 mx-2 bg-blue-100 rounded mb-2"},uS={class:"text-blue-600 text-semibold font-medium-xs"},cS={class:"p-2 mx-2 bg-blue-100 rounded mb-2 text-wrap"},dS={class:"flex flex-col justify-around items-end"},fS={class:"bg-blue-500 mr-2 text-semibold font-medium-xs text-wrap text-white rounded px-3 py-1 whitespace-normal w-[150px] h-[30px] overflow-hidden"},pS={key:1,class:"p-2 mx-2 bg-blue-100 rounded mt-2 text-wrap"},vS={key:2,class:"p-2 mx-2 bg-blue-100 rounded mt-2"},hS={class:"text-blue-600 text-semibold font-medium-xs"},gS={class:"grid gap-y-3"},mS={class:"fixed inset-0 z-10 w-screen overflow-hidden"},yS={class:"absolute right-0 flex h-full w-full max-w-xl justify-end"},bS={class:"space-y-2 overflow-y-auto h-[calc(100%-120px)] px-4"},wS={class:"mt-2"},xS={class:"mt-2"},_S=["data-custom_node"],SS={key:0,class:"text-red-500 text-sm"},ES={class:"mt-2"},kS={key:0,class:"text-red-500 text-sm"},CS={class:"mt-2"},OS={key:0,class:"text-red-500 text-sm"},NS={class:"mt-2"};const MS={__name:"cta-node",setup:function(e){var t=Bt(!1),n=Bt(""),o=Bt(""),r=Bt(""),i=Bt(""),l=Bt(""),a=Bt({}),s=Bt(""),u=Tf().toObject;function c(){a.value={},o.value||(a.value.reply_text="This field is required."),r.value||(a.value.button_name="This field is required."),i.value||(a.value.button_url="This field is required."),0===Object.keys(a.value).length&&(t.value=!1)}function d(){var e=u().nodes,n=u().edges,o=[],r=new Set;n.forEach((function(e){r.add(e.source),r.add(e.target)}));var i=r.has(f.id);i&&n.forEach((function(t){if(t.source===f.id||t.target===f.id){var n=t.source===f.id?t.target:t.source,r=e.find((function(e){return e.id===n}));if(r){var i,l=(null===(i=r.data)||void 0===i||null===(i=i.output)||void 0===i||null===(i=i[0])||void 0===i?void 0:i.rel_type)||"";l&&o.push(l)}}})),e.forEach((function(e){if("start"===e.type){var t,n=(null===(t=e.data)||void 0===t||null===(t=t.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.rel_type)||"";n&&o.push(n)}}));var l=o.every((function(e){return e===o[0]}));s.value=l&&i?o[0]:"",t.value=!0,setTimeout((function(){$(".custom_node").trigger("change")}),300)}var f=Vd(),p=function(){f.node.data=Q_(Q_({},f.node.data),{},{output:[{bot_header:n.value,reply_text:o.value,button_name:r.value,button_url:i.value,bot_footer:l.value}]})};br((function(){return f.node.data}),(function(e){var t,a,s,u,c;n.value=(null===(t=e.output)||void 0===t||null===(t=t[0])||void 0===t?void 0:t.bot_header)||"",o.value=(null===(a=e.output)||void 0===a||null===(a=a[0])||void 0===a?void 0:a.reply_text)||"",r.value=(null===(s=e.output)||void 0===s||null===(s=s[0])||void 0===s?void 0:s.button_name)||"",i.value=(null===(u=e.output)||void 0===u||null===(u=u[0])||void 0===u?void 0:u.button_url)||"",l.value=(null===(c=e.output)||void 0===c||null===(c=c[0])||void 0===c?void 0:c.bot_footer)||""}),{deep:!0,immediate:!0});var v=Tf(),h=v.removeNodes,g=v.nodes,m=v.addNodes;function y(){h(f.id)}function b(){var e=f.node,t=e.type,n=e.position,o=e.label,r=(e.data,{id:(g.value.length+1).toString(),type:t,position:{x:n.x-100,y:n.y-100},label:o,data:{}});m(r)}function w(e){return"".concat(image_path).concat(e)}return function(e,u){return jr(),Fr("div",{class:"min-h-24 w-[280px] rounded-lg border-2 border-blue-100 bg-white shadow-md",onClick:d},[qr(Ft(Jd),{class:"bg-black",type:"target",position:Ft(cc).Left},null,8,["position"]),Gr("div",tS,[Gr("div",nS,[Gr("div",oS,[Gr("img",{src:w("assets/images/icon_cta.png"),class:"h-4 w-4",alt:"Start icon"},null,8,rS),u[8]||(u[8]=Gr("div",{class:"flex flex-col gap-y-1"},[Gr("p",{class:"text-md font-medium text-gray-500"},"CTA (call to action)")],-1))]),Gr("div",iS,[Gr("button",{onClick:y},[Gr("img",{src:w("assets/images/delete.png"),class:"h-4 w-4 cursor-pointer",alt:"Delete icon"},null,8,lS)]),Gr("button",{onClick:b},[Gr("img",{src:w("assets/images/copy.png"),class:"h-4 w-4 cursor-pointer",alt:"Copy icon"},null,8,aS)])])]),Gr("div",null,[n.value.length>0?(jr(),Fr("div",sS,[Gr("p",uS,ue(n.value),1)])):Kr("v-if",!0),Gr("div",cS,[En(Gr("input",{class:"text-sm text-semibold font-medium-xs w-full bg-blue-100 text-blue-600",placeholder:"Enter Button Text","onUpdate:modelValue":u[0]||(u[0]=function(e){return o.value=e})},null,512),[[il,o.value]])]),Gr("div",dS,[Gr("div",fS,ue(r.value),1)]),i.value.length>0?(jr(),Fr("div",pS,[En(Gr("input",{class:"text-sm w-full bg-blue-100 text-blue-600 text-semibold font-medium-xs","onUpdate:modelValue":u[1]||(u[1]=function(e){return i.value=e})},null,512),[[il,i.value]])])):Kr("v-if",!0),Kr(" Footer Text "),l.value.length>0?(jr(),Fr("div",vS,[Gr("p",hS,ue(l.value),1)])):Kr("v-if",!0)]),Gr("div",gS,[qr(Ft(hy),{as:"template",show:t.value},{default:Sn((function(){return[qr(Ft(sb),{class:"relative z-10",onClose:u[7]||(u[7]=function(e){return t.value=!1})},{default:Sn((function(){return[qr(Ft(py),{as:"template",enter:"ease-out duration-300","enter-from":"opacity-0","enter-to":"opacity-100",leave:"ease-in duration-200","leave-from":"opacity-100","leave-to":"opacity-0"},{default:Sn((function(){return u[9]||(u[9]=[Gr("div",{class:"fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"},null,-1)])})),_:1}),Gr("div",mS,[Gr("div",yS,[qr(Ft(py),{as:"template",enter:"transform transition ease-in-out duration-300","enter-from":"translate-x-full","enter-to":"translate-x-0",leave:"transform transition ease-in-out duration-300","leave-from":"translate-x-0","leave-to":"translate-x-full"},{default:Sn((function(){return[qr(Ft(ub),{class:"relative w-full max-w-lg transform overflow-hidden bg-white py-6 text-left shadow-xl transition-all"},{default:Sn((function(){return[u[15]||(u[15]=Gr("div",{class:"mb-4 flex items-center justify-between border-b pb-3 mleft15"},[Gr("h3",{class:"text-xl font-bold text-gray-900"},"Configure CTA (call to action) ")],-1)),Gr("div",bS,[Gr("div",null,[u[10]||(u[10]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Header Text",-1)),Gr("div",wS,[En(Gr("input",{id:"bot_header",name:"bot_header",type:"text","onUpdate:modelValue":u[2]||(u[2]=function(e){return n.value=e}),onInput:p,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Header Text",maxlength:"60"},null,544),[[il,n.value]])])]),Gr("div",null,[u[11]||(u[11]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Value Text",-1)),Gr("div",xS,[En(Gr("input",{id:"reply_text","data-custom_node":Ft(s),name:"reply_text",type:"text",required:"","onUpdate:modelValue":u[3]||(u[3]=function(e){return o.value=e}),onInput:p,class:"mentionable custom_node block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Value Text",maxlength:"1024"},null,40,_S),[[il,o.value]]),a.value.reply_text?(jr(),Fr("p",SS,ue(a.value.reply_text),1)):Kr("v-if",!0)])]),Gr("div",null,[u[12]||(u[12]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Button Text",-1)),Gr("div",ES,[En(Gr("input",{id:"button_name",name:"button_name",type:"text",required:"","onUpdate:modelValue":u[4]||(u[4]=function(e){return r.value=e}),onInput:p,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Button Text",maxlength:"20"},null,544),[[il,r.value]]),a.value.button_name?(jr(),Fr("p",kS,ue(a.value.button_name),1)):Kr("v-if",!0)])]),Gr("div",null,[u[13]||(u[13]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Button Link",-1)),Gr("div",CS,[En(Gr("input",{id:"button_url",name:"button_url",type:"url",required:"","onUpdate:modelValue":u[5]||(u[5]=function(e){return i.value=e}),onInput:p,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Button Link"},null,544),[[il,i.value]]),a.value.button_url?(jr(),Fr("p",OS,ue(a.value.button_url),1)):Kr("v-if",!0)])]),Gr("div",null,[u[14]||(u[14]=Gr("label",{for:"keywords",class:"block text-gray-500"},"Footer Text",-1)),Gr("div",NS,[En(Gr("input",{id:"bot_footer",name:"bot_footer",type:"text","onUpdate:modelValue":u[6]||(u[6]=function(e){return l.value=e}),onInput:p,class:"block w-full rounded-md border-0 py-2.5 pl-2 text-gray-500 shadow-sm ring-1 ring-inset ring-gray-300 placeholder:text-gray-400 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6",placeholder:"Footer Text"},null,544),[[il,l.value]])])])]),Gr("div",{class:"absolute inset-x-0 bottom-0 w-full p-6"},[Gr("button",{onClick:c,class:"text-medium w-full rounded bg-blue-600 px-4 py-2 font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Done ")])]})),_:1})]})),_:1})])])]})),_:1})]})),_:1},8,["show"])])])])}}},PS=MS;var TS={class:"edgebutton"},AS=["src"],DS={inheritAttrs:!1};function IS(e){return"".concat(image_path).concat(e)}const $S=Object.assign(DS,{__name:"CustomEdge",props:{id:{type:String,required:!0},sourceX:{type:Number,required:!0},sourceY:{type:Number,required:!0},targetX:{type:Number,required:!0},targetY:{type:Number,required:!0},sourcePosition:{type:String,required:!0},targetPosition:{type:String,required:!0},markerEnd:{type:String,required:!1},style:{type:Object,required:!1}},setup:function(e){var t=e,n=Tf().removeEdges,o=yi((function(){return mf(t)}));return function(t,r){return jr(),Fr(Tr,null,[Kr(" You can use the `BaseEdge` component to create your own custom edge more easily "),qr(Ft(ff),{id:e.id,style:ee(e.style),path:o.value[0],"marker-end":e.markerEnd},null,8,["id","style","path","marker-end"]),Kr(" Use the `EdgeLabelRenderer` to escape the SVG world of edges and render your own custom label in a `<div>` ctx "),qr(Ft(op),null,{default:Sn((function(){return[Gr("div",{style:ee({pointerEvents:"all",position:"absolute",transform:"translate(-50%, -50%) translate(".concat(o.value[1],"px,").concat(o.value[2],"px)")}),class:"nodrag nopan"},[Gr("div",{class:"edge__button_delete",onClick:r[0]||(r[0]=function(t){return Ft(n)(e.id)})},[Gr("button",TS,[Gr("img",{src:IS("/assets/images/delete.png"),class:"h-4 w-4 cursor-pointer m-1",alt:"Delete icon"},null,8,AS)])])],4)]})),_:1})],64)}}});var zS=n(170),jS={};jS.styleTagTransform=wp(),jS.setAttributes=gp(),jS.insert=vp().bind(null,"head"),jS.domAPI=fp(),jS.insertStyleElement=yp(),cp()(zS.A,jS),zS.A&&zS.A.locals&&zS.A.locals;const BS=$S;function LS(e){return LS="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},LS(e)}function RS(e){return function(e){if(Array.isArray(e))return VS(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||FS(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function FS(e,t){if(e){if("string"==typeof e)return VS(e,t);var n={}.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?VS(e,t):void 0}}function VS(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,o=Array(t);n<t;n++)o[n]=e[n];return o}function HS(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);t&&(o=o.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,o)}return n}function US(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?HS(Object(n),!0).forEach((function(t){YS(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):HS(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function YS(e,t,n){return(t=function(e){var t=function(e){if("object"!=LS(e)||!e)return e;var t=e[Symbol.toPrimitive];if(void 0!==t){var n=t.call(e,"string");if("object"!=LS(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==LS(t)?t:t+""}(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const XS={__name:"Main-canvas",setup:function(e){var t=Bt(),n={start:It(Lb),textMessage:It(aw),imageMessage:It(Nw),audioMessage:It(Ww),videoMessage:It(gx),document:It(Bx),cta:It(PS),buttonsMessage:It(Z_),aiResponse:It(u_)},o=empty($('input[name="flow_data"]').val())?Tf({nodes:[{id:"1",type:"start",label:"start",position:{x:23.066960749912937,y:39.792079803513516}},{id:"2",type:"textMessage",label:"textMessage",position:{x:399.33333333333337,y:30.66666666666667}}],edges:[{id:"vueflow__edge-1-2",type:"button",data:{text:"custom edge"},source:"1",target:"2",sourceHandle:null,targetHandle:null,animated:!0,label:"",sourceX:308.06696074991294,sourceY:87.79208488977653,targetX:394.33333333333337,targetY:78.66666666666667}],position:[149.21137331223406,138.41822750448935],zoom:.9896309330796706,viewport:{x:149.21137331223406,y:138.41822750448935,zoom:.9896309330796706}}):Tf(JSON.parse($('input[name="flow_data"]').val())),r=o.findNode,i=(o.nodes,o.addNodes),l=o.addEdges,a=o.project,s=o.vueFlowRef,u=o.onConnect,c=o.setNodes,d=o.setEdges,f=o.setViewport;function p(e){var t,n=null===(t=e.dataTransfer)||void 0===t?void 0:t.getData("application/vueflow");if("workflow"===n){var o=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var o,r,i,l,a=[],s=!0,u=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(o=i.call(n)).done)&&(a.push(o.value),a.length!==t);s=!0);}catch(e){u=!0,r=e}finally{try{if(!s&&null!=n.return&&(l=n.return(),Object(l)!==l))return}finally{if(u)throw r}}return a}}(e,t)||FS(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(y,2),l=o[0],u=void 0===l?0:l,p=o[1],v=void 0===p?0:p;return c((function(e){return[].concat(RS(e),RS(newNodes))})),d((function(e){return[].concat(RS(e),RS(newEdges))})),void f({x:u,y:v,zoom:zoom||0})}var h=s.value.getBoundingClientRect(),g=h.left,m=h.top,y=a({x:e.clientX-g,y:e.clientY-m}),b={id:"node-".concat(Date.now()),type:n,position:y,label:"".concat(n," node"),data:{title:n}};i([b]),vn((function(){var e=r(b.id),t=br((function(){return e.dimensions}),(function(n){n.width>0&&n.height>0&&e&&(e.position={x:e.position.x-e.dimensions.width/2,y:e.position.y-e.dimensions.height/2},t())}),{deep:!0,flush:"post"})}))}function v(e){e.preventDefault(),e.dataTransfer&&(e.dataTransfer.dropEffect="move")}return vn((function(){if(empty($('input[name="vueflow"]').val()))f({x:149.21137331223406,y:138.41822750448935,zoom:.9896309330796706});else{var e=JSON.parse($('input[name="vueflow"]').val()),t=e.edges,n=e.viewport;!function(e){var t=e.map((function(e){return US(US({},e),{},{animated:!0})}));d(t)}(t),n&&f(n)}})),u((function(e){l(US(US({},e),{},{animated:!0,type:"button"}))})),function(e,o){return jr(),Fr("div",{class:"relative h-full w-full",id:"main-canvas",onDrop:p,onDragover:v},[qr(Ft(ap),{modelValue:t.value,"onUpdate:modelValue":o[0]||(o[0]=function(e){return t.value=e}),"node-types":n,class:"basic-flow","default-viewport":{zoom:1.5},"min-zoom":.2,"max-zoom":4,edges:e.edges,edgeTypes:e.edgeTypes,"connection-mode":Ft(pc).Strict},{"edge-button":Sn((function(e){return[qr(BS,{id:e.id,"source-x":e.sourceX,"source-y":e.sourceY,"target-x":e.targetX,"target-y":e.targetY,"source-position":e.sourcePosition,"target-position":e.targetPosition,"marker-end":e.markerEnd,style:ee(e.style)},null,8,["id","source-x","source-y","target-x","target-y","source-position","target-position","marker-end","style"])]})),default:Sn((function(){return[qr(Ft(Qp),{position:"top-right"}),qr(Ft($m),{pannable:"",zoomable:"",nodeColor:"#1e88e5",nodeStrokeColor:"#408B9A",nodeClassName:"custom-node-class"}),qr(Ft($p),{"pattern-color":"#aaa",gap:16})]})),_:1},8,["modelValue","edges","edgeTypes","connection-mode"])],32)}}};var GS=n(490),qS={};qS.styleTagTransform=wp(),qS.setAttributes=gp(),qS.insert=vp().bind(null,"head"),qS.domAPI=fp(),qS.insertStyleElement=yp(),cp()(GS.A,qS),GS.A&&GS.A.locals&&GS.A.locals;const WS=XS;var ZS={class:"absolute bottom-0 left-0 right-0 top-0"},KS={class:"relative flex h-full w-full flex-col"},JS={class:"relative flex h-full w-full flex-1"},QS={class:"w-[300px] bg-slate-50"},eE={class:"grid gap-4 mx-6 mb-6"},tE={class:"flex items-center gap-x-2"},nE=["src"],oE={class:"flex items-center gap-x-2"},rE=["src"],iE={class:"flex items-center gap-x-2"},lE=["src"],aE={class:"flex items-center gap-x-2"},sE=["src"],uE={class:"flex items-center gap-x-2"},cE=["src"],dE={class:"flex items-center gap-x-2"},fE=["src"],pE={class:"flex items-center gap-x-2"},vE=["src"],hE={class:"relative h-full flex-1 overflow-hidden border border-indigo-00"};const gE={__name:"VueFlowComponent",setup:function(e){function t(e,t){e.dataTransfer&&(e.dataTransfer.setData("application/vueflow",t),e.dataTransfer.effectAllowed="move")}var n=Tf().toObject;function o(){if(i()){var e=JSON.stringify(n());$('input[name="flow_data"]').val(e),$('input[name="is_validate"]').val("1")}}function r(e){return"".concat(image_path).concat(e)}var i=function(){var e=n(),t=e.nodes||[],o=e.edges||[],r=new Set,i=!0,l="";o.forEach((function(e){r.add(e.source),r.add(e.target)}));var a=t.every((function(e){return r.has(e.id)}));return a||(alert_float("danger","Ensure that the node connection set up correctly"),i=!1),t.forEach((function(e){e.data&&e.data.output&&0!==e.data.output.length?e.data.output.forEach((function(t,n){Object.keys(t).forEach((function(o){"bot_header"!==o&&"bot_footer"!==o&&"ai_footer"!==o&&(t[o]&&""!==t[o].trim()||(l+='Field "'.concat(o,'" is required in node ').concat(e.label||e.id,", output #").concat(n+1,".<br/>"),i=!1))}))})):(l+='Node "'.concat(e.label||e.id,'" must have an required field.<br/>'),i=!1)})),l&&alert_float("danger",l),i&&a};return function(e,n){return jr(),Fr("div",ZS,[Gr("div",KS,[Gr("main",JS,[Gr("div",QS,[n[16]||(n[16]=Gr("h2",{class:"font-semibold mx-6 my-4"},"Available Components",-1)),Gr("div",eE,[Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[0]||(n[0]=function(e){return t(e,"textMessage")})},[Gr("div",tE,[Gr("img",{src:r("assets/images/icon_TextMessage.png"),class:"h-6 w-6",alt:"Text Message icon"},null,8,nE),n[8]||(n[8]=Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"Text Message")],-1))])],32),Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[1]||(n[1]=function(e){return t(e,"imageMessage")})},[Gr("div",oE,[Gr("img",{src:r("assets/images/icon_ImageMessage.png"),class:"h-6 w-6",alt:"Image Message icon"},null,8,rE),n[9]||(n[9]=Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"Image Message")],-1))])],32),Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[2]||(n[2]=function(e){return t(e,"audioMessage")})},[Gr("div",iE,[Gr("img",{src:r("assets/images/icon_AudioMessage.png"),class:"h-6 w-6",alt:"Audio Message icon"},null,8,lE),n[10]||(n[10]=Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"Audio Message")],-1))])],32),Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[3]||(n[3]=function(e){return t(e,"videoMessage")})},[Gr("div",aE,[Gr("img",{src:r("assets/images/icon_VideoMessage.png"),class:"h-6 w-6",alt:"Video Message icon"},null,8,sE),n[11]||(n[11]=Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"Video Message")],-1))])],32),Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[4]||(n[4]=function(e){return t(e,"document")})},[Gr("div",uE,[Gr("img",{src:r("assets/images/icon_Document.png"),class:"h-6 w-6",alt:"Document icon"},null,8,cE),n[12]||(n[12]=Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"Document")],-1))])],32),Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[5]||(n[5]=function(e){return t(e,"cta")})},[Gr("div",dE,[Gr("img",{src:r("assets/images/icon_cta.png"),class:"h-6 w-6",alt:"Document icon"},null,8,fE),n[13]||(n[13]=Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"CTA (call to action)")],-1))])],32),Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[6]||(n[6]=function(e){return t(e,"buttonsMessage")})},[Gr("div",pE,[Gr("img",{src:r("assets/images/icon_Button.png"),class:"h-6 w-6",alt:"Button Message icon"},null,8,vE),n[14]||(n[14]=Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"Button Message")],-1))])],32),Gr("div",{class:"cursor-grab rounded-md bg-white p-2 shadow-md",draggable:!0,onDragstart:n[7]||(n[7]=function(e){return t(e,"aiResponse")})},n[15]||(n[15]=[Gr("div",{class:"flex items-center gap-x-2"},[Gr("i",{class:"h-6 w-6",alt:"aiicon"},"🧠"),Gr("div",null,[Gr("span",{class:"text-xs font-semibold"},"AI Personal Assistant")])],-1)]),32)]),Gr("div",{class:"w-full px-2 mt-2"},[Gr("button",{onClick:o,id:"save_btn",class:"text-xs w-full font-semibold rounded bg-blue-600 px-4 py-2 text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"}," Save ")])]),Gr("div",hE,[qr(WS)])])])])}}};var mE=((...e)=>{let t=(d||(d=dr(ul))).createApp(...e),{mount:n}=t;return t.mount=e=>{let o=function(e){return P(e)?document.querySelector(e):e}(e);if(!o)return;let r=t._component;M(r)||r.render||r.template||(r.template=o.innerHTML),1===o.nodeType&&(o.textContent="");let i=n(o,!1,function(e){return e instanceof SVGElement?"svg":"function"==typeof MathMLElement&&e instanceof MathMLElement?"mathml":void 0}(o));return o instanceof Element&&(o.removeAttribute("v-cloak"),o.setAttribute("data-v-app","")),i},t})(gE);document.querySelector("#new-vue-id")&&(mE.component("VueFlow",ap),mE.mount("#new-vue-id"))})();