<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Conectar Conta';
$lang['connect_whatsapp_business'] = 'Conectar Whatsapp Business';
$lang['campaigning'] = 'Campanha';
$lang['business_account_id_description'] = 'Seu ID de Conta Comercial do WhatsApp (WABA)';
$lang['access_token_description'] = 'Seu Token de Acesso de Usuário após se inscrever em uma conta no Portal de Desenvolvedores do Facebook';
$lang['whatsapp_business_account_id'] = 'ID da Conta Comercial do Whatsapp';
$lang['whatsapp_access_token'] = 'Token de Acesso do Whatsapp';
$lang['webhook_callback_url'] = 'URL de Callback do Webhook';
$lang['verify_token'] = 'Verificar Token';
$lang['connect'] = 'Conectar';
$lang['whatsapp'] = 'Whatsapp';
$lang['one_click_account_connection'] = 'Conexão de Conta com Um Clique';
$lang['connect_your_whatsapp_account'] = 'Conecte sua Conta do Whatsapp';
$lang['copy'] = 'Copiar';
$lang['copied'] = 'Copiado!!';
$lang['disconnect'] = 'Desconectar';
$lang['number'] = 'Número';
$lang['number_id'] = 'ID do Número';
$lang['quality'] = 'Qualidade';
$lang['status'] = 'Status';
$lang['business_account_id'] = 'ID da Conta Comercial';
$lang['permissions'] = 'Permissões';
$lang['phone_number_id_description'] = 'ID do número de telefone conectado à API do WhatsApp Business. Se você não tiver certeza sobre isso, pode usar uma solicitação GET Phone Number ID para recuperá-lo da API do WhatsApp (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'ID do Número do Telefone Registrado no WhatsApp';
$lang['update_details'] = 'Atualizar Detalhes';

$lang['bots'] = 'Bots';
$lang['bots_management'] = 'Gerenciamento de Bots';
$lang['create_template_base_bot'] = 'Criar bot baseado em modelo';
$lang['create_message_bot'] = 'Criar bot de mensagem';
$lang['type'] = 'Tipo';
$lang['message_bot'] = 'Bot de Mensagem';
$lang['new_template_bot'] = 'Novo Bot de Modelo';
$lang['new_message_bot'] = 'Novo Bot de Mensagem';
$lang['bot_name'] = 'Nome do Bot';
$lang['reply_text'] = 'Texto de resposta <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Texto que será enviado ao lead ou contato. Você também pode usar {companyname}, {crm_url} ou quaisquer outros campos de mesclagem personalizados do lead ou contato, ou usar o sinal \'@\' para encontrar os campos de mesclagem disponíveis" data-placement="bottom"></i> <span class="text-muted">(Máximo permitido de caracteres deve ser 1024)</span>';
$lang['reply_type'] = 'Tipo de resposta';
$lang['trigger'] = 'Gatilho';
$lang['header'] = 'Cabeçalho';
$lang['footer_bot'] = 'Rodapé <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Opção 1: Bot com botões de resposta';
$lang['bot_with_button_link'] = 'Opção 2: Bot com link de botão - URL de CTA';
$lang['button1'] = 'Botão1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID do Botão1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 256" data-placement="bottom"></i>';
$lang['button2'] = 'Botão2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID do Botão2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 256" data-placement="bottom"></i>';
$lang['button3'] = 'Botão3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID do Botão3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Nome do Botão <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Máximo permitido de caracteres deve ser 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Link do Botão';
$lang['enter_name'] = 'Digite o Nome';
$lang['select_reply_type'] = 'Selecione o tipo de resposta';
$lang['enter_bot_reply_trigger'] = 'Digite o gatilho de resposta do bot';
$lang['enter_header'] = 'Digite o cabeçalho';
$lang['enter_footer'] = 'Digite o rodapé';
$lang['enter_button1'] = 'Digite o botão1';
$lang['enter_button1_id'] = 'Digite o ID do botão1';
$lang['enter_button2'] = 'Digite o botão2';
$lang['enter_button2_id'] = 'Digite o ID do botão2';
$lang['enter_button3'] = 'Digite o botão3';
$lang['enter_button3_id'] = 'Digite o ID do botão3';
$lang['enter_button_name'] = 'Digite o nome do botão';
$lang['enter_button_url'] = 'Digite a URL do botão';
$lang['on_exact_match'] = 'Bot de resposta: Em correspondência exata';
$lang['when_message_contains'] = 'Bot de resposta: Quando a mensagem contém';
$lang['when_client_send_the_first_message'] = 'Resposta de boas-vindas - quando o lead ou cliente enviar a primeira mensagem';
$lang['bot_create_successfully'] = 'Bot criado com sucesso';
$lang['bot_update_successfully'] = 'Bot atualizado com sucesso';
$lang['bot_deleted_successfully'] = 'Bot excluído com sucesso';
$lang['templates'] = 'Modelos';
$lang['template_data_loaded'] = 'Modelos carregados com sucesso';
$lang['load_templates'] = 'Carregar Modelos';
$lang['template_management'] = 'Gerenciamento de Modelos';

// campaigns
$lang['campaign'] = 'Campanha';
$lang['campaigns'] = 'Campanhas';
$lang['send_new_campaign'] = 'Enviar Nova Campanha';
$lang['campaign_name'] = 'Nome da Campanha';
$lang['template'] = 'Modelo';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Por cliente, com base no fuso horário de contato" data-placement="left"></i>Hora de Envio Programada';
$lang['scheduled_time_description'] = 'Por cliente, com base no fuso horário de contato';
$lang['ignore_scheduled_time_and_send_now'] = 'Ignorar hora programada e enviar agora';
$lang['template'] = 'Modelo';
$lang['leads'] = 'Leads';
$lang['delivered_to'] = 'Entregue a';
$lang['read_by'] = 'Lido por';
$lang['variables'] = 'Variáveis';
$lang['body'] = 'Corpo';
$lang['variable'] = 'Variável';
$lang['match_with_selected_field'] = 'Correspondência com um campo selecionado';
$lang['preview'] = 'Visualizar';
$lang['send_campaign'] = 'Enviar campanha';
$lang['send_to'] = 'Enviar para';
$lang['send_campaign'] = 'Enviar Campanha';
$lang['view_campaign'] = 'Ver Campanha';
$lang['campaign_daily_task'] = 'Tarefa diária da campanha';
$lang['back'] = 'Voltar';
$lang['phone'] = 'Telefone';
$lang['message'] = 'Mensagem';
$lang['currently_type_not_supported'] = 'Atualmente, o tipo de modelo <strong> %s </strong> não é suportado!';
$lang['of_your'] = 'de seu ';
$lang['contacts'] = 'Contatos';
$lang['select_all_leads'] = 'Selecionar todos os Leads';
$lang['select_all_note_leads'] = 'Se você selecionar isso, todos os leads futuros estão incluídos nesta campanha.';
$lang['select_all_note_contacts'] = 'Se você selecionar isso, todos os contatos futuros estão incluídos nesta campanha.';

$lang['verified_name'] = 'Nome Verificado';
$lang['mark_as_default'] = 'Marcar como padrão';
$lang['default_number_updated'] = 'Número de telefone padrão atualizado com sucesso';
$lang['currently_using_this_number'] = 'Atualmente usando este número';
$lang['leads'] = 'Leads';
$lang['pause_campaign'] = 'Pausar Campanha';
$lang['resume_campaign'] = 'Retomar Campanha';
$lang['campaign_resumed'] = 'Campanha retomada';
$lang['campaign_paused'] = 'Campanha pausada';

//Template
$lang['body_data'] = 'Dados do Corpo';
$lang['category'] = 'Categoria';

// Template bot
$lang['create_new_template_bot'] = 'Criar novo bot modelo';
$lang['template_bot'] = 'Bot Modelo';
$lang['variables'] = 'Variáveis';
$lang['preview'] = 'Visualizar';
$lang['template'] = 'Modelo';
$lang['bot_content_1'] = 'Esta mensagem será enviada ao contato assim que a regra de gatilho for atendida na mensagem enviada pelo contato.';
$lang['save_bot'] = 'Salvar bot';
$lang['please_select_template'] = 'Por favor, selecione um modelo';
$lang['use_manually_define_value'] = 'Usar valor definido manualmente';
$lang['merge_fields'] = 'Campos de Mesclagem';
$lang['template_bot_create_successfully'] = 'Bot modelo criado com sucesso';
$lang['template_bot_update_successfully'] = 'Bot modelo atualizado com sucesso';
$lang['text_bot'] = 'Bot de Texto';
$lang['option_2_bot_with_link'] = 'Opção 2: Bot com link de botão - URL de Chamada para Ação (CTA)';
$lang['option_3_file'] = 'Opção 3: Bot com arquivo';
// Bot settings
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Mensagem enviada quando um atraso na resposta é esperado';
$lang['bot_delay_response_placeholder'] = 'Me dê um momento, terei a resposta em breve';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Tipo de Relação';
$lang['select_all'] = 'Selecionar todos';
$lang['total'] = 'Total';
$lang['merge_field_note'] = 'Use o sinal \'@\' para adicionar campos de mesclagem.';
$lang['send_to_all'] = 'Enviar para Todos ';
$lang['or'] = 'OU';

$lang['convert_whatsapp_message_to_lead'] = 'Adquirir Novo Lead Automaticamente (converter novas mensagens do whatsapp em lead)';
$lang['leads_status'] = 'Status do Lead';
$lang['leads_assigned'] = 'Lead atribuído';
$lang['whatsapp_auto_lead'] = 'Lead Automático do Whatsapp';
$lang['webhooks_label'] = 'Dados recebidos do Whatsapp serão reencaminhados para';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Habilitar Reenvio de WebHooks';
$lang['chat'] = 'Chat';
$lang['black_listed_phone_numbers'] = 'Números de telefone bloqueados';
$lang['sent_status'] = 'Status Enviado';

$lang['active'] = 'Ativo';
$lang['approved'] = 'Aprovado';
$lang['this_month'] = 'este mês';
$lang['open_chats'] = 'Chats Abertos';
$lang['resolved_conversations'] = 'Conversas Resolvidas';
$lang['messages_sent'] = 'Mensagens enviadas';
$lang['account_connected'] = 'Conta conectada';
$lang['account_disconnected'] = 'Conta desconectada';
$lang['webhook_verify_token'] = 'Token de verificação do Webhook';
// Chat integration
$lang['chat_message_note'] = 'A mensagem será enviada em breve. Por favor, note que, se for um novo contato, ele não aparecerá nesta lista até que o contato comece a interagir com você!';

$lang['activity_log'] = 'Registro de Atividade';
$lang['whatsapp_logs'] = 'Registros do Whatsapp';
$lang['response_code'] = 'Código de Resposta';
$lang['recorded_on'] = 'Registrado em';

$lang['request_details'] = 'Detalhes da Solicitação';
$lang['raw_content'] = 'Conteúdo Bruto';
$lang['headers'] = 'Cabeçalhos';
$lang['format_type'] = 'Tipo de Formato';

// Permission section
$lang['show_campaign'] = 'Mostrar campanha';
$lang['clear_log'] = 'Limpar Log';
$lang['log_activity'] = 'Registrar Atividade';
$lang['load_template'] = 'Carregar Modelo';

$lang['action'] = 'Ação';
$lang['total_parameters'] = 'Total de Parâmetros';
$lang['template_name'] = 'Nome do Modelo';
$lang['log_cleared_successfully'] = 'Log limpo com sucesso';
$lang['whatsbot_stats'] = 'Estatísticas do WhatsBot';

$lang['not_found_or_deleted'] = 'Não encontrado ou excluído';
$lang['response'] = 'Resposta';

$lang['select_image'] = 'Selecionar imagem';
$lang['image'] = 'Imagem';
$lang['image_deleted_successfully'] = 'Imagem excluída com sucesso';
$lang['whatsbot_settings'] = 'Configurações do Whatsbot';
$lang['maximum_file_size_should_be'] = 'O tamanho máximo do arquivo deve ser ';
$lang['allowed_file_types'] = 'Tipos de arquivo permitidos: ';

$lang['send_image'] = 'Enviar Imagem';
$lang['send_video'] = 'Enviar Vídeo';
$lang['send_document'] = 'Enviar Documento';
$lang['record_audio'] = 'Gravar Áudio';
$lang['chat_media_info'] = 'Mais informações sobre Tipos de Conteúdo Suportados & Tamanho do Mídia Pós-Processamento';
$lang['help'] = 'Ajuda';


// v1.1.0
$lang['clone'] = 'Clonar';
$lang['bot_clone_successfully'] = 'Bot clonado com sucesso';
$lang['all_chat'] = 'Todos os Chats';
$lang['from'] = 'De:';
$lang['phone_no'] = 'Número de Telefone:';
$lang['supportagent'] = 'Agente de Suporte';
$lang['assign_chat_permission_to_support_agent'] = 'Atribuir permissão de chat ao agente de suporte apenas';
$lang['enable_whatsapp_notification_sound'] = 'Ativar som de notificação de chat do WhatsApp';
$lang['notification_sound'] = 'Som de Notificação';
$lang['trigger_keyword'] = 'Palavra-chave de Gatilho';
$lang['modal_title'] = 'Selecionar Agente de Suporte';
$lang['close_btn'] = 'Fechar';
$lang['save_btn'] = 'Salvar';
$lang['support_agent'] = 'Agente de Suporte';
$lang['change_support_agent'] = 'Mudar Agente de Suporte';
$lang['replay_message'] = 'Você não pode enviar mensagem, 24 horas se passaram.';
$lang['support_agent_note'] = '<strong>Nota: </strong>Quando você ativa a função do agente de suporte, o responsável pelo lead será automaticamente atribuído ao chat. Os administradores também podem atribuir um novo agente na página de chat.';
$lang['permission_bot_clone'] = 'Clonar Bot';
$lang['remove_chat'] = 'Remover Chat';
$lang['default_message_on_no_match'] = 'Resposta Padrão - se alguma palavra-chave não corresponder';
$lang['default_message_note'] = '<strong>Nota: </strong>Ativar esta opção aumentará a carga do seu webhook. Para mais informações, visite este <a href="https://audiencetargetcrm.online/" target="_blank">link</a>.';

$lang['whatsbot_connect_account'] = 'Conectar Conta Whatsbot';
$lang['whatsbot_message_bot'] = 'Bot de Mensagem Whatsbot';
$lang['whatsbot_template_bot'] = 'Bot de Modelo Whatsbot';
$lang['whatsbot_template'] = 'Modelo Whatsbot';
$lang['whatsbot_campaigns'] = 'Campanhas Whatsbot';
$lang['whatsbot_chat'] = 'Chat Whatsbot';
$lang['whatsbot_log_activity'] = 'Registrar Atividade Whatsbot';
$lang['message_templates_not_exists_note'] = 'Permissão de modelo meta ausente. Por favor, ative-a em sua conta Meta';

// v1.2.0
$lang['ai_prompt'] = 'Prompt de IA';
$lang['ai_prompt_note'] = 'Para prompts de IA, por favor insira uma mensagem para ativar a função, ou use prompts de IA se já estiverem ativados';
$lang['emojis'] = 'Emojis';
$lang['translate'] = 'Traduzir';
$lang['change_tone'] = 'Mudar Tom';
$lang['professional'] = 'Profissional';
$lang['friendly'] = 'Amigável';
$lang['empathetic'] = 'Empático';
$lang['straightforward'] = 'Direto';
$lang['simplify_language'] = 'Simplificar Linguagem';
$lang['fix_spelling_and_grammar'] = 'Corrigir Ortografia & Gramática';

$lang['ai_integration'] = 'Integração de IA';
$lang['open_ai_api'] = 'API OpenAI';
$lang['open_ai_secret_key'] = 'Chave Secreta OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Onde você pode encontrar a chave secreta?</a>';
$lang['chat_text_limit'] = 'Limite de Texto do Chat';
$lang['chat_text_limit_note'] = 'Para otimizar os custos operacionais, considere limitar a contagem de palavras das respostas de chat da OpenAI';
$lang['chat_model'] = 'Modelo de Chat';
$lang['openai_organizations'] = 'Organizações OpenAi';
$lang['template_type'] = 'Tipo de Modelo';
$lang['update'] = 'Atualizar';
$lang['open_ai_key_verification_fail'] = 'Verificação da Chave OpenAi está pendente nas configurações, por favor conecte sua conta OpenAI';
$lang['enable_wb_openai'] = 'Ativar OpenAI no chat';
$lang['webhook_resend_method'] = 'Método de Reenvio do Webhook';
$lang['search_language'] = 'Pesquisar idioma...';
$lang['document'] = 'Documento';
$lang['select_document'] = 'Selecionar Documento';
$lang['attchment_deleted_successfully'] = 'Anexo excluído com sucesso';
$lang['attach_image_video_docs'] = 'Anexar Imagem Vídeo Documentos';
$lang['choose_file_type'] = 'Escolher Tipo de Arquivo';
$lang['max_size'] = 'Tamanho Máximo: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Campanhas em Massa';
$lang['upload_csv'] = 'Enviar CSV';
$lang['upload'] = 'Enviar';
$lang['csv_uploaded_successfully'] = 'Arquivo CSV enviado com sucesso';
$lang['please_select_file'] = 'Por favor, selecione o arquivo CSV';
$lang['phonenumber_field_is_required'] = 'Campo de número de telefone é obrigatório';
$lang['out_of_the'] = 'Fora de';
$lang['records_in_your_csv_file'] = 'registros no seu arquivo CSV,';
$lang['valid_the_campaign_can_be_sent'] = 'registros são válidos.<br /> A campanha pode ser enviada com sucesso para estes';
$lang['users'] = 'usuários';
$lang['campaigns_from_csv_file'] = 'Campanhas do Arquivo CSV';
$lang['download_sample'] = 'Baixar Exemplo';
$lang['csv_rule_1'] = '1. <b>Requisito da Coluna do Número de Telefone:</b> Seu arquivo CSV deve incluir uma coluna chamada "Phoneno." Cada registro nesta coluna deve conter um número de contato válido, formatado corretamente com o código do país, incluindo o sinal "+" . <br /><br />';
$lang['csv_rule_2'] = '2. <b>Formato e Codificação do CSV:</b> Seus dados CSV devem seguir o formato especificado. A primeira linha do seu arquivo CSV deve conter os cabeçalhos das colunas, conforme mostrado na tabela de exemplo. Certifique-se de que seu arquivo esteja codificado em UTF-8 para evitar problemas de codificação.';
$lang['please_upload_valid_csv_file'] = 'Por favor, envie um arquivo CSV válido';
$lang['please_add_valid_number_in_csv_file'] = 'Por favor, adicione um <b>Phoneno</b> válido no arquivo CSV';
$lang['total_send_campaign_list'] = 'Total de campanhas enviadas: %s';
$lang['sample_data'] = 'Dados de Exemplo';
$lang['firstname'] = 'Primeiro Nome';
$lang['lastname'] = 'Último Nome';
$lang['phoneno'] = 'Phoneno';
$lang['email'] = 'Email';
$lang['country'] = 'País';
$lang['download_sample_and_read_rules'] = 'Baixar Arquivo de Exemplo & Ler Regras';
$lang['please_wait_your_request_in_process'] = 'Por favor, aguarde, sua solicitação está sendo processada.';
$lang['whatsbot_bulk_campaign'] = 'Campanhas em Massa do Whatsbot';
$lang['csv_campaign'] = 'Campanha CSV';

// Canned reply
$lang['canned_reply'] = 'Resposta Pré-definida';
$lang['canned_reply_menu'] = 'Resposta Pré-definida';
$lang['create_canned_reply'] = 'Criar Resposta Pré-definida';
$lang['title'] = 'Título';
$lang['desc'] = 'Descrição';
$lang['public'] = 'Público';
$lang['action'] = 'Ação';
$lang['delete_successfully'] = 'Resposta excluída.';
$lang['cannot_delete'] = 'Resposta não pode ser excluída.';
$lang['whatsbot_canned_reply'] = 'Resposta Pré-definida do Whatsbot';
$lang['reply'] = 'Responder';

//AI Prompts
$lang['ai_prompts'] = 'Prompts de IA';
$lang['create_ai_prompts'] = 'Criar Prompts de IA';
$lang['name'] = 'Nome';
$lang['action'] = 'Ação';
$lang['prompt_name'] = 'Nome do Prompt';
$lang['prompt_action'] = 'Ação do Prompt';
$lang['whatsbot_ai_prompts'] = 'Prompts de IA do Whatsbot';

// new chat
$lang['replying_to'] = 'Respondendo a:';
$lang['download_document'] = 'Baixar Documento';
$lang['custom_prompt'] = 'Prompt Personalizado';
$lang['canned_replies'] = 'Respostas Pré-definidas';
$lang['use_@_to_add_merge_fields'] = 'Use \'@\' para adicionar campos de mesclagem';
$lang['type_your_message'] = 'Digite sua mensagem';
$lang['you_cannot_send_a_message_using_this_number'] = 'Você não pode enviar uma mensagem usando este número.';

// bot flow
$lang['bot_flow'] = 'Fluxo do Bot';
$lang['create_new_flow'] = 'Criar Novo Fluxo';
$lang['flow_name'] = 'Nome do Fluxo';
$lang['flow'] = 'Fluxo';
$lang['bot_flow_builder'] = 'Construtor de Fluxo do Bot';
$lang['you_can_not_upload_file_type'] = 'Você não pode enviar um arquivo do tipo <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Fluxo do Bot Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Limpeza Automática do Histórico de Chats';
$lang['enable_auto_clear_chat_history'] = 'Ativar Limpeza Automática do Histórico de Chats';
$lang['auto_clear_time'] = 'Tempo de Limpeza Automática';
$lang['clear_chat_history_note'] = '<strong>Nota: </strong> Se você ativar a função de limpeza automática do histórico de chats, ele será limpo automaticamente com base no número de dias que você especificar, sempre que a tarefa cron for executada.';
$lang['source'] = 'Fonte';
$lang['groups'] = 'Grupos';


// v1.3.3
$lang['click_user_to_chat'] = 'Clique no usuário para conversar';
$lang['searching'] = 'Procurando...';
$lang['filters'] = 'Filtros';
$lang['relation_type'] = 'Tipo de Relação';
$lang['groups'] = 'Grupos';
$lang['source'] = 'Fonte';
$lang['status'] = 'Status';
$lang['select_type'] = 'Selecionar Tipo';
$lang['select_agents'] = 'Selecionar Agentes';
$lang['select_group'] = 'Selecionar Grupo';
$lang['select_source'] = 'Selecionar Fonte';
$lang['select_status'] = 'Selecionar Status';
$lang['agents'] = 'Agentes';

// v1.4.2
$lang['read_only'] = 'Somente leitura';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
