{"name": "league/csv", "type": "library", "description": "CSV data manipulation made easy in PHP", "keywords": ["csv", "import", "export", "read", "write", "filter", "convert", "transform"], "license": "MIT", "homepage": "https://csv.thephpleague.com", "authors": [{"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "homepage": "https://github.com/nyamsprod/", "role": "Developer"}], "support": {"docs": "https://csv.thephpleague.com", "issues": "https://github.com/thephpleague/csv/issues", "rss": "https://github.com/thephpleague/csv/releases.atom", "source": "https://github.com/thephpleague/csv"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/nyamsprod"}], "require": {"php": "^8.1.2", "ext-filter": "*"}, "require-dev": {"ext-dom": "*", "ext-xdebug": "*", "doctrine/collections": "^2.2.2", "friendsofphp/php-cs-fixer": "^3.57.1", "phpbench/phpbench": "^1.2.15", "phpstan/phpstan": "^1.11.1", "phpstan/phpstan-deprecation-rules": "^1.2.0", "phpstan/phpstan-phpunit": "^1.4.0", "phpstan/phpstan-strict-rules": "^1.6.0", "phpunit/phpunit": "^10.5.16 || ^11.1.3", "symfony/var-dumper": "^6.4.6 || ^7.0.7"}, "autoload": {"psr-4": {"League\\Csv\\": "src"}, "files": ["src/functions_include.php"]}, "scripts": {"benchmark": "phpbench run src --report=default", "phpcs": "PHP_CS_FIXER_IGNORE_ENV=1 php-cs-fixer fix -vvv --diff --dry-run --allow-risky=yes --ansi", "phpcs:fix": "php-cs-fixer fix -vvv --allow-risky=yes --ansi", "phpstan": "phpstan analyse -c phpstan.neon --ansi --memory-limit=192M", "phpunit": "XDEBUG_MODE=coverage phpunit --coverage-text", "phpunit:min": "phpunit --no-coverage", "test": ["@phpunit", "@phpstan", "@phpcs"]}, "scripts-descriptions": {"benchmark": "Runs benchmarks on writing and reader CSV documents", "phpcs": "Runs coding style test suite", "phpstan": "Runs complete codebase static analysis", "phpunit": "Runs unit and functional testing", "test": "Runs full test suite"}, "suggest": {"ext-iconv": "Needed to ease transcoding CSV using iconv stream filters", "ext-dom": "Required to use the XMLConverter and the HTMLConverter classes", "ext-mbstring": "Needed to ease transcoding CSV using mb stream filters"}, "extra": {"branch-alias": {"dev-master": "9.x-dev"}}, "config": {"sort-packages": true}}