<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Подключить аккаунт';
$lang['connect_whatsapp_business'] = 'Подключить Whatsapp Business';
$lang['campaigning'] = 'Кампании';
$lang['business_account_id_description'] = 'Ваш ID аккаунта WhatsApp Business (WABA)';
$lang['access_token_description'] = 'Ваш токен доступа пользователя после регистрации на портале разработчиков Facebook';
$lang['whatsapp_business_account_id'] = 'ID аккаунта Whatsapp Business';
$lang['whatsapp_access_token'] = 'Токен доступа Whatsapp';
$lang['webhook_callback_url'] = 'URL обратного вызова Webhook';
$lang['verify_token'] = 'Проверить токен';
$lang['connect'] = 'Подключить';
$lang['whatsapp'] = 'Whatsapp';
$lang['one_click_account_connection'] = 'Подключение аккаунта в один клик';
$lang['connect_your_whatsapp_account'] = 'Подключите свой аккаунт Whatsapp';
$lang['copy'] = 'Скопировать';
$lang['copied'] = 'Скопировано!!';
$lang['disconnect'] = 'Отключить';
$lang['number'] = 'Номер';
$lang['number_id'] = 'ID номера';
$lang['quality'] = 'Качество';
$lang['status'] = 'Статус';
$lang['business_account_id'] = 'ID бизнес-аккаунта';
$lang['permissions'] = 'Разрешения';
$lang['phone_number_id_description'] = 'ID телефонного номера, подключенного к WhatsApp Business API. Если вы не уверены, вы можете использовать GET-запрос на получение ID телефонного номера, чтобы получить его из WhatsApp API ( https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers )';
$lang['phone_number_id'] = 'ID номера зарегистрированного телефона в WhatsApp';
$lang['update_details'] = 'Обновить данные';

$lang['bots'] = 'Боты';
$lang['bots_management'] = 'Управление ботами';
$lang['create_template_base_bot'] = 'Создать шаблонный бот';
$lang['create_message_bot'] = 'Создать бот сообщений';
$lang['type'] = 'Тип';
$lang['message_bot'] = 'Бот сообщений';
$lang['new_template_bot'] = 'Новый шаблонный бот';
$lang['new_message_bot'] = 'Новый бот сообщений';
$lang['bot_name'] = 'Имя бота';
$lang['reply_text'] = 'Текст ответа <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Текст, который будет отправлен лидеру или контакту. Вы также можете использовать {companyname}, {crm_url} или любые другие пользовательские поля слияния лида или контакта, или использовать знак \'@\' для поиска доступных полей слияния" data-placement="bottom"></i> <span class="text-muted">(Максимально допустимое количество символов - 1024)</span>';
$lang['reply_type'] = 'Тип ответа';
$lang['trigger'] = 'Триггер';
$lang['header'] = 'Заголовок';
$lang['footer_bot'] = 'Нижний колонтитул <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Вариант 1: Бот с кнопками ответа';
$lang['bot_with_button_link'] = 'Вариант 2: Бот с кнопкой ссылки - URL CTA';
$lang['button1'] = 'Кнопка 1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID кнопки 1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 256" data-placement="bottom"></i>';
$lang['button2'] = 'Кнопка 2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID кнопки 2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 256" data-placement="bottom"></i>';
$lang['button3'] = 'Кнопка 3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID кнопки 3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Имя кнопки <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Максимально допустимое количество символов - 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Ссылка кнопки';
$lang['enter_name'] = 'Введите имя';
$lang['select_reply_type'] = 'Выберите тип ответа';
$lang['enter_bot_reply_trigger'] = 'Введите триггер ответа бота';
$lang['enter_header'] = 'Введите заголовок';
$lang['enter_footer'] = 'Введите нижний колонтитул';
$lang['enter_button1'] = 'Введите кнопку 1';
$lang['enter_button1_id'] = 'Введите ID кнопки 1';
$lang['enter_button2'] = 'Введите кнопку 2';
$lang['enter_button2_id'] = 'Введите ID кнопки 2';
$lang['enter_button3'] = 'Введите кнопку 3';
$lang['enter_button3_id'] = 'Введите ID кнопки 3';
$lang['enter_button_name'] = 'Введите имя кнопки';
$lang['enter_button_url'] = 'Введите URL кнопки';
$lang['on_exact_match'] = 'Ответ бота: При точном совпадении';
$lang['when_message_contains'] = 'Ответ бота: Когда сообщение содержит';
$lang['when_client_send_the_first_message'] = 'Ответ приветствия - когда лидер или клиент отправляет первое сообщение';
$lang['bot_create_successfully'] = 'Бот успешно создан';
$lang['bot_update_successfully'] = 'Бот успешно обновлён';
$lang['bot_deleted_successfully'] = 'Бот успешно удалён';
$lang['templates'] = 'Шаблоны';
$lang['template_data_loaded'] = 'Шаблоны успешно загружены';
$lang['load_templates'] = 'Загрузить шаблоны';
$lang['template_management'] = 'Управление шаблонами';
// campaigns
$lang['campaign'] = 'Кампания';
$lang['campaigns'] = 'Кампании';
$lang['send_new_campaign'] = 'Отправить новую кампанию';
$lang['campaign_name'] = 'Название кампании';
$lang['template'] = 'Шаблон';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Для каждого клиента, в зависимости от часового пояса контакта" data-placement="left"></i>Запланированное время отправки';
$lang['scheduled_time_description'] = 'Для каждого клиента, в зависимости от часового пояса контакта';
$lang['ignore_scheduled_time_and_send_now'] = 'Игнорировать запланированное время и отправить сейчас';
$lang['template'] = 'Шаблон';
$lang['leads'] = 'Лиды';
$lang['delivered_to'] = 'Доставлено';
$lang['read_by'] = 'Прочитано';
$lang['variables'] = 'Переменные';
$lang['body'] = 'Текст';
$lang['variable'] = 'Переменная';
$lang['match_with_selected_field'] = 'Соответствует выбранному полю';
$lang['preview'] = 'Предварительный просмотр';
$lang['send_campaign'] = 'Отправить кампанию';
$lang['send_to'] = 'Отправить кому';
$lang['send_campaign'] = 'Отправить кампанию';
$lang['view_campaign'] = 'Просмотреть кампанию';
$lang['campaign_daily_task'] = 'Ежедневная задача кампании';
$lang['back'] = 'Назад';
$lang['phone'] = 'Телефон';
$lang['message'] = 'Сообщение';
$lang['currently_type_not_supported'] = 'В данный момент тип <strong> %s </strong> шаблона не поддерживается!';
$lang['of_your'] = 'из ваших ';
$lang['contacts'] = 'Контакты';
$lang['select_all_leads'] = 'Выбрать всех лидов';
$lang['select_all_note_leads'] = 'Если вы выберете это, все будущие лиды будут включены в эту кампанию.';
$lang['select_all_note_contacts'] = 'Если вы выберете это, все будущие контакты будут включены в эту кампанию.';

$lang['verified_name'] = 'Подтвержденное имя';
$lang['mark_as_default'] = 'Установить по умолчанию';
$lang['default_number_updated'] = 'Идентификатор номера телефона по умолчанию успешно обновлен';
$lang['currently_using_this_number'] = 'В данный момент используется этот номер';
$lang['leads'] = 'Лиды';
$lang['pause_campaign'] = 'Приостановить кампанию';
$lang['resume_campaign'] = 'Возобновить кампанию';
$lang['campaign_resumed'] = 'Кампания возобновлена';
$lang['campaign_paused'] = 'Кампания приостановлена';

//Template
$lang['body_data'] = 'Данные текста';
$lang['category'] = 'Категория';

// Template bot
$lang['create_new_template_bot'] = 'Создать нового шаблонного бота';
$lang['template_bot'] = 'Шаблонный бот';
$lang['variables'] = 'Переменные';
$lang['preview'] = 'Предварительный просмотр';
$lang['template'] = 'Шаблон';
$lang['bot_content_1'] = 'Это сообщение будет отправлено контакту, как только правило срабатывания будет выполнено в сообщении, отправленном контактом.';
$lang['save_bot'] = 'Сохранить бота';
$lang['please_select_template'] = 'Пожалуйста, выберите шаблон';
$lang['use_manually_define_value'] = 'Использовать вручную определенное значение';
$lang['merge_fields'] = 'Слияние полей';
$lang['template_bot_create_successfully'] = 'Шаблонный бот успешно создан';
$lang['template_bot_update_successfully'] = 'Шаблонный бот успешно обновлен';
$lang['text_bot'] = 'Текстовый бот';
$lang['option_2_bot_with_link'] = 'Вариант 2: Бот с кнопкой-ссылкой - Призыв к действию (CTA) URL';
$lang['option_3_file'] = 'Вариант 3: Бот с файлом';
// Bot settings
$lang['bot'] = 'Бот';
$lang['bot_delay_response'] = 'Сообщение будет отправлено, когда ожидается задержка в ответе';
$lang['bot_delay_response_placeholder'] = 'Дайте мне минутку, я скоро отвечу';

$lang['whatsbot'] = 'WhatsBot';

//campaigns
$lang['relation_type'] = 'Тип связи';
$lang['select_all'] = 'Выбрать всех';
$lang['total'] = 'Всего';
$lang['merge_field_note'] = 'Используйте знак \'@\' для добавления полей слияния.';
$lang['send_to_all'] = 'Отправить всем ';
$lang['or'] = 'ИЛИ';

$lang['convert_whatsapp_message_to_lead'] = 'Автоматически получать новые лиды (превращать новые сообщения WhatsApp в лиды)';
$lang['leads_status'] = 'Статус лида';
$lang['leads_assigned'] = 'Лид назначен';
$lang['whatsapp_auto_lead'] = 'Авто-лид WhatsApp';
$lang['webhooks_label'] = 'Полученные данные WhatsApp будут отправлены на';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Включить повторную отправку WebHooks';
$lang['chat'] = 'Чат';
$lang['black_listed_phone_numbers'] = 'Заблокированные номера телефонов';
$lang['sent_status'] = 'Статус отправки';

$lang['active'] = 'Активный';
$lang['approved'] = 'Одобрено';
$lang['this_month'] = 'в этом месяце';
$lang['open_chats'] = 'Открытые чаты';
$lang['resolved_conversations'] = 'Разрешенные разговоры';
$lang['messages_sent'] = 'Сообщения отправлены';
$lang['account_connected'] = 'Аккаунт подключен';
$lang['account_disconnected'] = 'Аккаунт отключен';
$lang['webhook_verify_token'] = 'Токен проверки Webhook';
// Chat integration
$lang['chat_message_note'] = 'Сообщение будет отправлено вскоре. Обратите внимание, что если это новый контакт, он не появится в этом списке, пока контакт не начнет взаимодействовать с вами!';

$lang['activity_log'] = 'Журнал активности';
$lang['whatsapp_logs'] = 'Журналы WhatsApp';
$lang['response_code'] = 'Код ответа';
$lang['recorded_on'] = 'Зарегистрировано в';

$lang['request_details'] = 'Детали запроса';
$lang['raw_content'] = 'Сырой контент';
$lang['headers'] = 'Заголовки';
$lang['format_type'] = 'Тип формата';

// Permission section
$lang['show_campaign'] = 'Показать кампанию';
$lang['clear_log'] = 'Очистить журнал';
$lang['log_activity'] = 'Журнал активности';
$lang['load_template'] = 'Загрузить шаблон';

$lang['action'] = 'Действие';
$lang['total_parameters'] = 'Всего параметров';
$lang['template_name'] = 'Название шаблона';
$lang['log_cleared_successfully'] = 'Журнал успешно очищен';
$lang['whatsbot_stats'] = 'Статистика WhatsBot';

$lang['not_found_or_deleted'] = 'Не найдено или удалено';
$lang['response'] = 'Ответ';

$lang['select_image'] = 'Выбрать изображение';
$lang['image'] = 'Изображение';
$lang['image_deleted_successfully'] = 'Изображение успешно удалено';
$lang['whatsbot_settings'] = 'Настройки WhatsBot';
$lang['maximum_file_size_should_be'] = 'Максимальный размер файла должен быть ';
$lang['allowed_file_types'] = 'Разрешенные типы файлов : ';

$lang['send_image'] = 'Отправить изображение';
$lang['send_video'] = 'Отправить видео';
$lang['send_document'] = 'Отправить документ';
$lang['record_audio'] = 'Записать аудио';
$lang['chat_media_info'] = 'Дополнительная информация о поддерживаемых типах контента и обработке размера медиафайлов';
$lang['help'] = 'Помощь';

// v1.1.0
$lang['clone'] = 'Клонировать';
$lang['bot_clone_successfully'] = 'Бот успешно клонирован';
$lang['all_chat'] = 'Все чаты';
$lang['from'] = 'От:';
$lang['phone_no'] = 'Телефон:';
$lang['supportagent'] = 'Служба поддержки';
$lang['assign_chat_permission_to_support_agent'] = 'Назначить разрешение на чат только агенту поддержки';
$lang['enable_whatsapp_notification_sound'] = 'Включить звук уведомления WhatsApp';
$lang['notification_sound'] = 'Звук уведомления';
$lang['trigger_keyword'] = 'Ключевое слово триггера';
$lang['modal_title'] = 'Выбрать агента поддержки';
$lang['close_btn'] = 'Закрыть';
$lang['save_btn'] = 'Сохранить';
$lang['support_agent'] = 'Агент поддержки';
$lang['change_support_agent'] = 'Сменить агента поддержки';
$lang['replay_message'] = 'Вы не можете отправить сообщение, если прошло более 24 часов.';
$lang['support_agent_note'] = '<strong>Примечание: </strong>При включении функции агента поддержки, ответственный за лид автоматически будет назначен в чат. Администраторы также могут назначить нового агента со страницы чата.';
$lang['permission_bot_clone'] = 'Клонировать бота';
$lang['remove_chat'] = 'Удалить чат';
$lang['default_message_on_no_match'] = 'Ответ по умолчанию - если ни одно ключевое слово не соответствует';
$lang['default_message_note'] = '<strong>Примечание: </strong>Включение этой опции увеличит нагрузку на ваш вебхук. Для получения дополнительной информации посетите этот <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">ссылку</a>.';
$lang['whatsbot_connect_account'] = 'Соединить аккаунт Whatsbot';
$lang['whatsbot_message_bot'] = 'Сообщение бота Whatsbot';
$lang['whatsbot_template_bot'] = 'Шаблон бота Whatsbot';
$lang['whatsbot_template'] = 'Шаблон Whatsbot';
$lang['whatsbot_campaigns'] = 'Кампании Whatsbot';
$lang['whatsbot_chat'] = 'Чат Whatsbot';
$lang['whatsbot_log_activity'] = 'Журнал активности Whatsbot';
$lang['message_templates_not_exists_note'] = 'Недостаточно прав на мета-шаблон. Пожалуйста, включите это в своем аккаунте Meta';

// v1.2.0
$lang['ai_prompt'] = 'AI Подсказки';
$lang['ai_prompt_note'] = 'Для AI подсказок, пожалуйста, введите сообщение, чтобы активировать функцию, или используйте AI подсказки, если они уже включены';
$lang['emojis'] = 'Эмодзи';
$lang['translate'] = 'Перевести';
$lang['change_tone'] = 'Изменить тон';
$lang['professional'] = 'Профессиональный';
$lang['friendly'] = 'Дружелюбный';
$lang['empathetic'] = 'Эмпатичный';
$lang['straightforward'] = 'Прямой';
$lang['simplify_language'] = 'Упрощать язык';
$lang['fix_spelling_and_grammar'] = 'Исправить орфографию и грамматику';

$lang['ai_integration'] = 'Интеграция AI';
$lang['open_ai_api'] = 'API OpenAI';
$lang['open_ai_secret_key'] = 'Секретный ключ OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Где найти секретный ключ?</a>';
$lang['chat_text_limit'] = 'Лимит текста чата';
$lang['chat_text_limit_note'] = 'Чтобы оптимизировать операционные расходы, рассмотрите возможность ограничения количества слов в ответах чата OpenAI';
$lang['chat_model'] = 'Модель чата';
$lang['openai_organizations'] = 'Организации OpenAi';
$lang['template_type'] = 'Тип шаблона';
$lang['update'] = 'Обновить';
$lang['open_ai_key_verification_fail'] = 'Проверка ключа OpenAi ожидает настройки, пожалуйста, подключите свой аккаунт OpenAI';
$lang['enable_wb_openai'] = 'Включить OpenAI в чате';
$lang['webhook_resend_method'] = 'Метод повторной отправки вебхука';
$lang['search_language'] = 'Искать язык...';
$lang['document'] = 'Документ';
$lang['select_document'] = 'Выбрать документ';
$lang['attchment_deleted_successfully'] = 'Вложение успешно удалено';
$lang['attach_image_video_docs'] = 'Прикрепить изображения, видео, документы';
$lang['choose_file_type'] = 'Выберите тип файла';
$lang['max_size'] = 'Макс. размер: ';

// v1.3.0

// CSV import
$lang['bulk_campaigns'] = 'Массовые кампании';
$lang['upload_csv'] = 'Загрузить CSV';
$lang['upload'] = 'Загрузить';
$lang['csv_uploaded_successfully'] = 'CSV файл успешно загружен';
$lang['please_select_file'] = 'Пожалуйста, выберите CSV файл';
$lang['phonenumber_field_is_required'] = 'Поле с номером телефона обязательно';
$lang['out_of_the'] = 'Из';
$lang['records_in_your_csv_file'] = 'записей в вашем CSV файле,';
$lang['valid_the_campaign_can_be_sent'] = 'записей являются действительными.<br /> Кампания может быть успешно отправлена этим';
$lang['users'] = 'пользователям';
$lang['campaigns_from_csv_file'] = 'Кампании из CSV файла';
$lang['download_sample'] = 'Скачать образец';
$lang['csv_rule_1'] = '1. <b>Требование к столбцу с номером телефона:</b> Ваш CSV файл должен содержать столбец с именем "Phoneno." Каждая запись в этом столбце должна содержать действительный номер контакта, правильно отформатированный с кодом страны, включая знак "+" . <br /><br />';
$lang['csv_rule_2'] = '2. <b>Формат и кодировка CSV:</b> Ваши данные CSV должны следовать указанному формату. Первая строка вашего CSV файла должна содержать заголовки столбцов, как показано в примере. Убедитесь, что ваш файл закодирован в UTF-8, чтобы избежать проблем с кодировкой.';
$lang['please_upload_valid_csv_file'] = 'Пожалуйста, загрузите действительный CSV файл';
$lang['please_add_valid_number_in_csv_file'] = 'Пожалуйста, добавьте действительный <b>Phoneno</b> в CSV файл';
$lang['total_send_campaign_list'] = 'Всего отправлено кампаний: %s';
$lang['sample_data'] = 'Образец данных';
$lang['firstname'] = 'Имя';
$lang['lastname'] = 'Фамилия';
$lang['phoneno'] = 'Номер телефона';
$lang['email'] = 'Электронная почта';
$lang['country'] = 'Страна';
$lang['download_sample_and_read_rules'] = 'Скачать образец файла и прочитать правила';
$lang['please_wait_your_request_in_process'] = 'Пожалуйста, подождите, ваша заявка обрабатывается.';
$lang['whatsbot_bulk_campaign'] = 'Массовые кампании Whatsbot';
$lang['csv_campaign'] = 'Кампания CSV';

// Canned reply
$lang['canned_reply'] = 'Стандартный ответ';
$lang['canned_reply_menu'] = 'Стандартные ответы';
$lang['create_canned_reply'] = 'Создать стандартный ответ';
$lang['title'] = 'Заголовок';
$lang['desc'] = 'Описание';
$lang['public'] = 'Публичный';
$lang['action'] = 'Действие';
$lang['delete_successfully'] = 'Ответ удален.';
$lang['cannot_delete'] = 'Ответ не может быть удален.';
$lang['whatsbot_canned_reply'] = 'Стандартные ответы Whatsbot';
$lang['reply'] = 'Ответ';

//AI Prompts
$lang['ai_prompts'] = 'AI Подсказки';
$lang['create_ai_prompts'] = 'Создать AI подсказки';
$lang['name'] = 'Имя';
$lang['action'] = 'Действие';
$lang['prompt_name'] = 'Имя подсказки';
$lang['prompt_action'] = 'Действие подсказки';
$lang['whatsbot_ai_prompts'] = 'AI Подсказки Whatsbot';

// new chat
$lang['replying_to'] = 'Ответ на:';
$lang['download_document'] = 'Скачать документ';
$lang['custom_prompt'] = 'Пользовательская подсказка';
$lang['canned_replies'] = 'Стандартные ответы';
$lang['use_@_to_add_merge_fields'] = 'Используйте \'@\' для добавления полей слияния';
$lang['type_your_message'] = 'Введите ваше сообщение';
$lang['you_cannot_send_a_message_using_this_number'] = 'Вы не можете отправить сообщение с использованием этого номера.';

// bot flow
$lang['bot_flow'] = 'Поток бота';
$lang['create_new_flow'] = 'Создать новый поток';
$lang['flow_name'] = 'Имя потока';
$lang['flow'] = 'Поток';
$lang['bot_flow_builder'] = 'Конструктор потока бота';
$lang['you_can_not_upload_file_type'] = 'Вы не можете загрузить файл типа <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Поток бота Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Автоочистка истории чата';
$lang['enable_auto_clear_chat_history'] = 'Включить автоочистку истории чата';
$lang['auto_clear_time'] = 'Время автоочистки истории';
$lang['clear_chat_history_note'] = '<strong>Примечание: </strong> Если вы включите функцию автоочистки истории чата, она будет автоматически очищать историю чата на основе количества дней, которое вы укажете, каждый раз, когда выполняется задание cron.';
$lang['source'] = 'Источник';
$lang['groups'] = 'Группы';


// v1.3.3
$lang['click_user_to_chat'] = 'Нажмите на пользователя, чтобы начать чат';
$lang['searching'] = 'Поиск...';
$lang['filters'] = 'Фильтры';
$lang['relation_type'] = 'Тип связи';
$lang['groups'] = 'Группы';
$lang['source'] = 'Источник';
$lang['status'] = 'Статус';
$lang['select_type'] = 'Выберите тип';
$lang['select_agents'] = 'Выберите агентов';
$lang['select_group'] = 'Выберите группу';
$lang['select_source'] = 'Выберите источник';
$lang['select_status'] = 'Выберите статус';
$lang['agents'] = 'Агенты';

// v1.4.2
$lang['read_only'] = 'Только для чтения';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
