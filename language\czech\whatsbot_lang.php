<?php

defined('BASEPATH') || exit('No direct script access allowed');

// connect account
$lang['whatsbot'] = 'WhatsBot';
$lang['connect_account'] = 'Připojit účet';
$lang['connect_whatsapp_business'] = 'Připojit WhatsApp Business';
$lang['campaigning'] = 'Kampaně';
$lang['business_account_id_description'] = 'ID vašeho účtu WhatsApp Business (WABA)';
$lang['access_token_description'] = 'Váš uživatelský přístupový token po registraci na Facebook Developers Portal';
$lang['whatsapp_business_account_id'] = 'ID účtu WhatsApp Business';
$lang['whatsapp_access_token'] = 'Přístupový token WhatsApp';
$lang['webhook_callback_url'] = 'Webhook URL pro zpětné volání';
$lang['verify_token'] = 'Ověřovací token';
$lang['connect'] = 'Připojit';
$lang['whatsapp'] = 'WhatsApp';
$lang['one_click_account_connection'] = 'Připojení účtu jedním kliknutím';
$lang['connect_your_whatsapp_account'] = 'Připojte svůj účet WhatsApp';
$lang['copy'] = 'Kopírovat';
$lang['copied'] = 'Zkopírováno!!';
$lang['disconnect'] = 'Odpojit';
$lang['number'] = 'Číslo';
$lang['number_id'] = 'ID čísla';
$lang['quality'] = 'Kvalita';
$lang['status'] = 'Stav';
$lang['business_account_id'] = 'ID obchodního účtu';
$lang['permissions'] = 'Oprávnění';
$lang['phone_number_id_description'] = 'ID telefonního čísla připojeného k WhatsApp Business API. Pokud si nejste jisti, můžete použít GET Phone Number ID request pro jeho získání z WhatsApp API (https://developers.facebook.com/docs/whatsapp/business-management-api/manage-phone-numbers)';
$lang['phone_number_id'] = 'ID čísla registrovaného na WhatsApp';
$lang['update_details'] = 'Aktualizovat podrobnosti';

$lang['bots'] = 'Boty';
$lang['bots_management'] = 'Správa botů';
$lang['create_template_base_bot'] = 'Vytvořit šablonového bota';
$lang['create_message_bot'] = 'Vytvořit zprávového bota';
$lang['type'] = 'Typ';
$lang['message_bot'] = 'Zprávový bot';
$lang['new_template_bot'] = 'Nový šablonový bot';
$lang['new_message_bot'] = 'Nový zprávový bot';
$lang['bot_name'] = 'Název bota';
$lang['reply_text'] = 'Text odpovědi <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Text, který bude odeslán potenciálnímu zákazníkovi nebo kontaktu. Můžete také použít {companyname}, {crm_url} nebo jakákoliv jiná vlastní pole pro sloučení, nebo použít znak \'@\' pro nalezení dostupných polí pro sloučení" data-placement="bottom"></i> <span class="text-muted">(Maximální povolený počet znaků je 1024)</span>';
$lang['reply_type'] = 'Typ odpovědi';
$lang['trigger'] = 'Spouštěč';
$lang['header'] = 'Hlava';
$lang['footer_bot'] = 'Patička <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 60" data-placement="bottom"></i>';
$lang['bot_with_reply_buttons'] = 'Možnost 1: Bot s odpovědními tlačítky';
$lang['bot_with_button_link'] = 'Možnost 2: Bot s tlačítkem odkazu - CTA URL';
$lang['button1'] = 'Tlačítko1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 20" data-placement="bottom"></i>';
$lang['button1_id'] = 'ID tlačítka1 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 256" data-placement="bottom"></i>';
$lang['button2'] = 'Tlačítko2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 20" data-placement="bottom"></i>';
$lang['button2_id'] = 'ID tlačítka2 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 256" data-placement="bottom"></i>';
$lang['button3'] = 'Tlačítko3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 20" data-placement="bottom"></i>';
$lang['button3_id'] = 'ID tlačítka3 <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 256" data-placement="bottom"></i>';
$lang['button_name'] = 'Název tlačítka <i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Maximální povolený počet znaků je 20" data-placement="bottom"></i>';
$lang['button_link'] = 'Odkaz na tlačítko';
$lang['enter_name'] = 'Zadejte název';
$lang['select_reply_type'] = 'Vyberte typ odpovědi';
$lang['enter_bot_reply_trigger'] = 'Zadejte spouštěč odpovědi bota';
$lang['enter_header'] = 'Zadejte hlavu';
$lang['enter_footer'] = 'Zadejte patičku';
$lang['enter_button1'] = 'Zadejte tlačítko1';
$lang['enter_button1_id'] = 'Zadejte ID tlačítka1';
$lang['enter_button2'] = 'Zadejte tlačítko2';
$lang['enter_button2_id'] = 'Zadejte ID tlačítka2';
$lang['enter_button3'] = 'Zadejte tlačítko3';
$lang['enter_button3_id'] = 'Zadejte ID tlačítka3';
$lang['enter_button_name'] = 'Zadejte název tlačítka';
$lang['enter_button_url'] = 'Zadejte URL tlačítka';
$lang['on_exact_match'] = 'Odpovědní bot: Při přesném shodě';
$lang['when_message_contains'] = 'Odpovědní bot: Když zpráva obsahuje';
$lang['when_client_send_the_first_message'] = 'Uvítací odpověď - když potenciální zákazník nebo klient odešle první zprávu';
$lang['bot_create_successfully'] = 'Bot byl úspěšně vytvořen';
$lang['bot_update_successfully'] = 'Bot byl úspěšně aktualizován';
$lang['bot_deleted_successfully'] = 'Bot byl úspěšně smazán';
$lang['templates'] = 'Šablony';
$lang['template_data_loaded'] = 'Šablony byly úspěšně načteny';
$lang['load_templates'] = 'Načíst šablony';
$lang['template_management'] = 'Správa šablon';


// kampaně
$lang['campaign'] = 'Kampaň';
$lang['campaigns'] = 'Kampaně';
$lang['send_new_campaign'] = 'Odeslat novou kampaň';
$lang['campaign_name'] = 'Název kampaně';
$lang['template'] = 'Šablona';
$lang['scheduled_send_time'] = '<i class="fa-regular fa-circle-question pull-left tw-mt-0.5 tw-mr-1" data-toggle="tooltip" data-title="Podle klienta, na základě časového pásma kontaktu" data-placement="left"></i>Naplánovaný čas odeslání';
$lang['scheduled_time_description'] = 'Podle klienta, na základě časového pásma kontaktu';
$lang['ignore_scheduled_time_and_send_now'] = 'Ignorovat naplánovaný čas a odeslat nyní';
$lang['template'] = 'Šablona';
$lang['leads'] = 'Zákazníci';
$lang['delivered_to'] = 'Doručeno';
$lang['read_by'] = 'Přečteno';
$lang['variables'] = 'Proměnné';
$lang['body'] = 'Tělo';
$lang['variable'] = 'Proměnná';
$lang['match_with_selected_field'] = 'Shodovat se s vybraným polem';
$lang['preview'] = 'Náhled';
$lang['send_campaign'] = 'Odeslat kampaň';
$lang['send_to'] = 'Odeslat na';
$lang['send_campaign'] = 'Odeslat kampaň';
$lang['view_campaign'] = 'Zobrazit kampaň';
$lang['campaign_daily_task'] = 'Denní úkol kampaně';
$lang['back'] = 'Zpět';
$lang['phone'] = 'Telefon';
$lang['message'] = 'Zpráva';
$lang['currently_type_not_supported'] = 'V současné době není <strong>%s</strong> typ šablony podporován!';
$lang['of_your'] = 'z vašich ';
$lang['contacts'] = 'Kontakty';
$lang['select_all_leads'] = 'Vybrat všechny zákazníky';
$lang['select_all_note_leads'] = 'Pokud toto vyberete, budou do této kampaně zahrnuti všichni budoucí zákazníci.';
$lang['select_all_note_contacts'] = 'Pokud toto vyberete, budou do této kampaně zahrnuty všechny budoucí kontakty.';

$lang['verified_name'] = 'Ověřené jméno';
$lang['mark_as_default'] = 'Označit jako výchozí';
$lang['default_number_updated'] = 'Výchozí telefonní číslo bylo úspěšně aktualizováno';
$lang['currently_using_this_number'] = 'V současné době používáte toto číslo';
$lang['leads'] = 'Zákazníci';
$lang['pause_campaign'] = 'Pozastavit kampaň';
$lang['resume_campaign'] = 'Pokračovat v kampani';
$lang['campaign_resumed'] = 'Kampaň obnovena';
$lang['campaign_paused'] = 'Kampaň pozastavena';

// Šablona
$lang['body_data'] = 'Data těla';
$lang['category'] = 'Kategorie';

// Šablona bot
$lang['create_new_template_bot'] = 'Vytvořit nového šablonového bota';
$lang['template_bot'] = 'Šablonový bot';
$lang['variables'] = 'Proměnné';
$lang['preview'] = 'Náhled';
$lang['template'] = 'Šablona';
$lang['bot_content_1'] = 'Tato zpráva bude odeslána kontaktu, jakmile bude splněno pravidlo spuštění ve zprávě odeslané kontaktem.';
$lang['save_bot'] = 'Uložit bota';
$lang['please_select_template'] = 'Prosím vyberte šablonu';
$lang['use_manually_define_value'] = 'Použít ručně definovanou hodnotu';
$lang['merge_fields'] = 'Sloučená pole';
$lang['template_bot_create_successfully'] = 'Šablonový bot byl úspěšně vytvořen';
$lang['template_bot_update_successfully'] = 'Šablonový bot byl úspěšně aktualizován';
$lang['text_bot'] = 'Textový bot';
$lang['option_2_bot_with_link'] = 'Možnost 2: Bot s tlačítkem odkazu - Výzva k akci (CTA) URL';
$lang['option_3_file'] = 'Možnost 3: Bot s souborem';
// Nastavení bota
$lang['bot'] = 'Bot';
$lang['bot_delay_response'] = 'Zpráva se odešle, když se očekává zpoždění v odpovědi';
$lang['bot_delay_response_placeholder'] = 'Dejte mi chvíli, brzy budu mít odpověď';

$lang['whatsbot'] = 'WhatsBot';

// kampaně
$lang['relation_type'] = 'Typ vztahu';
$lang['select_all'] = 'Vybrat vše';
$lang['total'] = 'Celkem';
$lang['merge_field_note'] = 'Použijte znak \'@\' pro přidání sloučených polí.';
$lang['send_to_all'] = 'Odeslat všem ';
$lang['or'] = 'NEBO';

$lang['convert_whatsapp_message_to_lead'] = 'Automaticky získat nový lead (převést nové zprávy WhatsApp na lead)';
$lang['leads_status'] = 'Stav leadu';
$lang['leads_assigned'] = 'Lead přidělen';
$lang['whatsapp_auto_lead'] = 'Automatický lead WhatsApp';
$lang['webhooks_label'] = 'Data přijatá WhatsApp budou znovu odeslána na';
$lang['webhooks'] = 'WebHooks';
$lang['enable_webhooks'] = 'Povolit opětovné odeslání WebHooks';
$lang['chat'] = 'Chat';
$lang['black_listed_phone_numbers'] = 'Černá telefonní čísla';
$lang['sent_status'] = 'Stav odeslání';

$lang['active'] = 'Aktivní';
$lang['approved'] = 'Schváleno';
$lang['this_month'] = 'tento měsíc';
$lang['open_chats'] = 'Otevřené chaty';
$lang['resolved_conversations'] = 'Vyřešené konverzace';
$lang['messages_sent'] = 'Odeslané zprávy';
$lang['account_connected'] = 'Účet připojen';
$lang['account_disconnected'] = 'Účet odpojen';
$lang['webhook_verify_token'] = 'Ověřovací token webhooku';
// Integrace chatu
$lang['chat_message_note'] = 'Zpráva bude brzy odeslána. Vezměte prosím na vědomí, že pokud je nový kontakt, neobjeví se v tomto seznamu, dokud s vámi kontakt nezačne komunikovat!';

$lang['activity_log'] = 'Aktivní log';
$lang['whatsapp_logs'] = 'WhatsApp logy';
$lang['response_code'] = 'Kód odpovědi';
$lang['recorded_on'] = 'Zaznamenáno dne';

$lang['request_details'] = 'Podrobnosti o žádosti';
$lang['raw_content'] = 'Nekompromisní obsah';
$lang['headers'] = 'Hlavičky';
$lang['format_type'] = 'Typ formátu';

// Sekce oprávnění
$lang['show_campaign'] = 'Zobrazit kampaň';
$lang['clear_log'] = 'Vymazat protokol';
$lang['log_activity'] = 'Protokolovat aktivitu';
$lang['load_template'] = 'Načíst šablonu';

$lang['action'] = 'Akce';
$lang['total_parameters'] = 'Celkový počet parametrů';
$lang['template_name'] = 'Název šablony';
$lang['log_cleared_successfully'] = 'Protokol byl úspěšně vymazán';
$lang['whatsbot_stats'] = 'Statistiky WhatsBot';

$lang['not_found_or_deleted'] = 'Nenalezeno nebo smazáno';
$lang['response'] = 'Odpověď';

$lang['select_image'] = 'Vybrat obrázek';
$lang['image'] = 'Obrázek';
$lang['image_deleted_successfully'] = 'Obrázek byl úspěšně smazán';
$lang['whatsbot_settings'] = 'Nastavení Whatsbot';
$lang['maximum_file_size_should_be'] = 'Maximální velikost souboru by měla být ';
$lang['allowed_file_types'] = 'Povolené typy souborů: ';

$lang['send_image'] = 'Odeslat obrázek';
$lang['send_video'] = 'Odeslat video';
$lang['send_document'] = 'Odeslat dokument';
$lang['record_audio'] = 'Nahrát zvuk';
$lang['chat_media_info'] = 'Další informace o podporovaných typech obsahu a velikosti post-processing media';
$lang['help'] = 'Nápověda';

// v1.1.0
$lang['clone'] = 'Klonovat';
$lang['bot_clone_successfully'] = 'Bot byl úspěšně zkopírován';
$lang['all_chat'] = 'Všechny chaty';
$lang['from'] = 'Od:';
$lang['phone_no'] = 'Telefonní číslo:';
$lang['supportagent'] = 'Podpora';
$lang['assign_chat_permission_to_support_agent'] = 'Přidělit oprávnění chatu pouze podpoře';
$lang['enable_whatsapp_notification_sound'] = 'Povolit zvuk oznámení WhatsApp chatu';
$lang['notification_sound'] = 'Zvuk oznámení';
$lang['trigger_keyword'] = 'Spouštěcí klíčové slovo';
$lang['modal_title'] = 'Vyberte agenta podpory';
$lang['close_btn'] = 'Zavřít';
$lang['save_btn'] = 'Uložit';
$lang['support_agent'] = 'Agent podpory';
$lang['change_support_agent'] = 'Změnit agenta podpory';
$lang['replay_message'] = 'Zprávu nemůžete odeslat, protože uplynulo 24 hodin.';
$lang['support_agent_note'] = '<strong>Poznámka: </strong> Když povolíte funkci agenta podpory, lead, který byl přidělen, bude automaticky přiřazen k chatu. Admini mohou také přiřadit nového agenta z chatové stránky.';
$lang['permission_bot_clone'] = 'Klonovat bota';
$lang['remove_chat'] = 'Odstranit chat';
$lang['default_message_on_no_match'] = 'Výchozí odpověď - pokud se žádné klíčové slovo neshoduje';
$lang['default_message_note'] = '<strong>Poznámka: </strong> Povolování této možnosti zvýší zatížení vašeho webhooku. Pro více informací navštivte tento <a href="https://docs.corbitaltech.dev/products/whatsbot/index.html" target="_blank">odkaz</a>.';

$lang['whatsbot_connect_account'] = 'Připojit účet Whatsbot';
$lang['whatsbot_message_bot'] = 'Whatsbot Zpráva Bot';
$lang['whatsbot_template_bot'] = 'Whatsbot Šablona Bot';
$lang['whatsbot_template'] = 'Whatsbot Šablona';
$lang['whatsbot_campaigns'] = 'Whatsbot Kampaně';
$lang['whatsbot_chat'] = 'Whatsbot Chat';
$lang['whatsbot_log_activity'] = 'Aktivita protokolu Whatsbot';
$lang['message_templates_not_exists_note'] = 'Chybí oprávnění meta šablony. Prosím, povolte to ve vašem Meta účtu';

// v1.2.0
$lang['ai_prompt'] = 'AI Výzvy';
$lang['ai_prompt_note'] = 'Pro AI výzvy prosím zadejte zprávu, aby se funkce povolila, nebo použijte AI výzvy, pokud již byly povoleny';
$lang['emojis'] = 'Emodži';
$lang['translate'] = 'Přeložit';
$lang['change_tone'] = 'Změnit tón';
$lang['professional'] = 'Profesionální';
$lang['friendly'] = 'Přátelský';
$lang['empathetic'] = 'Empatický';
$lang['straightforward'] = 'Přímý';
$lang['simplify_language'] = 'Zjednodušit jazyk';
$lang['fix_spelling_and_grammar'] = 'Opravit pravopis a gramatiku';

$lang['ai_integration'] = 'AI Integrace';
$lang['open_ai_api'] = 'OpenAI API';
$lang['open_ai_secret_key'] = 'Tajný klíč OpenAI - <a href="https://platform.openai.com/account/api-keys" target="_blank">Kde můžete najít tajný klíč?</a>';
$lang['chat_text_limit'] = 'Limit textu chatu';
$lang['chat_text_limit_note'] = 'Pro optimalizaci provozních nákladů zvažte omezení počtu slov v odpovědích chatu OpenAI';
$lang['chat_model'] = 'Model chatu';
$lang['openai_organizations'] = 'Organizace OpenAI';
$lang['template_type'] = 'Typ šablony';
$lang['update'] = 'Aktualizovat';
$lang['open_ai_key_verification_fail'] = 'Ověření klíče OpenAI čeká na nastavení, prosím připojte svůj účet OpenAI';
$lang['enable_wb_openai'] = 'Povolit OpenAI v chatu';
$lang['webhook_resend_method'] = 'Metoda opětovného odeslání webhooku';
$lang['search_language'] = 'Hledat jazyk...';
$lang['document'] = 'Dokument';
$lang['select_document'] = 'Vyberte dokument';
$lang['attchment_deleted_successfully'] = 'Příloha byla úspěšně smazána';
$lang['attach_image_video_docs'] = 'Připojit obrázek, video, dokumenty';
$lang['choose_file_type'] = 'Vyberte typ souboru';
$lang['max_size'] = 'Maximální velikost: ';

// v1.3.0

$lang['bulk_campaigns'] = 'Hromadné kampaně';
$lang['upload_csv'] = 'Nahrát CSV';
$lang['upload'] = 'Nahrát';
$lang['csv_uploaded_successfully'] = 'CSV soubor byl úspěšně nahrán';
$lang['please_select_file'] = 'Vyberte prosím CSV soubor';
$lang['phonenumber_field_is_required'] = 'Pole pro telefonní číslo je povinné';
$lang['out_of_the'] = 'Z';
$lang['records_in_your_csv_file'] = 'záznamů ve vašem CSV souboru,';
$lang['valid_the_campaign_can_be_sent'] = 'záznamy jsou platné.<br /> Kampaň může být úspěšně odeslána těmto';
$lang['users'] = 'uživatelům';
$lang['campaigns_from_csv_file'] = 'Kampaně z CSV souboru';
$lang['download_sample'] = 'Stáhnout ukázku';
$lang['csv_rule_1'] = '1. <b>Povinnost sloupce s telefonním číslem:</b> Váš CSV soubor musí obsahovat sloupec s názvem "Phoneno." Každý záznam v tomto sloupci by měl obsahovat platné telefonní číslo, správně naformátované s předvolbou země, včetně znaku "+".<br /><br />';
$lang['csv_rule_2'] = '2. <b>Formát a kódování CSV:</b> Vaše CSV data by měla dodržovat stanovený formát. První řádek vašeho CSV souboru musí obsahovat názvy sloupců, jak je ukázáno v příkladové tabulce. Ujistěte se, že je váš soubor kódován v UTF-8, abyste předešli problémům s kódováním.';
$lang['please_upload_valid_csv_file'] = 'Prosím, nahrajte platný CSV soubor';
$lang['please_add_valid_number_in_csv_file'] = 'Prosím, přidejte platné <b>Phoneno</b> do CSV souboru';
$lang['total_send_campaign_list'] = 'Celkový seznam odeslaných kampaní: %s';
$lang['sample_data'] = 'Ukázková data';
$lang['firstname'] = 'Jméno';
$lang['lastname'] = 'Příjmení';
$lang['phoneno'] = 'Telefonní číslo';
$lang['email'] = 'Email';
$lang['country'] = 'Země';
$lang['download_sample_and_read_rules'] = 'Stáhněte soubor s ukázkou a přečtěte si pravidla';
$lang['please_wait_your_request_in_process'] = 'Prosím, počkejte, vaše žádost je aktuálně zpracovávána.';
$lang['whatsbot_bulk_campaign'] = 'Hromadné kampaně Whatsbot';
$lang['csv_campaign'] = 'CSV Kampaň';

// Odpovědi z konzervy
$lang['canned_reply'] = 'Odpověď z konzervy';
$lang['canned_reply_menu'] = 'Odpověď z konzervy';
$lang['create_canned_reply'] = 'Vytvořit odpověď z konzervy';
$lang['title'] = 'Název';
$lang['desc'] = 'Popis';
$lang['public'] = 'Veřejné';
$lang['action'] = 'Akce';
$lang['delete_successfully'] = 'Odpověď byla smazána.';
$lang['cannot_delete'] = 'Odpověď nelze smazat.';
$lang['whatsbot_canned_reply'] = 'Odpověď z konzervy Whatsbot';
$lang['reply'] = 'Odpověď';

// AI Výzvy
$lang['ai_prompts'] = 'AI Výzvy';
$lang['create_ai_prompts'] = 'Vytvořit AI Výzvy';
$lang['name'] = 'Jméno';
$lang['action'] = 'Akce';
$lang['prompt_name'] = 'Název výzvy';
$lang['prompt_action'] = 'Akce výzvy';
$lang['whatsbot_ai_prompts'] = 'AI výzvy Whatsbot';

// Nový chat
$lang['replying_to'] = 'Odpovídám na:';
$lang['download_document'] = 'Stáhnout dokument';
$lang['custom_prompt'] = 'Vlastní výzva';
$lang['canned_replies'] = 'Odpovědi z konzervy';
$lang['use_@_to_add_merge_fields'] = 'Použijte \'@\' pro přidání sloučených polí';
$lang['type_your_message'] = 'Napište svou zprávu';
$lang['you_cannot_send_a_message_using_this_number'] = 'Nemůžete odeslat zprávu pomocí tohoto čísla.';

// Tok bota
$lang['bot_flow'] = 'Tok bota';
$lang['create_new_flow'] = 'Vytvořit nový tok';
$lang['flow_name'] = 'Název toku';
$lang['flow'] = 'Tok';
$lang['bot_flow_builder'] = 'Nástroj pro vytváření toku bota';
$lang['you_can_not_upload_file_type'] = 'Nemůžete nahrát soubor typu <b> %s </b>';
$lang['whatsbot_bot_flow'] = 'Tok bota Whatsbot';

// v1.3.2
$lang['auto_clear_chat_history'] = 'Automatické vymazání historie chatu';
$lang['enable_auto_clear_chat_history'] = 'Povolit automatické vymazání historie chatu';
$lang['auto_clear_time'] = 'Čas automatického vymazání historie';
$lang['clear_chat_history_note'] = '<strong>Poznámka: </strong> Pokud povolíte funkci automatického vymazání historie chatu, automaticky vymaže historii chatu na základě počtu dnů, které určíte, kdykoliv běží úloha cron.';
$lang['source'] = 'Zdroj';
$lang['groups'] = 'Skupiny';


// v1.3.3
$lang['click_user_to_chat'] = 'Klikněte na uživatele pro chat';
$lang['searching'] = 'Vyhledávání...';
$lang['filters'] = 'Filtry';
$lang['relation_type'] = 'Typ vztahu';
$lang['groups'] = 'Skupiny';
$lang['source'] = 'Zdroj';
$lang['status'] = 'Stav';
$lang['select_type'] = 'Vyberte typ';
$lang['select_agents'] = 'Vyberte agenty';
$lang['select_group'] = 'Vyberte skupinu';
$lang['select_source'] = 'Vyberte zdroj';
$lang['select_status'] = 'Vyberte stav';
$lang['agents'] = 'Agenti';

// v1.4.2
$lang['read_only'] = 'Pouze ke čtení';

// v2.0.0
$lang['personal_assistant'] = 'AI Personal Assistant';
$lang['create_personal_assistant'] = 'Create AI Personal Assistant';
$lang['assistant_name'] = 'Assistant name';
$lang['pa_files'] = 'Upload files for AI analysis';
$lang['new_personal_assistant'] = 'New Personal Assistant';
$lang['edit_personal_assistant'] = 'Edit Personal Assistant';

$lang['click_to_get_qr_code'] = 'Click to get QR Code';
$lang['phone_numbers'] = 'Phone Numbers';
$lang['display_phone_number'] = 'Display Phone Number';
$lang['update_business_profile'] = 'Update Business Profile';
$lang['resync_phone_numbers'] = 'Re-sync Phone Numbers';
$lang['manage_phone_numbers'] = 'Manage Phone Numbers';
$lang['scan_qr_code_to_start_chat'] = 'Scan QR Code to Start Chat';
$lang['use_qr_code_to_invite'] = 'You can use the following QR Codes to invite people on this platform.';
$lang['url_for_qr_image'] = 'URL for QR Image';
$lang['whatsapp_url'] = 'WhatsApp URL';
$lang['whatsapp_now'] = 'WhatsApp Now';

$lang['file_upload_guidelines'] = 'File upload guidelines for best results';
$lang['supported_file_formats'] = 'Supported file formats';
$lang['pdf'] = 'PDF';
$lang['pdf_text'] = ': Only text-based PDFs (not scanned images).';
$lang['word'] = 'Word (DOC/DOCX)';
$lang['word_text'] = ': Text-based documents only (avoid images or scanned content).';
$lang['text'] = 'Text (TXT)';
$lang['text_text'] = ': Simple, plain text files with UTF-8 encoding.';
$lang['what_to_avoid'] = 'What to avoid';
$lang['scanned_images'] = 'Scanned Images';
$lang['scanned_images_text'] = ': Ensure documents are not image-based. Use OCR software for scanned PDFs.';
$lang['junk_characters'] = 'Junk Characters';
$lang['junk_characters_text'] = ': Avoid non-standard or corrupted characters in the document.';
$lang['large_files'] = 'Large Files';
$lang['large_files_text'] = ': Keep the file size reasonable for optimal performance.';
$lang['file_naming'] = 'File naming';
$lang['avoid_special_characters'] = 'Avoid Special Characters';
$lang['avoid_special_characters_text'] = ': Use alphanumeric characters and underscores in filenames (e.g., document_name.pdf).';
$lang['best_practices'] = 'Best practices';
$lang['well_structured_text'] = 'Use well-structured text with clear headings and paragraphs.';
$lang['proper_encoding'] = 'Ensure proper encoding (UTF-8) for text files.';
$lang['modal_processing_note'] = 'We are processing your document...';
$lang['incorrect_api_key_provided'] = 'Incorrect API key provided';
$lang['phone_number'] = 'Phone Number';
$lang['overall_health'] = 'Overall Health';
$lang['whatsApp_business_id'] = 'WhatsApp Business ID';
$lang['status_as_at'] = 'Status as at';
$lang['fb_app_id'] = 'Facebook App ID <a href="https://developers.facebook.com/docs/whatsapp/solution-providers/get-started-for-tech-providers#step-2--create-a-meta-app" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['fb_app_secret'] = 'Facebook App Secret';
$lang['facebook_developer_account_facebook_app'] = 'Step - 1 : Facebook Developer Account & Facebook App';
$lang['access_token_information'] = 'Access Token Information';
$lang['debug_token'] = 'Debug token';
$lang['permission_scopes'] = 'Permission scopes';
$lang['expiry_at'] = 'Expiry at';
$lang['disconnect_webhook'] = 'Disconnect Webhook';
$lang['webhook_configured'] = 'Webhook Configured';
$lang['connect_webhook'] = 'Connect Webhook';
$lang['webhook_subscribed_successfully'] = 'Webhook subscribe successfully';
$lang['can_send_message'] = 'Can Send Messages';
$lang['overall_health_send_message'] = 'Overall Health of Send Messages';
$lang['refresh_status'] = 'Refresh status';
$lang['issued_at'] = 'Issued at';
$lang['connect_whatsapp_business_account'] = 'Connect Whatsapp Business Account';
$lang['whatsApp_integration_setup'] = 'Step - 2 : WhatsApp Integration Setup';
$lang['openai_key_not_verified_note'] = 'This feature requires OpenAI key verification, which is currently pending. Please <a href="' . admin_url("settings?group=whatsbot&tab=ai_integration") . '" class="alert-link">Click here</a> to verify your API key.';
$lang['cant_upload_file_verification_pending'] = 'You can\'t upload file OpenAI KEY verification pending';

$lang['ai_assistant'] = 'AI Assistant';
$lang['enable_ai_assistant'] = 'Enable AI Assistant';
$lang['stop_ai_assistant'] = 'Keyword to stop AI Assistant';
$lang['temperature'] = 'Temperature:';
$lang['max_token'] = 'Max Token:';
$lang['ai_model'] = 'AI Model';
$lang['temperature_note'] = 'Adjusts the randomness of the models responses. Lower values make outputs more focused and predictable while higher values make them more creative and diverse.';
$lang['max_tokens_note'] = 'Sets the maximum number of tokens the model can generate. This includes both input and output tokens. A higher value allows for longer responses but may use more processing time.';
$lang['bot_with_reply_buttons'] = 'Option 2: Bot with reply buttons';
$lang['option_2_bot_with_link'] = 'Option 3: Bot with button link - Call to Action (CTA) URL';
$lang['option_3_file'] = 'Option 4: Bot with file';
$lang['option_1_personal_assitant'] = 'Option 1: Personal AI assistant';
$lang['whatsbot_pa'] = 'Whatsbot Personal Assistant';


$lang['disconnect_acount'] = 'Disconnect Account';
$lang['whatsapp_business_account'] = 'WhatsApp business account ';
$lang['access_token_information'] = 'Access Token Information';
$lang['refresh_health_status'] = 'Refresh health status';
$lang['facebook_developer_account_info'] = 'Facebook developer account information';
$lang['fb_config_id'] = 'Facebook Config ID <a href="https://developers.facebook.com/docs/whatsapp/embedded-signup/implementation#step-2--create-facebook-login-for-business-configuration" class="mleft10 text-danger" target="_blank">HELP</a>';
$lang['connect_with_facebook'] = 'Connect with Facebook';
$lang['send_test_message'] = 'Send Test Message';
$lang['test_number_note'] = 'Add `+` and the country code before the number (e.g., `+***********`) to send a WhatsApp message.';
$lang['wb_number'] = 'WhatsApp number';
$lang['send_message'] = 'Send message';
$lang['message_sent_successfully'] = 'Message sent sucessfully';
$lang['configure'] = 'Configure';
$lang['enable_embaded_signin'] = 'Enable embedded SignIn';
$lang['save_details'] = 'Save details';
$lang['modal_processing_connect_account_note'] = 'We are procesing on your request';
$lang['user_cancle_note'] = 'User cancelled login or did not fully authorize.';
$lang['access_token'] = 'Access token';
$lang['webhook_url'] = 'Webhook URL';
$lang['please_enter_all_details'] = 'Please enter all details';
$lang['please_select_default_number_first'] = 'Please select a default WhatsApp number to send the test message';
$lang['please_add_number_for_send_message'] = 'Please add a WhatsApp number to send the test message';
$lang['webhook_connected'] = 'Webhook connected successfully';

// Update language line : Start
$lang['update_version'] = 'Update Version';
$lang['update_warning'] = 'Before performing an update, it is <b>strongly recommended to create a full backup</b> of your current installation <b>(files and database)</b> and review the changelog.';
$lang['upgrade_function'] = 'Upgrade Function';
$lang['download_files'] = 'Download files';
$lang['fix_errors'] = 'Please fix the errors listed below.';
$lang['module_update'] = 'Module Update';
$lang['username'] = 'Username';
$lang['changelog'] = 'Change Log';
$lang['check_update'] = 'Check Update';
$lang['database_upgrade_required'] = 'Database upgrade is required!';
$lang['update_content_1'] = 'You need to perform a database upgrade before proceeding. Your ';
$lang['update_content_2'] = '<strong>files version</strong> is ';
$lang['update_content_3'] = ' and <strong>database version</strong> is ';
$lang['update_content_4'] = 'Make sure that you have a backup of your database before performing an upgrade.';
$lang['update_content_5'] = 'This message may show if you uploaded files from a newer version downloaded from CodeCanyon to your existing installation or you used an auto-upgrade tool.';
$lang['upgrade_now'] = 'UPGRADE NOW';
$lang['module_updated_successfully'] = 'Module Updated Successfully';
$lang['create_support_ticket'] = 'Create Support Ticket';
$lang['support_ticket_content'] = 'Do you want custom services? Visit here and create your ticket';
// Update language line: Over

$lang['verify_webhook'] = 'Verify Webhook';
$lang['webhook_received_successfully'] = 'Webhook received successfully';
$lang['sending'] = 'Sending ...';
$lang['verify'] = 'Verify';

$lang['flows'] = "Flows";
$lang['flow_data_loaded'] = 'Flows loaded successfully';
$lang['load_flows'] = 'Load Flows';
$lang['flow_management'] = 'Flow Management';
$lang['flow_templates'] = 'Flow Templates';
$lang['flow_responses'] = 'Flow Responses';
$lang['receiver'] = 'Receiver';
$lang['submit_time'] = 'Submit time';
$lang['whatsapp_no'] = 'Whatsapp number';

$lang['marketing_automation'] = "Marketing Automation";
$lang['after_ticket_status_changed'] = 'Ticket Status Changed';
$lang['project_status_changed'] = 'Project Status Changed';
$lang['add'] = 'Add';
$lang['automation'] = 'Automation';

$lang['after_ticket_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when the ticket status changes.';
$lang['project_status_changed_subtext'] = 'Send the selected WhatsApp flow to the client when project status changes';

$lang['sender_phone'] = 'Phone number use for message sending';

$lang['section'] = 'Section';
$lang['text'] = 'Text';
$lang['sub_text'] = 'Sub Text';
$lang['submit_button_label'] = 'Submit Button Label';
$lang['option_5_bot_with_options'] = 'Option 5: Bot With Options';
$lang['choose_options'] = 'Choose Option';

$lang['flow_response'] = 'Flow Response';
$lang['not_allowed_to_view'] = 'Not allowed to view';
