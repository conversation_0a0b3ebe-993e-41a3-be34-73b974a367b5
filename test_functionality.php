<?php
/**
 * WhatsBot Functionality Test & Fix Script
 * This script tests and fixes common functionality issues
 */

echo "<h2>WhatsBot Functionality Test & Fix</h2>";

// Check if we're in the correct directory
if (!file_exists('whatsbot.php')) {
    die('<p style="color: red;">Error: Please run this script from the WhatsBot module directory.</p>');
}

// Try to include CodeIgniter
$ci_paths = [
    '../../index.php',
    '../../../index.php',
    '../../../../index.php'
];

$ci_loaded = false;
foreach ($ci_paths as $path) {
    if (file_exists($path)) {
        // Set up basic environment
        $_SERVER['REQUEST_METHOD'] = 'GET';
        $_SERVER['REQUEST_URI'] = '/admin';
        $_SERVER['HTTP_HOST'] = 'localhost';
        
        try {
            require_once $path;
            $ci_loaded = true;
            break;
        } catch (Exception $e) {
            continue;
        }
    }
}

if ($ci_loaded && function_exists('get_instance')) {
    echo "<p style='color: green;'>✓ CodeIgniter loaded successfully</p>";
    
    $CI = &get_instance();
    $module_name = 'whatsbot';
    
    // Test 1: Check if module is activated
    echo "<h3>Test 1: Module Activation Status</h3>";
    $module = $CI->app_modules->get($module_name);
    if ($module && $CI->app_modules->is_active($module_name)) {
        echo "<p style='color: green;'>✓ Module is activated</p>";
    } else {
        echo "<p style='color: red;'>✗ Module is not activated</p>";
    }
    
    // Test 2: Check activation options
    echo "<h3>Test 2: Activation Options</h3>";
    $verification_id = get_option($module_name.'_verification_id');
    $product_token = get_option($module_name.'_product_token');
    $last_verification = get_option($module_name.'_last_verification');
    $support_date = get_option($module_name.'_support_until_date');
    
    if (!empty($verification_id)) {
        echo "<p style='color: green;'>✓ Verification ID exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Verification ID missing - fixing...</p>";
        $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
        update_option($module_name.'_verification_id', base64_encode($fake_verification_id));
        echo "<p style='color: green;'>✓ Verification ID created</p>";
    }
    
    if (!empty($product_token)) {
        echo "<p style='color: green;'>✓ Product token exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Product token missing - fixing...</p>";
        $fake_token = base64_encode(json_encode([
            'item_id' => '53052338',
            'buyer' => 'admin',
            'purchase_code' => 'fake_purchase_code',
            'check_interval' => 86400
        ]));
        update_option($module_name.'_product_token', $fake_token);
        echo "<p style='color: green;'>✓ Product token created</p>";
    }
    
    if (!empty($last_verification)) {
        echo "<p style='color: green;'>✓ Last verification timestamp exists</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Last verification missing - fixing...</p>";
        update_option($module_name.'_last_verification', time());
        echo "<p style='color: green;'>✓ Last verification timestamp created</p>";
    }
    
    if (!empty($support_date)) {
        echo "<p style='color: green;'>✓ Support date exists: $support_date</p>";
    } else {
        echo "<p style='color: orange;'>⚠ Support date missing - fixing...</p>";
        update_option($module_name.'_support_until_date', date('Y-m-d', strtotime('+10 years')));
        echo "<p style='color: green;'>✓ Support date created</p>";
    }
    
    // Test 3: Check database tables
    echo "<h3>Test 3: Database Tables</h3>";
    $required_tables = [
        'wtc_templates',
        'wtc_flows', 
        'wtc_campaigns',
        'wtc_campaign_data',
        'wtc_bot',
        'wtc_interactions',
        'wtc_interaction_messages'
    ];
    
    foreach ($required_tables as $table) {
        $full_table_name = db_prefix() . $table;
        if ($CI->db->table_exists($full_table_name)) {
            echo "<p style='color: green;'>✓ Table $full_table_name exists</p>";
        } else {
            echo "<p style='color: red;'>✗ Table $full_table_name missing</p>";
        }
    }
    
    // Test 4: Check upload directories
    echo "<h3>Test 4: Upload Directories</h3>";
    $upload_dirs = [
        'uploads/whatsbot',
        'uploads/whatsbot/campaign',
        'uploads/whatsbot/template',
        'uploads/whatsbot/bot_files',
        'uploads/whatsbot/csv'
    ];
    
    foreach ($upload_dirs as $dir) {
        $full_path = FCPATH . $dir;
        if (is_dir($full_path)) {
            echo "<p style='color: green;'>✓ Directory $dir exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ Directory $dir missing - creating...</p>";
            if (mkdir($full_path, 0755, true)) {
                echo "<p style='color: green;'>✓ Directory $dir created</p>";
            } else {
                echo "<p style='color: red;'>✗ Failed to create directory $dir</p>";
            }
        }
    }
    
    // Test 5: Check license file
    echo "<h3>Test 5: License File</h3>";
    try {
        $CI->load->helper('whatsbot/whatsbot');
        $chatOptions = set_chat_header();
        $license_file = TEMP_FOLDER . $chatOptions['chat_content'] . '.lic';
        
        if (file_exists($license_file)) {
            echo "<p style='color: green;'>✓ License file exists</p>";
        } else {
            echo "<p style='color: orange;'>⚠ License file missing - creating...</p>";
            $content = 'activated_' . time();
            write_file($license_file, $content);
            echo "<p style='color: green;'>✓ License file created</p>";
        }
    } catch (Exception $e) {
        echo "<p style='color: orange;'>⚠ Could not check license file: " . $e->getMessage() . "</p>";
    }
    
    // Remove any heartbeat failures
    delete_option($module_name.'_heartbeat');
    echo "<p style='color: green;'>✓ Removed any heartbeat failures</p>";
    
    echo "<h3 style='color: green;'>✅ All Tests Completed!</h3>";
    echo "<p><strong>Next Steps:</strong></p>";
    echo "<ol>";
    echo "<li>Go to Admin → Modules and check if WhatsBot is activated</li>";
    echo "<li>If not activated, click 'Activate' - it should work without asking for Envato details</li>";
    echo "<li>Navigate to WhatsBot from the admin menu</li>";
    echo "<li>Test the main features: Templates, Campaigns, Chat, etc.</li>";
    echo "</ol>";
    
} else {
    echo "<p style='color: red;'>Could not load CodeIgniter. Please run this script from a web browser.</p>";
}

echo "<hr>";
echo "<p><em>Rebranded by Audience Target CRM - <a href='https://audiencetargetcrm.online/' target='_blank'>https://audiencetargetcrm.online/</a></em></p>";
?>
