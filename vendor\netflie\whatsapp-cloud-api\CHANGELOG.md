# Changelog

## 2.2.1 - 2024-08-28

### What's Changed

- Document caption is not required. Thanks @limsenkeat
- Implement CTA URL message (With text & Image support). Thanks @jfradj
- Add reaction to messages. Thanks @derrickobedgiu1
- Send Catalog Message. Thanks @derrickobedgiu1
- Add Send Location Request. Thanks @derrickobedgiu1
- Adding flow message support. Thanks @pravnyadv
- Add Multi Products. Thanks @derrickobedgiu1
- Add Single Products. Thanks @derrickobedgiu1
- Support for ctwa_clid. Thanks @jpsilvaa

## 2.2.0 - 2023-11-19

### What's Changed

- Updated default Graph version to v18
- Retrieve and update business profile. Thanks @winkelco
- Allow to reply messages. Thanks @johnflash4real
- Add Interactive buttons. Thanks @derrickobedgiu1 and @i<PERSON><PERSON><PERSON>
- Retrieve a batch of notifications from Webhook. Thanks @arneee
- Optional headers in interactive list messages. Thanks @horatiua
- Fix non nullable variables when empty ENV variables are defined

## 2.1.0 - 2023-08-12

### What's Changed

- Media filename and sha256 are now available on Webhook notification Media object. See PR #126

## 2.0.5 - 2023-06-24

### What's Changed

- Fix message notification initialization when context is empty. See PR #114

## 2.0.4 - 2023-06-01

### What's Changed

- Fix Webhook error with new base pricing category conversations

## 2.0.3 - 2023-03-29

### What's Changed

- Fix forwaded messages without ID in the message context. Issue https://github.com/netflie/whatsapp-cloud-api/issues/94

## 2.0.2 - 2023-02-06

### What's Changed

- Fix some minor issues

## 2.0.1 - 2023-01-29

### What's Changed

New features supported:

- Fix wrong documented conversation type value when a user initiated a conversation

## 2.0.0 - 2023-01-29

### What's Changed

New features supported:

- Upload media resources to WhatsApp servers
- Download media resources from WhatsApp servers
- Mark messages as read
- Webhook verification
- Webhook notifications

## 1.4.1 - 2023-01-28

### What's Changed

- API Graph v15.0 as default

## 1.4.0 - 2022-12-09

### 1.4.0 - 2022-12-23

#### What's Changed

- Support Interactive Lists by @robertripoll in https://github.com/netflie/whatsapp-cloud-api/pull/58
- Execute workflow for PHP 7.4, 8.0 & 8.1 versions

## 1.3.1 - 2022-11-18

### What's Changed

- Configured PHPStan github action
- Fix some minor bugs reported by PHPStan analysis
- Fix Response namespace in ResponseException class

## 1.3.0 - 2022-09-30

### What's Changed

- Support WhatsApp ID (waid) for Contacts
- Improved README

## 1.2.0 - 2022-06-02

### What's Changed

- Suuport for PHP 8.0 & 8.1 versions

## 1.1.0 - 2022-05-29

### What's Changed

- Support for WhatsApp Contact Messages
- Support for WhatsApp Location Messages
- Support for WhatsApp Sticker media object
- Support for WhatsApp Video media object
- Support for WhatsApp Image media object
- Support for WhatsApp Audio media object
- Support for WhatsApp Templates with paramters
- Support for WhatsApp Document media object
- Define Integration and Unit PHPUnit testsuites
- Ignore .php-cs-fixer.dist.php file

## 1.0.1 - 2022-05-27

### What's Changed

- Ignore composer.lock file

## 1.0.0 - 2022-05-24

### What's Changed

- Everything, initial release
