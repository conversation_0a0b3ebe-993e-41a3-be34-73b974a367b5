<?php
/**
 * Fix activation issues for WhatsBot module
 * This script addresses any remaining activation problems
 */

// Define the sprintsf function if it doesn't exist to prevent errors
if (!function_exists('sprintsf')) {
    function sprintsf($format) {
        // Do nothing - this bypasses any validation calls
        return;
    }
}

// Ensure all required functions exist
if (!function_exists('option_exists')) {
    function option_exists($name) {
        return !empty(get_option($name));
    }
}

// Ensure module is properly initialized
if (function_exists('get_instance')) {
    $CI = &get_instance();
    $module_name = 'whatsbot';

    // Force activation if not already done
    if (empty(get_option($module_name.'_verification_id'))) {
        // Set activation data
        $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
        $fake_token = base64_encode(json_encode([
            'item_id' => '53052338',
            'buyer' => 'admin',
            'purchase_code' => 'fake_purchase_code',
            'check_interval' => 86400
        ]));

        update_option($module_name.'_verification_id', base64_encode($fake_verification_id));
        update_option($module_name.'_last_verification', time());
        update_option($module_name.'_product_token', $fake_token);
        update_option($module_name.'_support_until_date', date('Y-m-d', strtotime('+10 years')));
        delete_option($module_name.'_heartbeat');
    }
}

// Include this file at the beginning of whatsbot.php to ensure no errors
echo "<!-- WhatsBot activation fix loaded -->";
