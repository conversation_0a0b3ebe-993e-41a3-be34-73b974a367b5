<?php

namespace modules\whatsbot\core;

require_once __DIR__.'/../third_party/node.php';
require_once __DIR__.'/../vendor/autoload.php';

use Corbital\Rightful\Classes\CTLExternalAPI as Whatsbot_CTLExternalAPI;

class Apiinit {
    public static function the_da_vinci_code($module_name) {
        // Always return true to bypass license validation
        return true;
    }

    public static function ease_of_mind($module_name) {
        // Always pass the function existence check
        return true;
    }

    public static function activate($module)
    {
        if (!option_exists($module['system_name'].'_verification_id') && empty(get_option($module['system_name'].'_verification_id'))) {
            // Auto-activate without requiring Envato details
            $module_name = $module['system_name'];
            $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
            $fake_token = base64_encode(json_encode([
                'item_id' => basename($module['headers']['uri']),
                'buyer' => 'admin',
                'purchase_code' => 'fake_purchase_code',
                'check_interval' => 86400 // 24 hours
            ]));

            // Set the required options to simulate successful activation
            update_option($module_name.'_verification_id', base64_encode($fake_verification_id));
            update_option($module_name.'_last_verification', time());
            update_option($module_name.'_product_token', $fake_token);
            update_option($module_name.'_support_until_date', date('Y-m-d', strtotime('+1 year')));

            // Create license file
            get_instance()->load->helper('whatsbot/whatsbot');
            $chatOptions = set_chat_header();
            $content = (!empty($chatOptions['chat_header']) && !empty($chatOptions['chat_footer'])) ? hash_hmac('sha512', $chatOptions['chat_header'], $chatOptions['chat_footer']) : '';
            write_file(TEMP_FOLDER . $chatOptions['chat_content'] . '.lic', $content);

            return; // Skip the activation form
        }
    }

    public static function pre_validate($module_name, $code='', $username='')
    {
        // Auto-activate without validation
        get_instance()->load->helper('whatsbot/whatsbot');
        $module = get_instance()->app_modules->get($module_name);

        // Create fake verification data
        $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
        $fake_token = base64_encode(json_encode([
            'item_id' => basename($module['headers']['uri']),
            'buyer' => 'admin',
            'purchase_code' => 'fake_purchase_code',
            'check_interval' => 86400 // 24 hours
        ]));

        // Set the required options to simulate successful activation
        update_option($module_name.'_verification_id', base64_encode($fake_verification_id));
        update_option($module_name.'_last_verification', time());
        update_option($module_name.'_product_token', $fake_token);
        update_option($module_name.'_support_until_date', date('Y-m-d', strtotime('+10 years')));
        delete_option($module_name.'_heartbeat');

        // Create license file
        $chatOptions = set_chat_header();
        $content = (!empty($chatOptions['chat_header']) && !empty($chatOptions['chat_footer'])) ? hash_hmac('sha512', $chatOptions['chat_header'], $chatOptions['chat_footer']) : 'activated';
        write_file(TEMP_FOLDER . $chatOptions['chat_content'] . '.lic', $content);

        return ['status' => true];
    }
}
