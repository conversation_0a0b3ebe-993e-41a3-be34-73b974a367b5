name: PHP

on:
  - push
  - pull_request

jobs:
  tests:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: true
      matrix:
        php: [7.4, 8.0, 8.1]

    name: Tests on PHP ${{ matrix.php }} - ${{ matrix.stability }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php }}
          tools: composer:v2, phpstan
          coverage: none

      - name: Install dependencies
        run: composer install --prefer-dist --no-interaction --no-progress

      - name: Lint composer.json
        run: composer validate

      - name: Execute tests
        run: composer unit-test

      - name: Run PHPStan
        run: phpstan analyse src --level=1