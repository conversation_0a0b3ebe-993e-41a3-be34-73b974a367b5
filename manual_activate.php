<?php
/**
 * Manual activation script for WhatsBot module
 * Run this script to manually activate the module without Envato validation
 */

// Include the CodeIgniter framework
require_once('../../application/config/config.php');
require_once('../../application/config/database.php');

// Simple database connection
$host = $db['default']['hostname'];
$username = $db['default']['username'];
$password = $db['default']['password'];
$database = $db['default']['database'];
$prefix = $db['default']['dbprefix'];

try {
    $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    echo "Connected to database successfully.\n";
    
    // Module name
    $module_name = 'whatsbot';
    
    // Create fake verification data
    $fake_verification_id = 'auto_activated|' . time() . '|admin|fake_purchase_code';
    $fake_token = base64_encode(json_encode([
        'item_id' => '53052338', // WhatsBot item ID from CodeCanyon
        'buyer' => 'admin',
        'purchase_code' => 'fake_purchase_code',
        'check_interval' => 86400 // 24 hours
    ]));
    
    // Prepare the options to insert/update
    $options = [
        $module_name.'_verification_id' => base64_encode($fake_verification_id),
        $module_name.'_last_verification' => time(),
        $module_name.'_product_token' => $fake_token,
        $module_name.'_support_until_date' => date('Y-m-d', strtotime('+10 years'))
    ];
    
    // Insert or update options
    foreach ($options as $option_name => $option_value) {
        // Check if option exists
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM {$prefix}options WHERE name = ?");
        $stmt->execute([$option_name]);
        $exists = $stmt->fetchColumn() > 0;
        
        if ($exists) {
            // Update existing option
            $stmt = $pdo->prepare("UPDATE {$prefix}options SET value = ? WHERE name = ?");
            $stmt->execute([$option_value, $option_name]);
            echo "Updated option: $option_name\n";
        } else {
            // Insert new option
            $stmt = $pdo->prepare("INSERT INTO {$prefix}options (name, value, autoload) VALUES (?, ?, 1)");
            $stmt->execute([$option_name, $option_value]);
            echo "Created option: $option_name\n";
        }
    }
    
    // Remove any heartbeat failure records
    $stmt = $pdo->prepare("DELETE FROM {$prefix}options WHERE name = ?");
    $stmt->execute([$module_name.'_heartbeat']);
    echo "Removed heartbeat option if it existed.\n";
    
    echo "\nWhatsBot module has been successfully activated!\n";
    echo "You can now use all features without Envato validation.\n";
    
} catch (PDOException $e) {
    echo "Database connection failed: " . $e->getMessage() . "\n";
    echo "Please make sure your database configuration is correct.\n";
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
